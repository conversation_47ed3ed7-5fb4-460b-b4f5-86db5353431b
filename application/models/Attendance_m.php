<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Attendance_m extends MY_Model
{
    protected string $_table_name = 'attendance';
    function __construct() {
        parent::__construct();
    }

    public function get_attendance_data($date){
        $attendance = parent::get(['date' => $date])->result_array();
        $attendance_data = [];

        foreach ($attendance as $item){
            $attendance_data[$item['user_id']]['attendance'] = $item['attendance'];
            $attendance_data[$item['user_id']]['remarks'] = $item['remarks'];
            $attendance_data[$item['user_id']]['off_date'] = $item['off_date'];
        }
        return $attendance_data;
    }

    public function get_attendance_data_overview($from_date, $to_date){
        $attendance = parent::get(['date>=' => $from_date, 'date<=' => $to_date])->result_array();
        $attendance_data = [];

        foreach ($attendance as $item){
            $attendance_data[$item['user_id']][$item['date']]['attendance'] = $item['attendance'];
            $attendance_data[$item['user_id']][$item['date']]['remarks'] = $item['remarks'];
            $attendance_data[$item['user_id']][$item['date']]['off_date'] = $item['off_date'];
        }
        return $attendance_data;
    }

    // get employee attendance data
    public function get_employee_attendance_data($user_id, $from_date, $to_date){
        $attendance = parent::get(['date>=' => $from_date, 'date<=' => $to_date, 'user_id' => $user_id])->result_array();
        $attendance_data = [];

        foreach ($attendance as $item){
            $attendance_data[$item['date']]['attendance'] = $item['attendance'] ?? '';
            $attendance_data[$item['date']]['remarks'] = $item['remarks'] ?? '';
            $attendance_data[$item['date']]['off_date'] = $item['off_date'] ?? '';
        }
        return $attendance_data;
    }


    public function overview_employee_report($user_id, $start_date, $end_date){
        $attendance = parent::get(['date>=' => $start_date, 'date<=' => $end_date, 'user_id' => $user_id])->result_array();
        $attendance_data = [];

        foreach ($attendance as $item){
            $date = DateTime::createFromFormat('Y-m-d', $item['date']);
            $month = $date->format('Y-m');
            $day = $date->format('d');

            $attendance_data[$month]['data'][$day]['date'] = $item['date']?? '';
            $attendance_data[$month]['data'][$day]['attendance'] = $item['attendance'] ?? '';
            $attendance_data[$month]['data'][$day]['remarks'] = $item['remarks'] ?? '';
            $attendance_data[$month]['data'][$day]['off_date'] = $item['off_date'] ?? '';
        }
        return $attendance_data;
    }

    // Calculates the number of unique working days within a given date range.
    public function get_working_days($start_date, $end_date){
        $this->db->distinct('date');
        $attendance_days = parent::get(
            ['date>=' => $start_date, 'date<=' => $end_date],
            ['date']
        )->result_array();
        return count($attendance_days) ?? 0;
    }

    // get working days within a given date range.
    public function get_office_days($start_date, $end_date){
        $this->db->distinct('date');
        $attendance_days = parent::get(
            ['date>=' => $start_date, 'date<=' => $end_date],
            ['date']
        )->result_array();
        return array_column($attendance_days, 'date');
    }

    // Get attendance status report for date range
    public function get_attendance_status_report($start_date, $end_date) {
        // Get all users (both active and inactive)
        $this->load->model('users_m');
        $users = $this->users_m->get([
            'is_employee' => 1
        ], ['id', 'name', 'employee_status'], ['key' => 'name', 'direction' => 'ASC'])->result_array();

        // Get attendance data for the date range
        $attendance_data = parent::get([
            'date >=' => $start_date,
            'date <=' => $end_date
        ])->result_array();

        // Process attendance data
        $attendance_array = [];
        foreach ($attendance_data as $attendance) {
            $attendance_array[$attendance['user_id']][$attendance['date']] = [
                'attendance' => $attendance['attendance'],
                'remarks' => $attendance['remarks']
            ];
        }

        // Generate date array
        $date_array = get_date_array($start_date, $end_date);

        // Build attendance report
        $attendance_report = [];
        foreach ($users as $user) {
            $user_id = $user['id'];

            // Check if user has any attendance records in the date range
            $has_attendance = isset($attendance_array[$user_id]);

            // Include user if they are active OR have attendance records in the date range
            if ($user['employee_status'] == 1 || $has_attendance) {
                $attendance_report[$user_id] = [
                    'user_info' => $user,
                    'daily_attendance' => [],
                    'summary' => [
                        'P' => 0,   // Present
                        'A' => 0,   // Absent
                        'WH' => 0,  // Work From Home
                        'OF' => 0,  // Off
                        'OD' => 0,  // On Duty
                        'HD' => 0   // Half Day
                    ]
                ];

                foreach ($date_array as $date) {
                    $attendance_info = $attendance_array[$user_id][$date] ?? null;
                    $attendance_status = $attendance_info['attendance'] ?? null;
                    $remarks = $attendance_info['remarks'] ?? '';

                    $attendance_report[$user_id]['daily_attendance'][$date] = [
                        'attendance' => $attendance_status,
                        'remarks' => $remarks
                    ];

                    // Count attendance types
                    if ($attendance_status) {
                        $attendance_report[$user_id]['summary'][$attendance_status]++;
                    }
                }
            }
        }

        return $attendance_report;
    }

    // Get late coming report for date range
    public function get_late_coming_report($start_date, $end_date) {
        // Get all users (both active and inactive)
        $this->load->model('users_m');
        $users = $this->users_m->get([
            'is_employee' => 1
        ], ['id', 'name', 'employee_status'], ['key' => 'name', 'direction' => 'ASC'])->result_array();

        // Get morning punch-in data from time_log table
        $this->load->model('time_log_m');
        $this->db->select('user_id, log_date, log_time');
        $this->db->where('log_type_id', 1); // Morning punch-in
        $this->db->where('type', 1); // IN punch
        $this->db->where('log_date >=', $start_date);
        $this->db->where('log_date <=', $end_date);
        $this->db->order_by('log_date', 'ASC');
        $time_log_data = $this->db->get('time_log')->result_array();

        // Process time log data
        $punch_in_array = [];
        foreach ($time_log_data as $log) {
            $punch_in_array[$log['user_id']][$log['log_date']] = $log['log_time'];
        }

        // Generate date array
        $date_array = get_date_array($start_date, $end_date);

        // Build late coming report
        $late_coming_report = [];
        foreach ($users as $user) {
            $user_id = $user['id'];

            // Check if user has any punch-in records in the date range
            $has_punch_in = isset($punch_in_array[$user_id]);

            // Include user if they are active OR have punch-in records in the date range
            if ($user['employee_status'] == 1 || $has_punch_in) {
                $late_coming_report[$user_id] = [
                    'user_info' => $user,
                    'daily_punch_in' => [],
                    'summary' => [
                        'on_time' => 0,      // ≤ 9:30 AM
                        'warning' => 0,      // 9:31-9:35 AM
                        'danger' => 0,       // > 9:35 AM
                        'total_late' => 0,   // All after 9:30 AM
                        'no_record' => 0     // No punch-in record
                    ]
                ];

                // Process each date
                foreach ($date_array as $date) {
                    $punch_in_time = isset($punch_in_array[$user_id][$date]) ? $punch_in_array[$user_id][$date] : null;

                    if ($punch_in_time && $punch_in_time != '00:00:00') {
                        // Determine status based on punch-in time
                        $punch_time_obj = DateTime::createFromFormat('H:i:s', $punch_in_time);
                        $cutoff_930 = DateTime::createFromFormat('H:i:s', '09:30:00');
                        $cutoff_935 = DateTime::createFromFormat('H:i:s', '09:35:00');

                        if ($punch_time_obj <= $cutoff_930) {
                            $status = 'on_time';
                            $late_coming_report[$user_id]['summary']['on_time']++;
                        } elseif ($punch_time_obj <= $cutoff_935) {
                            $status = 'warning';
                            $late_coming_report[$user_id]['summary']['warning']++;
                            $late_coming_report[$user_id]['summary']['total_late']++;
                        } else {
                            $status = 'danger';
                            $late_coming_report[$user_id]['summary']['danger']++;
                            $late_coming_report[$user_id]['summary']['total_late']++;
                        }

                        $late_coming_report[$user_id]['daily_punch_in'][$date] = [
                            'punch_time' => $punch_in_time,
                            'status' => $status,
                            'formatted_time' => $punch_time_obj->format('g:i A')
                        ];
                    } else {
                        // No punch-in record
                        $late_coming_report[$user_id]['daily_punch_in'][$date] = [
                            'punch_time' => null,
                            'status' => 'no_record',
                            'formatted_time' => 'N/A'
                        ];
                        $late_coming_report[$user_id]['summary']['no_record']++;
                    }
                }
            }
        }

        return $late_coming_report;
    }
}
