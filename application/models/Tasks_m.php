<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Tasks_m extends MY_Model
{
    protected string $_table_name = 'tasks';
    function __construct() {
        parent::__construct();
    }

    // get status wise count
    public function get_count($start_date, $end_date, $where = [], $project_id = 0, $user_id = 0){
        if(isset($where['task_status']) && $where['task_status'] != 'pending' && $where['task_status'] != 'assigned' && $where['task_status'] != 'on_hold'){
            if (!empty($start_date)) {
                $where['date(tasks.due_date) >='] = $start_date;
            }
            if (!empty($end_date)) {
                $where['date(tasks.due_date) <='] = $end_date;
            }
        }
        
        if ($project_id > 0){
            $where['tasks.project_id'] = $project_id;
        }
        if ($user_id > 0){
            $where['tasks.user_id'] = $user_id;
        }
        $count = parent::get($where, ['count(tasks.id) as item_count'])->row()->item_count ?? 0;
        // echo $this->db->last_query();
        return $count;
    }


    // get pending tasks
    public function get_user_pending_count(){
        $this->db->where('user_id', get_user_id());
        if (is_technical_support()){
            $this->db->where_in('task_status', ['assigned', 'testing']);
        }else{
            $this->db->where_in('task_status', ['assigned']);
        }
        return parent::get(null, ['count(tasks.id) as item_count'])->row()->item_count ?? 0;
    }

    /**
     * Get tasks for DataTable with server-side processing
     */
    public function get_tasks_datatable($request_data) {
        // Build the base query
        $this->db->select('
            t.*,
            p.title as project_title,
            u.name as assigned_user_name,
            c.name as created_by_name
        ');
        $this->db->from('tasks t');
        $this->db->join('projects p', 't.project_id = p.id', 'left');
        $this->db->join('users u', 't.user_id = u.id', 'left');
        $this->db->join('users c', 't.created_by = c.id', 'left');

        // Apply filters
        if (!empty($request_data['filter_status']) && $request_data['filter_status'] !== 'all') {
            $this->db->where('t.task_status', $request_data['filter_status']);
        }
        if (!empty($request_data['filter_priority']) && $request_data['filter_priority'] !== 'all') {
            $this->db->where('t.task_priority', $request_data['filter_priority']);
        }
        if (!empty($request_data['filter_project']) && $request_data['filter_project'] !== 'all') {
            $this->db->where('t.project_id', $request_data['filter_project']);
        }
        if (!empty($request_data['filter_assigned']) && $request_data['filter_assigned'] !== 'all') {
            $this->db->where('t.user_id', $request_data['filter_assigned']);
        }
        if (!empty($request_data['filter_date_from'])) {
            $this->db->where('DATE(t.due_date) >=', $request_data['filter_date_from']);
        }
        if (!empty($request_data['filter_date_to'])) {
            $this->db->where('DATE(t.due_date) <=', $request_data['filter_date_to']);
        }

        // Get total count before applying limit
        $total_records = $this->db->count_all_results('', false);

        // Apply search
        if (!empty($request_data['search']['value'])) {
            $search_value = $request_data['search']['value'];
            $this->db->group_start();
            $this->db->like('t.title', $search_value);
            $this->db->or_like('t.id', $search_value);
            $this->db->or_like('p.title', $search_value);
            $this->db->or_like('u.name', $search_value);
            $this->db->or_like('c.name', $search_value);
            $this->db->group_end();
        }

        // Get filtered count
        $filtered_records = $this->db->count_all_results('', false);

        // Apply ordering
        if (!empty($request_data['order'])) {
            $columns = ['t.id', 't.title', 't.task_type', 't.task_priority', 't.task_status', 'u.name', 't.due_date', 'c.name'];
            $order_column = $columns[$request_data['order'][0]['column']] ?? 't.id';
            $order_direction = $request_data['order'][0]['dir'] ?? 'desc';
            $this->db->order_by($order_column, $order_direction);
        } else {
            $this->db->order_by('t.id', 'desc');
        }

        // Apply pagination
        if (isset($request_data['start']) && isset($request_data['length'])) {
            $this->db->limit($request_data['length'], $request_data['start']);
        }

        // Execute query
        $data = $this->db->get()->result_array();

        return [
            'recordsTotal' => $total_records,
            'recordsFiltered' => $filtered_records,
            'data' => $data
        ];
    }

}
