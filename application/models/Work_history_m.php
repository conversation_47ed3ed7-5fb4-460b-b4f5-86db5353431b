<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: PMS
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: https://trogonmedia.com
 */

class Work_history_m extends MY_Model
{
	protected string $_table_name = 'work_history';
	
	function __construct() {
		parent::__construct();
	}

	// Get ticket history with user details
	public function get_ticket_history($ticket_id) {
		$this->db->select('
			wh.*,
			u.name as user_name,
			u.photo as user_photo
		');
		$this->db->from($this->_table_name . ' wh');
		$this->db->join('users u', 'wh.user_id = u.id', 'left');
		$this->db->where('wh.item_type', 'ticket');
		$this->db->where('wh.item_id', $ticket_id);
		$this->db->order_by('wh.created_at', 'DESC'); // Latest first

		return $this->db->get()->result_array();
	}

	// Get task history with user details
	public function get_task_history($task_id) {
		$this->db->select('
			wh.*,
			u.name as user_name,
			u.photo as user_photo
		');
		$this->db->from($this->_table_name . ' wh');
		$this->db->join('users u', 'wh.user_id = u.id', 'left');
		$this->db->where('wh.item_type', 'task');
		$this->db->where('wh.item_id', $task_id);
		$this->db->order_by('wh.created_at', 'DESC'); // Latest first

		return $this->db->get()->result_array();
	}

	// Get recent ticket activities
	public function get_recent_activities($limit = 10) {
		$this->db->select('
			wh.*, 
			u.name as user_name,
			t.title as ticket_title
		');
		$this->db->from($this->_table_name . ' wh');
		$this->db->join('users u', 'wh.user_id = u.id', 'left');
		$this->db->join('tickets t', 'wh.item_id = t.id AND wh.item_type = "ticket"', 'left');
		$this->db->where('wh.item_type', 'ticket');
		$this->db->order_by('wh.created_at', 'DESC');
		$this->db->limit($limit);
		
		return $this->db->get()->result_array();
	}

	// Get user activity on tickets
	public function get_user_ticket_activities($user_id, $limit = 20) {
		$this->db->select('
			wh.*, 
			t.title as ticket_title
		');
		$this->db->from($this->_table_name . ' wh');
		$this->db->join('tickets t', 'wh.item_id = t.id AND wh.item_type = "ticket"', 'left');
		$this->db->where('wh.user_id', $user_id);
		$this->db->where('wh.item_type', 'ticket');
		$this->db->order_by('wh.created_at', 'DESC');
		$this->db->limit($limit);
		
		return $this->db->get()->result_array();
	}

	// Insert ticket history entry
	public function insert_ticket_history($data) {
		$work_history_data = [
			'user_id' => $data['user_id'],
			'item_type' => 'ticket',
			'item_id' => $data['ticket_id'],
			'remarks' => $data['remarks'],
			'created_at' => $data['created_at'] ?? date('Y-m-d H:i:s')
		];

		// Add start_time and end_time if provided
		if (isset($data['start_time'])) {
			$work_history_data['start_time'] = $data['start_time'];
		}
		if (isset($data['end_time'])) {
			$work_history_data['end_time'] = $data['end_time'];
		}

		// Calculate duration if both start_time and end_time are provided
		if (isset($data['start_time']) && isset($data['end_time'])) {
			$start = new DateTime($data['start_time']);
			$end = new DateTime($data['end_time']);
			$work_history_data['duration'] = $end->getTimestamp() - $start->getTimestamp();
		}

		return $this->insert($work_history_data);
	}

	// Insert task history entry
	public function insert_task_history($data) {
		$work_history_data = [
			'user_id' => $data['user_id'],
			'item_type' => 'task',
			'item_id' => $data['task_id'],
			'remarks' => $data['remarks'] ?? null,
			'created_at' => $data['created_at'] ?? date('Y-m-d H:i:s')
		];

		// Add start_time and end_time if provided
		if (isset($data['start_time'])) {
			$work_history_data['start_time'] = $data['start_time'];
		}
		if (isset($data['end_time'])) {
			$work_history_data['end_time'] = $data['end_time'];
		}

		// Calculate duration if both start_time and end_time are provided
		if (isset($data['start_time']) && isset($data['end_time'])) {
			$start = new DateTime($data['start_time']);
			$end = new DateTime($data['end_time']);
			$work_history_data['duration'] = ($end->getTimestamp() - $start->getTimestamp()) / 60; // Convert to minutes
		}

		return $this->insert($work_history_data);
	}

	// Check if task is already assigned
	public function is_task_assigned($task_id) {
		return $this->get(['item_type' => 'task', 'item_id' => $task_id])->num_rows() > 0;
	}

	// Get employee total job duration
	public function get_employee_total_job_duration($start_date, $end_date, $user_id) {
		$this->db->select('duration');
		$this->db->where('DATE(start_time) >=', $start_date);
		$this->db->where('DATE(start_time) <=', $end_date);
		$this->db->where('user_id', $user_id);
		$this->db->where('item_type', 'task');
		$task_status = $this->get()->result_array();
		$duration_array = array_column($task_status, 'duration');
		$total_duration = array_sum($duration_array);
		return $total_duration * 60; // Convert minutes to seconds for compatibility
	}

	// Get employee project report
	public function get_employee_project_report($start_date, $end_date, $user_id, $is_detailed = 0){
		$this->db->select('wh.duration, wh.start_time, wh.end_time, wh.remarks, t.project_id, t.title, wh.created_at');
		$this->db->from($this->_table_name . ' wh');
		$this->db->join('tasks t', 't.id = wh.item_id');
		$this->db->where('wh.item_type', 'task');
		$this->db->where('DATE(wh.start_time) >=', $start_date);
		$this->db->where('DATE(wh.start_time) <=', $end_date);
		$this->db->where('wh.user_id', $user_id);
		$this->db->where('wh.duration <', 360); // Less than 6 hours in minutes
		$task_status = $this->db->get()->result_array();
		
		$time_taken_array = [];
		$total_time_taken = [];
		$monthly_array = [];

		foreach ($task_status as $task) {
			$time_taken_array[$task['project_id']][] = $task['duration'] * 60; // Convert to seconds
			$total_time_taken[] = $task['duration'] * 60;
			$month = DateTime::createFromFormat('Y-m-d H:i:s', $task['created_at'])->format('m-Y');
			$monthly_array[$month][$task['project_id']][] = $task;
		}

		$total_time_taken = array_sum($total_time_taken);
		$project_time_taken = [];
		$projects = $this->projects_m->get(null, ['id', 'title', 'project_type'])->result_array();
		$projects = array_column($projects, 'title', 'id');
		foreach ($time_taken_array as $project_id => $time_taken_arr) {
			$start_time = '';
			$end_time = '';
			if(!empty($task['start_time']) && !empty($task['end_time'])){
				$start_time = DateTime::createFromFormat('Y-m-d H:i:s', $task['start_time'])->format('g:i a');
				$end_time = DateTime::createFromFormat('Y-m-d H:i:s', $task['end_time'])->format('g:i a');
			}

			if (count($time_taken_arr)){
				$project_time_taken[] = [
					'project_id' => $project_id,
					'project_name' => $projects[$project_id],
					'duration_seconds' => array_sum($time_taken_arr),
					'percentage' => array_sum($time_taken_arr)/$total_time_taken*100,
					'duration' => get_time_from_seconds(array_sum($time_taken_arr)),
					'start_time' => $start_time,
					'end_time' => $end_time
				];
			}else{
				$project_time_taken[] = [
					'project_id' => $project_id,
					'project_name' => $projects[$project_id],
					'duration_seconds' => 0,
					'percentage' => 0,
					'duration' => '',
					'start_time' => '',
					'end_time' => ''
				];
			}
		}
		usort($project_time_taken, function($a, $b) {
			return $b['duration_seconds'] <=> $a['duration_seconds'];
		});
		return ['monthly_report' => $monthly_array, 'project_wise_report' => $project_time_taken];
	}

	// Get employee total job duration performance
	public function get_employee_total_job_duration_performance($start_date, $end_date, $user_id) {
		$this->db->select('duration');
		$this->db->where('DATE(start_time) >=', $start_date);
		$this->db->where('DATE(start_time) <=', $end_date);
		$this->db->where('user_id', $user_id);
		$this->db->where('item_type', 'task');
		$this->db->where('duration <', 300); // Less than 5 hours in minutes
		$task_status = $this->get()->result_array();

		$duration_array = array();

		foreach ($task_status as $task) {
			$job_date_8_05_pm = strtotime(date('Y-m-d', strtotime($task['start_time'])) . ' 21:05:00');
			$updated_on_time = strtotime($task['updated_at']);

			if ($updated_on_time <= $job_date_8_05_pm) {
				$duration_array[] = $task['duration'] * 60; // Convert to seconds
			}else{
				$duration_array[] = $task['duration'] * 60; // Convert to seconds
			}
		}

		$total_duration = array_sum($duration_array);
		return $total_duration;
	}

	// Get employee total job duration performance OT
	public function get_employee_total_job_duration_performance_ot($start_date, $end_date, $user_id) {
		$this->db->select('duration');
		$this->db->where('DATE(start_time) >=', $start_date);
		$this->db->where('DATE(start_time) <=', $end_date);
		$this->db->where('user_id', $user_id);
		$this->db->where('item_type', 'task');
		$this->db->where('TIME(end_time) >=', '18:30:00');
		$this->db->where('duration <', 720); // Less than 12 hours in minutes
		$task_status = $this->get()->result_array();
		$duration_array = array_column($task_status, 'duration');
		$total_duration = array_sum($duration_array) * 60; // Convert to seconds
		return $total_duration;
	}

	// Check for task period overlap
	public function is_task_period_overlap($user_id, $job_date, $start_time, $end_time) {
		$this->db->select('COUNT(*) as overlap_count');
		$this->db->from($this->_table_name);
		$this->db->where('user_id', $user_id);
		$this->db->where('item_type', 'task');
		$this->db->where('DATE(start_time)', $job_date);
		$this->db->group_start();
		$this->db->where("('$start_time' BETWEEN TIME(start_time) AND TIME(end_time))", NULL, FALSE);
		$this->db->or_where("('$end_time' BETWEEN TIME(start_time) AND TIME(end_time))", NULL, FALSE);
		$this->db->or_where("(TIME(start_time) BETWEEN '$start_time' AND '$end_time')", NULL, FALSE);
		$this->db->or_where("(TIME(end_time) BETWEEN '$start_time' AND '$end_time')", NULL, FALSE);
		$this->db->group_end();

		$query = $this->db->get();
		$result = $query->row();

		return $result->overlap_count > 0;
	}

	// get task report
	public function get_work_report($start_date, $end_date, $user_id){
		$this->db->select('wh.item_id as task_id, wh.duration, wh.start_time, wh.end_time, wh.remarks, t.project_id, t.title as task_title');
		$this->db->from($this->_table_name . ' wh');
		$this->db->join('tasks t', 't.id = wh.item_id');
		$this->db->where('wh.item_type', 'task');
		$this->db->where('DATE(wh.start_time) >=', $start_date);
		$this->db->where('DATE(wh.start_time) <=', $end_date);
		$this->db->where('wh.user_id', $user_id);
		$this->db->where('wh.duration <', 360); // Less than 6 hours in minutes
		$task_status = $this->db->get()->result_array();
		return $task_status;
	}
}
