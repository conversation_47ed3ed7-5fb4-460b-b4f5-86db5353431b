<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Projects_m extends MY_Model
{
    protected string $_table_name = 'projects';
    function __construct() {
        parent::__construct();
    }

    public function get_project_list($filters = []) {
        $this->load->model('tasks_m');
        
        // Apply filters
        if (!empty($filters['is_delivered'])) {
            if ($filters['is_delivered'] == 'delivered') {
                $this->db->where('is_delivered', 1);
            } elseif ($filters['is_delivered'] == 'not_delivered') {
                $this->db->where('is_delivered', 0);
            }
        }
        
        if (!empty($filters['tester_id'])) {
            $this->db->where('tester_id', $filters['tester_id']);
        }
        
        $this->db->order_by('id', 'desc');
        $projects = parent::get()->result_array();
        
        foreach ($projects as $key => $project){
             // get total number of tasks
            $total_tasks = $this->db->get_where('tasks', ['task_status!=' => 'on_hold', 'project_id' => $project['id']])->num_rows();

            // get total completed or testing
            $this->db->where_in('task_status', ['completed', 'testing']);
            $this->db->where('project_id', $project['id']);
            $completed_tasks = $this->db->get('tasks')->num_rows();

            // pending tasks
            $pending_tasks = $this->db->get_where('tasks', ['task_status' => 'pending', 'project_id' => $project['id']])->num_rows();

            // assigned tasks
            $assigned_tasks = $this->db->get_where('tasks', ['task_status' => 'assigned', 'project_id' => $project['id']])->num_rows();

            if ($total_tasks > 0){
                $progress = ($completed_tasks/$total_tasks)*100;
            }else{
                $progress = 0;
            }

            $projects[$key]['tasks']['total'] = $total_tasks;
            $projects[$key]['tasks']['pending'] = $pending_tasks;
            $projects[$key]['tasks']['assigned'] = $assigned_tasks;
            $projects[$key]['tasks']['completed'] = $completed_tasks;
            $projects[$key]['tasks']['progress'] = number_format($progress);

            // Get ticket statistics
            $total_tickets = $this->db->get_where('tickets', ['status!=' => 'on_hold', 'project_id' => $project['id']])->num_rows();

            // get total closed tickets
            $closed_tickets = $this->db->get_where('tickets', ['status' => 'closed', 'project_id' => $project['id']])->num_rows();

            // pending tickets (new + re_open)
            $this->db->where_in('status', ['new', 're_open']);
            $this->db->where('project_id', $project['id']);
            $pending_tickets = $this->db->get('tickets')->num_rows();

            // assigned tickets
            $assigned_tickets = $this->db->get_where('tickets', ['status' => 'assigned', 'project_id' => $project['id']])->num_rows();

            if ($total_tickets > 0){
                $ticket_progress = ($closed_tickets/$total_tickets)*100;
            } else {
                $ticket_progress = 0;
            }

            $projects[$key]['tickets']['total'] = $total_tickets;
            $projects[$key]['tickets']['pending'] = $pending_tickets;
            $projects[$key]['tickets']['assigned'] = $assigned_tickets;
            $projects[$key]['tickets']['closed'] = $closed_tickets;
            $projects[$key]['tickets']['progress'] = number_format($ticket_progress);
        }

        // Apply task status filter after getting task data
        if (!empty($filters['task_status'])) {
            $filtered_projects = [];
            foreach ($projects as $project) {
                if ($filters['task_status'] == 'pending' && $project['tasks']['pending'] > 0) {
                    $filtered_projects[] = $project;
                } elseif ($filters['task_status'] == 'completed' && $project['tasks']['progress'] == 100) {
                    $filtered_projects[] = $project;
                }
            }
            $projects = $filtered_projects;
        }

        return $projects;
    }

    public function get_status_count($projects){
        $status_count = [
            'pending_count' => 0,
            'assigned_count' => 0,
            'completed_count' => 0,
        ];

        foreach ($projects as $project) {
            if ($project['tasks']['progress'] == 0) {
                $status_count['pending_count']++;
            }elseif ($project['tasks']['progress'] == 100){
                $status_count['completed_count']++;
            }else{
                $status_count['assigned_count']++;
            }
        }

        return $status_count;
    }

    // get project list
    public function get_projects_by_team_id($team_id){
        $this->db->select('id, title, project_type');
        $this->db->where("JSON_CONTAINS(project_teams, '\"$team_id\"')");
        return $this->db->get('projects')->result_array();
    }

    /**
     * Calculate priority score based on pending tasks and tickets
     */
    private function _calculate_priority_score($project_id) {
        $score = 0;

        // Task priority weights
        $task_weights = ['high' => 3, 'medium' => 2, 'low' => 1];

        // Get pending tasks with priority
        $this->db->select('task_priority');
        $this->db->where('project_id', $project_id);
        $this->db->where_in('task_status', ['pending', 'assigned']);
        $pending_tasks = $this->db->get('tasks')->result_array();

        foreach ($pending_tasks as $task) {
            $score += $task_weights[$task['task_priority']] ?? 1;
        }

        // Ticket priority weights
        $ticket_weights = ['critical' => 4, 'high' => 3, 'medium' => 2, 'low' => 1];

        // Get pending tickets with priority
        $this->db->select('priority');
        $this->db->where('project_id', $project_id);
        $this->db->where_in('status', ['new', 'assigned', 're_open']);
        $pending_tickets = $this->db->get('tickets')->result_array();

        foreach ($pending_tickets as $ticket) {
            $score += $ticket_weights[$ticket['priority']] ?? 1;
        }

        return $score;
    }

    /**
     * Get priority level based on score
     */
    public function get_priority_level($score) {
        if ($score >= 15) return ['level' => 'critical', 'color' => 'danger', 'icon' => 'exclamation-triangle'];
        if ($score >= 10) return ['level' => 'high', 'color' => 'warning', 'icon' => 'exclamation-circle'];
        if ($score >= 5) return ['level' => 'medium', 'color' => 'info', 'icon' => 'info-circle'];
        return ['level' => 'low', 'color' => 'success', 'icon' => 'check-circle'];
    }

}
