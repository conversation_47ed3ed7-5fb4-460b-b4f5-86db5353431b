<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Time_log_m extends MY_Model
{
    protected string $_table_name = 'time_log';
    function __construct() {
        parent::__construct();
    }

    // update time log
    public function get_time_log_data($log_date){
        $time_log = parent::get(['log_date' => $log_date])->result_array();
        $time_log_data = [];

        foreach ($time_log as $log){
            $time_log_data[$log['user_id']][$log['log_type_id']] = $log['log_time'] == '00:00:00' ? null : $log['log_time'];
        }

        foreach ($time_log_data as $user_id => $log_data) {
            $time_log_data[$user_id]['total_duration'] = get_time_from_seconds($this->get_time_log_duration($log_data));
            $time_log_data[$user_id]['total_break'] = get_time_from_seconds($this->get_break_duration($log_data));
        }
        return $time_log_data;
    }

    // update time log
    public function get_time_log_data_overview($start_date, $end_date){
        $time_log = parent::get(['log_date >=' => $start_date, 'log_date <=' => $end_date], [], ['key' => 'log_time', 'direction' => 'ASCz'])->result_array();
        $time_log_data = [];
        $time_log_array = [];
        foreach ($time_log as $log){
            $log_time_item = $log['log_time'] == '00:00:00' ? null : $log['log_time'];

            $time_log_data[$log['user_id']][$log['log_date']][$log['log_type_id']] = $log_time_item;
            if (!empty($log_time_item)){
                $time_log_array[$log['user_id']][$log['log_date']][] = $log;
            }
        }

        foreach ($time_log_data as $user_id => $log_date) {
            foreach ($log_date as $date => $log_data){
                $time_log_data[$user_id][$date]['work_duration_seconds'] = $this->get_time_log_duration($log_data);
                $time_log_data[$user_id][$date]['break_duration_seconds'] = $this->get_total_break_duration($time_log_array[$user_id][$date]);
                $time_log_data[$user_id][$date]['office_duration_seconds'] = $this->get_office_time_duration_new($time_log_array[$user_id][$date]);
                $time_log_data[$user_id][$date]['total_duration'] = get_time_from_seconds($time_log_data[$user_id][$date]['work_duration_seconds']);
                $time_log_data[$user_id][$date]['total_break'] = get_time_from_seconds($time_log_data[$user_id][$date]['break_duration_seconds']);
                $time_log_data[$user_id][$date]['total_office_time'] = get_time_from_seconds($time_log_data[$user_id][$date]['office_duration_seconds']);
            }
        }
        return $time_log_data;
    }

    // calculate punch in time
    private function get_time_log_duration($log_data){
        $total_duration = 0;
        //morning session
        $total_duration += get_time_duration($log_data[1], $log_data[2]);
        //before lunch
        $total_duration += get_time_duration($log_data[3], $log_data[4]);
        //after lunch
        $total_duration += get_time_duration($log_data[5], $log_data[6]);
        //after evening tea
        $total_duration += get_time_duration($log_data[7], $log_data[8]);
        return $total_duration;
    }

    private function get_office_duration($log_data){
        if (!is_array($log_data)){
            return 0;
        }
        $total_duration = 0;

        if ($log_data[count($log_data)-1]['type']==1){
            $punch_out = date('H:i:s');
            $log_data[] = ['type' => 0, 'log_time' => $punch_out];
        }

        for ($i = 0; $i < count($log_data); $i += 2) {
            $inTime = strtotime($log_data[$i]['log_time']);
            $outTime = strtotime($log_data[$i + 1]['log_time']);
            $total_duration += ($outTime - $inTime);
        }
        if (($total_duration < 0)){
            log_message('error', print_r($log_data, true));
            log_message('error', $total_duration);
        }
        return $total_duration;
    }

    // calculate office time
    private function get_office_time_duration_new($log_data){
        if (!empty($log_data)){
            $firstItem = reset($log_data);
            $lastItem = end($log_data);
            return get_time_duration($firstItem['log_time'], $lastItem['log_time']);
        }else{
            return 0;
        }

    }

    private function get_office_time_duration($log_data){
        return get_time_duration($log_data[1], $log_data[8]);
    }

    // calculate punch out time
    private function get_break_duration($log_data){
        $total_duration = 0;
        //morning tea
        $total_duration += get_time_duration($log_data[2], $log_data[3]);
        //lunch time
        $total_duration += get_time_duration($log_data[4], $log_data[5]);
        //evening tea
        $total_duration += get_time_duration($log_data[6], $log_data[7]);
        return $total_duration;
    }

    private function get_total_break_duration($log_data){
        if (!is_array($log_data)){
            return 0;
        }
        $total_duration = 0;
        if ($log_data[count($log_data)-1]['type']==1){
            $punch_out = date('H:i:s');
            $log_data[] = ['type' => 0, 'log_time' => $punch_out];
        }

        for ($i = 1; $i < count($log_data) - 1; $i += 2) {
            $outTime = strtotime($log_data[$i]['log_time']);
            $inTime = strtotime($log_data[$i + 1]['log_time']);
            $total_duration += ($inTime - $outTime);
        }
//        log_message('error', $total_duration);
        return $total_duration;
    }

    // get time log data for employee
    public function get_employee_time_log_data($user_id, $from_date, $to_date){
        $this->db->order_by('log_time', 'ASC');
        $time_log = parent::get(['log_date>=' => $from_date, 'log_date<=' => $to_date, 'user_id' => $user_id])->result_array();
        $time_log_data = [];
        $time_log_array = [];

        foreach ($time_log as $log){
            $time_log_data[$log['log_date']][$log['log_type_id']] = $log['log_time'] == '00:00:00' ? null : $log['log_time'];
            $time_log_item = $log['log_time'] == '00:00:00' ? null : $log['log_time'];
            if (!empty($time_log_item)){
                $time_log_array[$log['log_date']][] = [
                    'log_time' => $time_log_item,
                    'type' => $log['type']
                ];
                $time_log_data[$log['log_date']]['records'][] = $log;
            }
        }



        foreach ($time_log_data as $log_date => $log_data) {
//            log_message('error', $log_date.':'.$user_id.':'.json_encode($time_log_array[$log_date]));
            $time_log_data[$log_date]['total_duration'] = get_time_from_seconds($this->get_office_duration($time_log_array[$log_date]));
            $time_log_data[$log_date]['total_break'] = get_time_from_seconds($this->get_total_break_duration($time_log_array[$log_date]));

            $time_log_data[$log_date]['total_break'] = max($time_log_data[$log_date]['total_break'], 0);
        }
//        log_message('error', json_encode($time_log_data));
        return $time_log_data;
    }

    // generate report
    public function generate_overview_report($start_date, $end_date, $user_id = 0){

        if ($user_id > 0){
            $users = $this->users_m->get(
                ['id' => $user_id],
                ['id', 'name', 'phone', 'employee_code', 'is_performance']
            )->result_array();
        }else{
            $users = $this->users_m->get(
                ['employee_code!=' => '', 'employee_status' => 1],
                ['id', 'name', 'phone', 'employee_code', 'is_performance']
            )->result_array();
        }


        // get time log
        $time_log_data = $this->time_log_m->get_time_log_data_overview($start_date, $end_date);

        // get attendance data
        $attendance_data = $this->attendance_m->get_attendance_data_overview($start_date, $end_date);

        foreach($users as $key => $user){
            $users[$key]['attendance_overview'] = [
                'P' => 0,
                'A' => 0,
                'WH' => 0,
                'OF' => 0,
                'OD' => 0,
                'HD' => 0,
                'late_coming' => 0,
                'early_going' => 0,
            ];

            $users[$key]['time_log_overview'] = [
                'work_duration_seconds' => 0,
                'break_duration_seconds' => 0,
                'office_duration_seconds' => 0,
                'work_duration' => '00:00:00',
                'break_duration' => '00:00:00',
                'office_duration' => '00:00:00',
            ];


            // attendance status
            $user_attendance_data = $attendance_data[$user['id']];
            if (is_array($user_attendance_data)){

                foreach ($user_attendance_data as $date => $attendance){
                    if ($attendance['attendance'] == 'P'){
                        $users[$key]['attendance_overview']['P']++;
                    }elseif ($attendance['attendance'] == 'A'){
                        $users[$key]['attendance_overview']['A']++;
                    }elseif ($attendance['attendance'] == 'WH'){
                        $users[$key]['attendance_overview']['WH']++;
                    }elseif ($attendance['attendance'] == 'OF'){
                        $users[$key]['attendance_overview']['OF']++;
                    }elseif ($attendance['attendance'] == 'OD'){
                        $users[$key]['attendance_overview']['OD']++;
                    }elseif ($attendance['attendance'] == 'HD'){
                        $users[$key]['attendance_overview']['HD']++;
                    }
                }
            }

            // time log status
            $user_time_log_data = $time_log_data[$user['id']];
            if(is_array($user_time_log_data)){
                foreach ($user_time_log_data as $date => $time_log){
                    $users[$key]['time_log_overview']['work_duration_seconds'] += $time_log['work_duration_seconds'];
                    $users[$key]['time_log_overview']['break_duration_seconds'] += $time_log['break_duration_seconds'];
                    $users[$key]['time_log_overview']['office_duration_seconds'] += $time_log['office_duration_seconds'];
                    if (is_late_coming($time_log)){
                        $users[$key]['attendance_overview']['late_coming']++;
                    }

                    if (is_early_going($time_log)){
                        $users[$key]['attendance_overview']['early_going']++;
                    }
                }
            }
            $user_job_duration = $this->work_history_m->get_employee_total_job_duration_performance($start_date, $end_date, $user['id']);

            $users[$key]['time_log_overview']['work_duration'] = get_time_from_seconds($users[$key]['time_log_overview']['work_duration_seconds']);
            $users[$key]['time_log_overview']['break_duration'] = get_time_from_seconds($users[$key]['time_log_overview']['break_duration_seconds']);
            $users[$key]['time_log_overview']['office_duration'] = get_time_from_seconds($users[$key]['time_log_overview']['office_duration_seconds']);
            $users[$key]['time_log_overview']['job_duration'] = get_time_from_seconds($user_job_duration);

            $employee_points = 0;
            $job_points = 0;
            $attendance_points = 0;

            $user_job_duration += $this->work_history_m->get_employee_total_job_duration_performance_ot($start_date, $end_date, $user['id'])*3;

            $tasks_count_completed = $this->tasks_m->get([
                'user_id' => $user['id'],
                'due_date >=' => $start_date,
                'due_date <=' => $end_date,
                'task_status!=' => 'assigned',
            ])->num_rows();

            $task_points = $tasks_count_completed*0.3;


            if ($user_job_duration > 0){
                $job_points = $user_job_duration/25200*2;
//                $job_points += $task_points;
            }
            if ($user['id'] == 57){
                $job_points -= 10;
            }
            if ($user['id'] == 43){
                $job_points += 7.3;
            }
            if($user['id'] == 44 || $user['id'] == 30){//technical support
                $job_points += 7;
            }
            if($user['id'] == 51 || $user['id'] == 20 || $user['id'] == 15  || $user['id'] == 53){
                $job_points -= 25;
            }
            if($user['id'] == 64){
                $job_points += 35;
            }
            if($user['id'] == 61){
                $job_points += 10;
            }
            
            

            // calculate employee points

            $work_duration = $users[$key]['time_log_overview']['work_duration_seconds'];
            $avg_work_duration = $work_duration/25;

            if ($users[$key]['attendance_overview']['A'] > 0){
                $work_duration = $work_duration - $avg_work_duration/2 * $users[$key]['attendance_overview']['A'];
            }

            $work_duration = $work_duration + ($users[$key]['attendance_overview']['OD'] * 2.8 * $avg_work_duration);


//            if ($user['id'] == 35){
//                $work_duration = $work_duration + (1.2 * $avg_work_duration);
//                $job_points += 3;
//            }

            if($users[$key]['attendance_overview']['late_coming'] > 4){
                $work_duration = $work_duration - ($users[$key]['attendance_overview']['late_coming'] * 1.5 * $avg_work_duration);
            }

            
            // $work_duration = $work_duration - ($users[$key]['attendance_overview']['early_going'] * 0.25 * $avg_work_duration);
//            $work_duration = $work_duration - $users[$key]['time_log_overview']['break_duration_seconds']/3600/$users[$key]['attendance_overview']['P'];

            $attendance_points = $work_duration/20200*0.5;

            // if($users[$key]['time_log_overview']['break_duration_seconds'] > 0 && $users[$key]['attendance_overview']['P']){
            //     $avg_break = $users[$key]['time_log_overview']['break_duration_seconds']/3600/$users[$key]['attendance_overview']['P'];
            // }else{
            //     $avg_break = 1;
            // }
            
            // if ($avg_break < 1){
            //     $attendance_points = $attendance_points + 2;
            // }else{
            //     $attendance_points = $attendance_points + $avg_break;
            // }

//

            $attendance_points = $attendance_points + ($users[$key]['attendance_overview']['OD']*1.5);
            $attendance_points = $attendance_points - ($users[$key]['attendance_overview']['WH']*1.5);

            
//
//            $attendance_points = $attendance_points - ($users[$key]['attendance_overview']['late_coming']*0.15);
//            $attendance_points = $attendance_points - ($users[$key]['attendance_overview']['early_going']*0.15);



            $users[$key]['attendance_points'] = number_format($attendance_points, 4);

            if ($user['id'] == 62){
                $job_points -= 18.0;
            }
            if ($user['id'] == 58){
                $job_points -= 20;
            }
            
            if ($user['id'] == 18){
                $job_points += 10;
            }
            if ($user['id'] == 15){
                $job_points += 8;
            }
            if ($user['id'] == 10){
                $job_points += 5;
            }
//
            //   if ($user['id'] == 37){
            //       $job_points += (16/24) * $users[$key]['attendance_overview']['P'];
            //  }
              if ($user['id'] == 9){
                 $job_points += (80/27) * $users[$key]['attendance_overview']['P'];
             }
            //  if ($user['id'] == 45){
            //      $job_points += (3/25) * $users[$key]['attendance_overview']['P'];
            //  }
//            if (in_array($user['id'], [$haystack])){
//                $job_points += 3;
//            }
//            $job_points = 0;
//            $users[$key]['attendance_points'] = 0;
            $users[$key]['job_points'] = number_format($job_points, 4);
//            $users[$key]['effectiveness_points'] = number_format($effectiveness_points, 4);
            $users[$key]['employee_points'] = number_format($users[$key]['attendance_points'] + $users[$key]['job_points'], 4);
        }
        usort($users, function($a, $b) {
            return $b['employee_points'] <=> $a['employee_points'];
        });
        $rank = 1;
        foreach ($users as $key => $user){
            if ($user['is_performance'] == 1){
                $users[$key]['rank'] = $rank++;
            }else{
                $users[$key]['rank'] = '-';
            }
        }
        usort($users, function($a, $b) {
            return $a['rank'] <=> $b['rank'];
        });
        return $users;
    }


}
