<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Migrate_task_assign_to_work_history extends CI_Migration {

    public function up() {
        // Migrate data from task_assign to work_history
        $this->db->select('*');
        $this->db->from('task_assign');
        $task_assign_data = $this->db->get()->result_array();

        foreach ($task_assign_data as $task_assign) {
            // Calculate duration if start_time and end_time are available
            $duration = null;
            $start_time = null;
            $end_time = null;
            
            if (!empty($task_assign['start_time']) && !empty($task_assign['end_time'])) {
                // Convert time to datetime for the job_date
                $job_date = $task_assign['job_date'];
                $start_time = $job_date . ' ' . $task_assign['start_time'];
                $end_time = $job_date . ' ' . $task_assign['end_time'];
                
                $start = new DateTime($start_time);
                $end = new DateTime($end_time);
                $duration = ($end->getTimestamp() - $start->getTimestamp()) / 60; // Convert to minutes
            }

            $work_history_data = [
                'user_id' => $task_assign['user_id'],
                'item_type' => 'task',
                'item_id' => $task_assign['task_id'],
                'start_time' => $start_time,
                'end_time' => $end_time,
                'duration' => $duration,
                'remarks' => $task_assign['remarks'],
                'created_at' => $task_assign['created_on'] ?? date('Y-m-d H:i:s'),
                'updated_at' => $task_assign['updated_on'] ?? date('Y-m-d H:i:s')
            ];

            $this->db->insert('work_history', $work_history_data);
        }

        // Add a comment to indicate migration completion
        $this->db->query("ALTER TABLE `task_assign` COMMENT = 'Migrated to work_history table on " . date('Y-m-d H:i:s') . "'");
        
        echo "Migration completed: " . count($task_assign_data) . " records migrated from task_assign to work_history\n";
    }

    public function down() {
        // Remove migrated task data from work_history
        $this->db->where('item_type', 'task');
        $this->db->delete('work_history');

        // Remove comment from task_assign table
        $this->db->query("ALTER TABLE `task_assign` COMMENT = ''");
        
        echo "Migration rolled back: task data removed from work_history\n";
    }
} 