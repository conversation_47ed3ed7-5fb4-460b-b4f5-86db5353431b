<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-14 05:34:42 --> Severity: Warning --> array_keys() expects parameter 1 to be array, null given /home/<USER>/ci_apps/application/controllers/app/Project_report.php 175
ERROR - 2025-06-14 05:34:42 --> Severity: Warning --> implode(): Invalid arguments passed /home/<USER>/ci_apps/application/controllers/app/Project_report.php 175
ERROR - 2025-06-14 05:34:48 --> Severity: Warning --> array_keys() expects parameter 1 to be array, null given /home/<USER>/ci_apps/application/controllers/app/Project_report.php 175
ERROR - 2025-06-14 05:34:48 --> Severity: Warning --> implode(): Invalid arguments passed /home/<USER>/ci_apps/application/controllers/app/Project_report.php 175
ERROR - 2025-06-14 06:59:40 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:22:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:37:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:20:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:01:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 06:59:40
        )

)

ERROR - 2025-06-14 06:59:40 --> -12500
ERROR - 2025-06-14 09:06:29 --> Array
(
    [0] => Array
        (
            [log_time] => 09:04:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:18:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:27:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:01:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:06:29
        )

)

ERROR - 2025-06-14 09:06:29 --> -3631
ERROR - 2025-06-14 09:30:16 --> Array
(
    [0] => Array
        (
            [log_time] => 09:34:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:12:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:35:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:46:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:30:16
        )

)

ERROR - 2025-06-14 09:30:16 --> -6644
ERROR - 2025-06-14 09:30:21 --> Array
(
    [0] => Array
        (
            [log_time] => 09:34:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:12:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:35:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:46:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:30:21
        )

)

ERROR - 2025-06-14 09:30:21 --> -6639
ERROR - 2025-06-14 09:34:58 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:15:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:34:58
        )

)

ERROR - 2025-06-14 09:34:58 --> -2402
ERROR - 2025-06-14 09:36:59 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:22:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:37:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:20:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:01:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:59
        )

)

ERROR - 2025-06-14 09:36:59 --> -3061
ERROR - 2025-06-14 09:37:31 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 12:32:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:03:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:37:31
        )

)

ERROR - 2025-06-14 09:37:31 --> -5009
ERROR - 2025-06-14 09:40:29 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:23:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:01:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:29
        )

)

ERROR - 2025-06-14 09:40:29 --> -2671
ERROR - 2025-06-14 09:42:18 --> Array
(
    [0] => Array
        (
            [log_time] => 09:14:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:39:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:27:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:22:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:42:18
        )

)

ERROR - 2025-06-14 09:42:18 --> -2742
ERROR - 2025-06-14 09:43:06 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:23:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:01:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:43:06
        )

)

ERROR - 2025-06-14 09:43:06 --> -2514
ERROR - 2025-06-14 09:43:11 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:23:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:01:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:43:11
        )

)

ERROR - 2025-06-14 09:43:11 --> -2509
ERROR - 2025-06-14 09:43:33 --> Array
(
    [0] => Array
        (
            [log_time] => 09:42:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:16:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:26:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:01:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:43:33
        )

)

ERROR - 2025-06-14 09:43:33 --> -6687
ERROR - 2025-06-14 09:44:26 --> Array
(
    [0] => Array
        (
            [log_time] => 09:42:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:16:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:26:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:01:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:44:26
        )

)

ERROR - 2025-06-14 09:44:26 --> -6634
ERROR - 2025-06-14 09:45:07 --> Array
(
    [0] => Array
        (
            [log_time] => 09:35:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:29:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:14:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:45:07
        )

)

ERROR - 2025-06-14 09:45:07 --> -3473
ERROR - 2025-06-14 09:45:24 --> Array
(
    [0] => Array
        (
            [log_time] => 09:35:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:29:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:14:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:45:24
        )

)

ERROR - 2025-06-14 09:45:24 --> -3456
ERROR - 2025-06-14 09:45:46 --> Array
(
    [0] => Array
        (
            [log_time] => 09:14:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:39:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:27:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:22:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:45:46
        )

)

ERROR - 2025-06-14 09:45:46 --> -2534
ERROR - 2025-06-14 09:49:02 --> Array
(
    [0] => Array
        (
            [log_time] => 09:35:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:29:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:14:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:49:02
        )

)

ERROR - 2025-06-14 09:49:02 --> -3238
ERROR - 2025-06-14 09:54:51 --> Array
(
    [0] => Array
        (
            [log_time] => 09:44:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:31:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:14:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:54:51
        )

)

ERROR - 2025-06-14 09:54:51 --> -3309
ERROR - 2025-06-14 11:21:08 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 11:23:58 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-14'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('10:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '10:50')
OR (end_time BETWEEN '09:30' AND '10:50')
 )
ERROR - 2025-06-14 12:36:55 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 13:55:12 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-14'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '14:00')
OR (end_time BETWEEN '11:20' AND '14:00')
 )
ERROR - 2025-06-14 13:56:43 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 13:58:28 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-14'
AND   (
('09:10' BETWEEN start_time AND end_time)
OR ('14:57' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:10' AND '14:57')
OR (end_time BETWEEN '09:10' AND '14:57')
 )
ERROR - 2025-06-14 13:59:40 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 14:47:15 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 14:47:15 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 14:47:17 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 14:47:17 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 14:47:19 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 14:48:30 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 14:50:01 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-14'
AND   (
('09:10' BETWEEN start_time AND end_time)
OR ('13:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:10' AND '13:50')
OR (end_time BETWEEN '09:10' AND '13:50')
 )
ERROR - 2025-06-14 15:34:02 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 16:08:58 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [2] => Array
        (
            [log_time] => 11:49:00
            [type] => 0
        )

)

ERROR - 2025-06-14 16:08:58 --> -1749875400
ERROR - 2025-06-14 16:21:36 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 16:52:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-14'
AND   (
('14:45' BETWEEN start_time AND end_time)
OR ('17:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:45' AND '17:45')
OR (end_time BETWEEN '14:45' AND '17:45')
 )
ERROR - 2025-06-14 17:18:08 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 17:22:14 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-14'
AND   (
('09:35' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:35' AND '11:00')
OR (end_time BETWEEN '09:35' AND '11:00')
 )
ERROR - 2025-06-14 17:23:41 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-14'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '14:00')
OR (end_time BETWEEN '11:20' AND '14:00')
 )
ERROR - 2025-06-14 17:26:28 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-14'
AND   (
('16:00' BETWEEN start_time AND end_time)
OR ('17:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:00' AND '17:00')
OR (end_time BETWEEN '16:00' AND '17:00')
 )
ERROR - 2025-06-14 17:27:18 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-14'
AND   (
('09:26' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:26' AND '11:15')
OR (end_time BETWEEN '09:26' AND '11:15')
 )
ERROR - 2025-06-14 17:31:35 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 17:32:20 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-14'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:00')
OR (end_time BETWEEN '09:30' AND '11:00')
 )
ERROR - 2025-06-14 17:32:49 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-14'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('13:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '13:50')
OR (end_time BETWEEN '11:20' AND '13:50')
 )
ERROR - 2025-06-14 17:33:23 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 17:34:11 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-14'
AND   (
('14:20' BETWEEN start_time AND end_time)
OR ('15:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:20' AND '15:00')
OR (end_time BETWEEN '14:20' AND '15:00')
 )
ERROR - 2025-06-14 17:34:19 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-14'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:10')
OR (end_time BETWEEN '09:30' AND '11:10')
 )
ERROR - 2025-06-14 17:36:44 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-14'
AND   (
('11:30' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:30' AND '14:00')
OR (end_time BETWEEN '11:30' AND '14:00')
 )
ERROR - 2025-06-14 17:37:12 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-14'
AND   (
('15:00' BETWEEN start_time AND end_time)
OR ('17:37' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:00' AND '17:37')
OR (end_time BETWEEN '15:00' AND '17:37')
 )
ERROR - 2025-06-14 17:37:40 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-14'
AND   (
('15:10' BETWEEN start_time AND end_time)
OR ('17:37' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:10' AND '17:37')
OR (end_time BETWEEN '15:10' AND '17:37')
 )
ERROR - 2025-06-14 17:38:06 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 17:40:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-14'
AND   (
('15:01' BETWEEN start_time AND end_time)
OR ('17:40' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:01' AND '17:40')
OR (end_time BETWEEN '15:01' AND '17:40')
 )
ERROR - 2025-06-14 17:41:15 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-14'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:50')
OR (end_time BETWEEN '09:30' AND '11:50')
 )
ERROR - 2025-06-14 17:41:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-14'
AND   (
('14:50' BETWEEN start_time AND end_time)
OR ('17:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:50' AND '17:30')
OR (end_time BETWEEN '14:50' AND '17:30')
 )
ERROR - 2025-06-14 17:42:45 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-14'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('10:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '10:50')
OR (end_time BETWEEN '09:30' AND '10:50')
 )
ERROR - 2025-06-14 17:43:31 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-14'
AND   (
('11:19' BETWEEN start_time AND end_time)
OR ('14:01' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:19' AND '14:01')
OR (end_time BETWEEN '11:19' AND '14:01')
 )
ERROR - 2025-06-14 17:45:09 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-14'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('10:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '10:50')
OR (end_time BETWEEN '09:30' AND '10:50')
 )
ERROR - 2025-06-14 17:45:45 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-14'
AND   (
('11:19' BETWEEN start_time AND end_time)
OR ('14:01' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:19' AND '14:01')
OR (end_time BETWEEN '11:19' AND '14:01')
 )
ERROR - 2025-06-14 17:46:03 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-14'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('10:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '10:00')
OR (end_time BETWEEN '09:30' AND '10:00')
 )
ERROR - 2025-06-14 17:46:17 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-14'
AND   (
('14:37' BETWEEN start_time AND end_time)
OR ('17:47' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:37' AND '17:47')
OR (end_time BETWEEN '14:37' AND '17:47')
 )
ERROR - 2025-06-14 17:47:35 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-14'
AND   (
('09:35' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:35' AND '11:00')
OR (end_time BETWEEN '09:35' AND '11:00')
 )
ERROR - 2025-06-14 17:50:05 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-14'
AND   (
('10:01' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:01' AND '11:00')
OR (end_time BETWEEN '10:01' AND '11:00')
 )
ERROR - 2025-06-14 17:50:37 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-14'
AND   (
('14:12' BETWEEN start_time AND end_time)
OR ('16:39' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:12' AND '16:39')
OR (end_time BETWEEN '14:12' AND '16:39')
 )
ERROR - 2025-06-14 17:52:30 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-14'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('12:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '12:30')
OR (end_time BETWEEN '11:20' AND '12:30')
 )
ERROR - 2025-06-14 17:53:22 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-14'
AND   (
('11:15' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:15' AND '14:00')
OR (end_time BETWEEN '11:15' AND '14:00')
 )
ERROR - 2025-06-14 17:54:10 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-14'
AND   (
('16:40' BETWEEN start_time AND end_time)
OR ('17:52' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:40' AND '17:52')
OR (end_time BETWEEN '16:40' AND '17:52')
 )
ERROR - 2025-06-14 17:57:51 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-14'
AND   (
('12:31' BETWEEN start_time AND end_time)
OR ('13:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:31' AND '13:00')
OR (end_time BETWEEN '12:31' AND '13:00')
 )
ERROR - 2025-06-14 17:59:44 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-14'
AND   (
('13:01' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:01' AND '14:00')
OR (end_time BETWEEN '13:01' AND '14:00')
 )
ERROR - 2025-06-14 18:00:22 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-14'
AND   (
('14:50' BETWEEN start_time AND end_time)
OR ('17:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:50' AND '17:50')
OR (end_time BETWEEN '14:50' AND '17:50')
 )
ERROR - 2025-06-14 18:00:44 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-14'
AND   (
('14:39' BETWEEN start_time AND end_time)
OR ('16:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:39' AND '16:00')
OR (end_time BETWEEN '14:39' AND '16:00')
 )
ERROR - 2025-06-14 18:01:53 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-14'
AND   (
('16:01' BETWEEN start_time AND end_time)
OR ('17:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:01' AND '17:00')
OR (end_time BETWEEN '16:01' AND '17:00')
 )
ERROR - 2025-06-14 18:02:19 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-14 18:03:31 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-14'
AND   (
('17:01' BETWEEN start_time AND end_time)
OR ('18:02' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:01' AND '18:02')
OR (end_time BETWEEN '17:01' AND '18:02')
 )
ERROR - 2025-06-14 18:03:32 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-14'
AND   (
('09:22' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:22' AND '11:00')
OR (end_time BETWEEN '09:22' AND '11:00')
 )
ERROR - 2025-06-14 18:03:49 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-14'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:00')
OR (end_time BETWEEN '09:30' AND '11:00')
 )
ERROR - 2025-06-14 18:04:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-14'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '14:00')
OR (end_time BETWEEN '11:20' AND '14:00')
 )
ERROR - 2025-06-14 18:05:12 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-14'
AND   (
('11:27' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:27' AND '14:00')
OR (end_time BETWEEN '11:27' AND '14:00')
 )
ERROR - 2025-06-14 18:05:41 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-14'
AND   (
('14:49' BETWEEN start_time AND end_time)
OR ('18:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:49' AND '18:05')
OR (end_time BETWEEN '14:49' AND '18:05')
 )
ERROR - 2025-06-14 18:07:28 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-14'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('18:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '18:05')
OR (end_time BETWEEN '14:40' AND '18:05')
 )
ERROR - 2025-06-14 23:30:42 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-14'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:15')
OR (end_time BETWEEN '09:30' AND '11:15')
 )
ERROR - 2025-06-14 23:31:19 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-14'
AND   (
('11:35' BETWEEN start_time AND end_time)
OR ('13:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:35' AND '13:50')
OR (end_time BETWEEN '11:35' AND '13:50')
 )
ERROR - 2025-06-14 23:34:21 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-14'
AND   (
('14:30' BETWEEN start_time AND end_time)
OR ('18:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:30' AND '18:00')
OR (end_time BETWEEN '14:30' AND '18:00')
 )
