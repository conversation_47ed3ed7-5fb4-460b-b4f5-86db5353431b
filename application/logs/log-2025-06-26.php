<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-26 09:20:10 --> Array
(
    [0] => Array
        (
            [log_time] => 09:05:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:37:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:23:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:20:10
        )

)

ERROR - 2025-06-26 09:20:10 --> -1850
ERROR - 2025-06-26 09:27:39 --> Array
(
    [0] => Array
        (
            [log_time] => 09:10:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:01:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:27:39
        )

)

ERROR - 2025-06-26 09:27:39 --> -2721
ERROR - 2025-06-26 09:30:24 --> Array
(
    [0] => Array
        (
            [log_time] => 09:23:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:54:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 12:05:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:10:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:30:24
        )

)

ERROR - 2025-06-26 09:30:24 --> -2436
ERROR - 2025-06-26 09:30:24 --> Array
(
    [0] => Array
        (
            [log_time] => 09:23:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:54:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 12:05:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:10:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:30:24
        )

)

ERROR - 2025-06-26 09:30:24 --> -2436
ERROR - 2025-06-26 09:39:33 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:13:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:39:33
        )

)

ERROR - 2025-06-26 09:39:33 --> -3027
ERROR - 2025-06-26 09:40:08 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:13:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:08
        )

)

ERROR - 2025-06-26 09:40:08 --> -2992
ERROR - 2025-06-26 09:40:30 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:13:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:30
        )

)

ERROR - 2025-06-26 09:40:30 --> -2970
ERROR - 2025-06-26 09:40:33 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:13:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:33
        )

)

ERROR - 2025-06-26 09:40:33 --> -2967
ERROR - 2025-06-26 09:42:27 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:10:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:42:27
        )

)

ERROR - 2025-06-26 09:42:27 --> -3753
ERROR - 2025-06-26 09:42:28 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:10:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:42:28
        )

)

ERROR - 2025-06-26 09:42:28 --> -3752
ERROR - 2025-06-26 09:42:46 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:10:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:42:46
        )

)

ERROR - 2025-06-26 09:42:46 --> -3734
ERROR - 2025-06-26 09:42:47 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:10:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:42:47
        )

)

ERROR - 2025-06-26 09:42:47 --> -3733
ERROR - 2025-06-26 09:42:57 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:13:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:42:57
        )

)

ERROR - 2025-06-26 09:42:57 --> -2823
ERROR - 2025-06-26 09:45:25 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:53:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:45:25
        )

)

ERROR - 2025-06-26 09:45:25 --> -3395
ERROR - 2025-06-26 09:46:19 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:13:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:46:19
        )

)

ERROR - 2025-06-26 09:46:19 --> -2621
ERROR - 2025-06-26 09:53:39 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:53:39
        )

)

ERROR - 2025-06-26 09:53:39 --> -2781
ERROR - 2025-06-26 09:53:39 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:01:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:53:39
        )

)

ERROR - 2025-06-26 09:53:39 --> -3561
ERROR - 2025-06-26 09:54:45 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:18:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:43:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:54:45
        )

)

ERROR - 2025-06-26 09:54:45 --> -1395
ERROR - 2025-06-26 10:14:34 --> Array
(
    [0] => Array
        (
            [log_time] => 09:29:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:29:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:49:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:50:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:24:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:14:34
        )

)

ERROR - 2025-06-26 10:14:34 --> -506
ERROR - 2025-06-26 10:27:01 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:13:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:27:01
        )

)

ERROR - 2025-06-26 10:27:01 --> -179
ERROR - 2025-06-26 10:29:15 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:13:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:52:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:29:15
        )

)

ERROR - 2025-06-26 10:29:15 --> -45
ERROR - 2025-06-26 10:31:55 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-26 10:31:55 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-26 10:31:55 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-26 10:31:55 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-26 10:31:55 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-26 12:06:57 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-26 12:10:59 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-26 12:11:23 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-26 12:12:51 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('61', '30', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-26 12:12:51')
ERROR - 2025-06-26 12:13:01 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('61', '30', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-26 12:13:01')
ERROR - 2025-06-26 12:13:05 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('61', '30', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-26 12:13:05')
ERROR - 2025-06-26 12:13:31 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('61', '30', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-26 12:13:31')
ERROR - 2025-06-26 12:13:51 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 931
ERROR - 2025-06-26 12:15:50 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-26 12:16:03 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-26 12:16:22 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 6 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE `tickets`.`status` = 'assigned'
AND (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '2025-06-26)'
ERROR - 2025-06-26 16:27:45 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('73', '68', 'Updated ticket status to \'Closed\'', '[]', 'closed', '2025-06-26 16:27:45')
ERROR - 2025-06-26 16:28:36 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('64', '68', 'Updated ticket status to \'Closed\'', '[]', 'closed', '2025-06-26 16:28:36')
ERROR - 2025-06-26 18:16:07 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-26'
AND   (
('09:53' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:15' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:53' AND '11:15')
OR (TIME(end_time) BETWEEN '09:53' AND '11:15')
 )
ERROR - 2025-06-26 18:17:19 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-26'
AND   (
('11:16' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('13:15' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '11:16' AND '13:15')
OR (TIME(end_time) BETWEEN '11:16' AND '13:15')
 )
ERROR - 2025-06-26 18:17:46 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-26'
AND   (
('11:16' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('13:15' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '11:16' AND '13:15')
OR (TIME(end_time) BETWEEN '11:16' AND '13:15')
 )
