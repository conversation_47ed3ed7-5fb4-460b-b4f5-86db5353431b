<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-24 06:57:39 --> Array
(
    [0] => Array
        (
            [log_time] => 07:12:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 09:00:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 11:37:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 12:00:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:50:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:36:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 06:57:39
        )

)

ERROR - 2025-06-24 06:57:39 --> -6501
ERROR - 2025-06-24 06:59:52 --> Array
(
    [0] => Array
        (
            [log_time] => 07:12:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 09:00:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 11:37:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 12:00:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:50:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:36:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 06:59:52
        )

)

ERROR - 2025-06-24 06:59:52 --> -6368
ERROR - 2025-06-24 07:03:01 --> Array
(
    [0] => Array
        (
            [log_time] => 07:12:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 09:00:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 11:37:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 12:00:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:50:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:36:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 07:03:01
        )

)

ERROR - 2025-06-24 07:03:01 --> -6179
ERROR - 2025-06-24 07:58:24 --> Array
(
    [0] => Array
        (
            [log_time] => 07:12:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 09:00:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 11:37:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 12:00:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:50:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:36:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 07:58:24
        )

)

ERROR - 2025-06-24 07:58:24 --> -2856
ERROR - 2025-06-24 08:39:09 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:30:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:34:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 08:39:09
        )

)

ERROR - 2025-06-24 08:39:09 --> -8331
ERROR - 2025-06-24 09:24:00 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:34:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:38:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:24:00
        )

)

ERROR - 2025-06-24 09:24:00 --> -4200
ERROR - 2025-06-24 09:24:28 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:34:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:38:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:24:28
        )

)

ERROR - 2025-06-24 09:24:28 --> -4172
ERROR - 2025-06-24 09:27:12 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:34:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:38:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:27:12
        )

)

ERROR - 2025-06-24 09:27:12 --> -4008
ERROR - 2025-06-24 09:42:17 --> Array
(
    [0] => Array
        (
            [log_time] => 09:39:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:03:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:25:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:42:17
        )

)

ERROR - 2025-06-24 09:42:17 --> -4723
ERROR - 2025-06-24 09:50:52 --> Array
(
    [0] => Array
        (
            [log_time] => 09:39:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:05:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:30:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:51:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:50:52
        )

)

ERROR - 2025-06-24 09:50:52 --> -3428
ERROR - 2025-06-24 09:51:20 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:51:20
        )

)

ERROR - 2025-06-24 09:51:20 --> -2920
ERROR - 2025-06-24 09:51:20 --> Array
(
    [0] => Array
        (
            [log_time] => 09:36:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:38:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:02:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:31:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:38:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:51:20
        )

)

ERROR - 2025-06-24 09:51:20 --> -4540
ERROR - 2025-06-24 09:51:22 --> Array
(
    [0] => Array
        (
            [log_time] => 09:26:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:37:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 12:00:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:50:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:36:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:51:22
        )

)

ERROR - 2025-06-24 09:51:22 --> -2618
ERROR - 2025-06-24 09:51:39 --> Array
(
    [0] => Array
        (
            [log_time] => 09:16:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:04:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:30:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:51:39
        )

)

ERROR - 2025-06-24 09:51:39 --> -1221
ERROR - 2025-06-24 09:52:10 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:05:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:30:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:51:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:52:10
        )

)

ERROR - 2025-06-24 09:52:10 --> -2810
ERROR - 2025-06-24 09:52:26 --> Array
(
    [0] => Array
        (
            [log_time] => 10:34:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:06:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:30:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:55:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:52:26
        )

)

ERROR - 2025-06-24 09:52:26 --> -6334
ERROR - 2025-06-24 09:52:28 --> Array
(
    [0] => Array
        (
            [log_time] => 10:34:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:06:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:30:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:55:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:52:28
        )

)

ERROR - 2025-06-24 09:52:28 --> -6332
ERROR - 2025-06-24 09:52:36 --> Array
(
    [0] => Array
        (
            [log_time] => 10:34:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:06:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:30:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:55:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:52:36
        )

)

ERROR - 2025-06-24 09:52:36 --> -6324
ERROR - 2025-06-24 09:52:39 --> Array
(
    [0] => Array
        (
            [log_time] => 10:34:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:06:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:30:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:55:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:52:39
        )

)

ERROR - 2025-06-24 09:52:39 --> -6321
ERROR - 2025-06-24 09:52:46 --> Array
(
    [0] => Array
        (
            [log_time] => 09:36:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:39:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:13:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:52:46
        )

)

ERROR - 2025-06-24 09:52:46 --> -1274
ERROR - 2025-06-24 09:54:13 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:03:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:25:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:54:13
        )

)

ERROR - 2025-06-24 09:54:13 --> -2747
ERROR - 2025-06-24 09:55:27 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:39:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:02:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:55:27
        )

)

ERROR - 2025-06-24 09:55:27 --> -2973
ERROR - 2025-06-24 09:57:23 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:34:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:38:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:57:23
        )

)

ERROR - 2025-06-24 09:57:23 --> -2197
ERROR - 2025-06-24 10:10:41 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:30:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:34:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:10:41
        )

)

ERROR - 2025-06-24 10:10:41 --> -2839
ERROR - 2025-06-24 10:15:12 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:30:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:34:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:15:12
        )

)

ERROR - 2025-06-24 10:15:12 --> -2568
ERROR - 2025-06-24 11:11:24 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:11:24 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:11:24 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:11:24 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:11:24 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:18 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:18 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:18 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:18 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:18 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:33 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:33 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:33 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:33 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:33 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:39 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:39 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:39 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:39 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:15:39 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:16:21 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:16:21 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:16:21 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:16:21 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:16:21 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:18:05 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:18:05 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:18:05 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:18:05 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:18:05 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:19:10 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:19:10 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:19:10 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:19:10 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:19:10 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:19:24 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:19:24 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:19:24 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:19:24 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:19:24 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:20:05 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:20:05 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:20:05 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:20:05 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:20:05 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:20:39 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:20:39 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:20:39 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:20:39 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:20:39 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-24 11:27:47 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-24 12:55:36 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-24 12:55:58 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-24 12:57:46 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-24 14:00:50 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 14:03:03 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 14:10:09 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 14:15:19 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 14:20:43 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 14:34:35 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 14:35:26 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 14:43:51 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 14:47:50 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 14:48:39 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-24 14:51:07 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 14:56:03 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 15:06:18 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 15:06:42 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 15:11:03 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 15:18:48 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 15:31:17 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 15:41:02 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 15:41:15 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 16:01:42 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 13 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-06-17)'
AND   (
`tickets`.`title` LIKE '%we%' ESCAPE '!'
OR  `tickets`.`description` LIKE '%we%' ESCAPE '!'
OR  `projects`.`title` LIKE '%we%' ESCAPE '!'
OR  `users`.`name` LIKE '%we%' ESCAPE '!'
OR  `tickets`.`reported_by` LIKE '%we%' ESCAPE '!'
OR  CONCAT("TKT", tickets.id) LIKE '%we%' ESCAPE '!'
 )
ERROR - 2025-06-24 16:01:53 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 13 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-06-17)'
AND   (
`tickets`.`title` LIKE '%we%' ESCAPE '!'
OR  `tickets`.`description` LIKE '%we%' ESCAPE '!'
OR  `projects`.`title` LIKE '%we%' ESCAPE '!'
OR  `users`.`name` LIKE '%we%' ESCAPE '!'
OR  `tickets`.`reported_by` LIKE '%we%' ESCAPE '!'
OR  CONCAT("TKT", tickets.id) LIKE '%we%' ESCAPE '!'
 )
ERROR - 2025-06-24 16:02:03 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 13 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-06-17)'
AND   (
`tickets`.`title` LIKE '%we%' ESCAPE '!'
OR  `tickets`.`description` LIKE '%we%' ESCAPE '!'
OR  `projects`.`title` LIKE '%we%' ESCAPE '!'
OR  `users`.`name` LIKE '%we%' ESCAPE '!'
OR  `tickets`.`reported_by` LIKE '%we%' ESCAPE '!'
OR  CONCAT("TKT", tickets.id) LIKE '%we%' ESCAPE '!'
 )
ERROR - 2025-06-24 16:02:10 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 13 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-06-17)'
AND   (
`tickets`.`title` LIKE '%we%' ESCAPE '!'
OR  `tickets`.`description` LIKE '%we%' ESCAPE '!'
OR  `projects`.`title` LIKE '%we%' ESCAPE '!'
OR  `users`.`name` LIKE '%we%' ESCAPE '!'
OR  `tickets`.`reported_by` LIKE '%we%' ESCAPE '!'
OR  CONCAT("TKT", tickets.id) LIKE '%we%' ESCAPE '!'
 )
ERROR - 2025-06-24 16:13:10 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 16:33:38 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 16:35:43 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-24 16:52:31 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-24'
AND   (
('09:05' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:20' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:05' AND '11:20')
OR (TIME(end_time) BETWEEN '09:05' AND '11:20')
 )
ERROR - 2025-06-24 16:54:20 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-24'
AND   (
('11:35' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('14:00' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '11:35' AND '14:00')
OR (TIME(end_time) BETWEEN '11:35' AND '14:00')
 )
ERROR - 2025-06-24 16:56:51 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-24'
AND   (
('14:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('16:55' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '14:30' AND '16:55')
OR (TIME(end_time) BETWEEN '14:30' AND '16:55')
 )
ERROR - 2025-06-24 17:29:10 --> Severity: Warning --> count(): Parameter must be an array or an object that implements Countable /home/<USER>/ci_apps/application/core/MY_Controller.php 27
ERROR - 2025-06-24 17:29:10 --> Severity: Warning --> Cannot modify header information - headers already sent by (output started at /home/<USER>/ci_apps/system/core/Exceptions.php:272) /home/<USER>/ci_apps/system/helpers/url_helper.php 565
ERROR - 2025-06-24 18:00:16 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '51'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-24'
AND   (
('09:41' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:04' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:41' AND '11:04')
OR (TIME(end_time) BETWEEN '09:41' AND '11:04')
 )
ERROR - 2025-06-24 18:25:53 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-24'
AND   (
('09:37' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('10:00' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:37' AND '10:00')
OR (TIME(end_time) BETWEEN '09:37' AND '10:00')
 )
ERROR - 2025-06-24 18:27:47 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-24'
AND   (
('10:01' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('13:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '10:01' AND '13:30')
OR (TIME(end_time) BETWEEN '10:01' AND '13:30')
 )
ERROR - 2025-06-24 18:28:17 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-24'
AND   (
('14:35' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('18:28' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '14:35' AND '18:28')
OR (TIME(end_time) BETWEEN '14:35' AND '18:28')
 )
