ERROR - 2025-06-23 07:25:23 --> Severity: error --> Exception: Call to a member function format() on bool /home/<USER>/ci_apps/application/views/app/tasks/task_description.php 49
ERROR - 2025-06-23 07:29:21 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 07:29:21
        )

)

ERROR - 2025-06-23 07:29:21 --> -11559
ERROR - 2025-06-23 07:32:17 --> <PERSON><PERSON><PERSON>
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 07:32:17
        )

)

ERROR - 2025-06-23 07:32:17 --> -11383
ERROR - 2025-06-23 07:32:58 --> Severity: error --> Exception: Call to a member function format() on bool /home/<USER>/ci_apps/application/views/app/tasks/task_description.php 49
ERROR - 2025-06-23 07:33:59 --> Severity: error --> Exception: Call to a member function format() on bool /home/<USER>/ci_apps/application/views/app/tasks/task_description.php 51
ERROR - 2025-06-23 07:34:08 --> Severity: error --> Exception: Call to a member function format() on bool /home/<USER>/ci_apps/application/views/app/tasks/task_description.php 51
ERROR - 2025-06-23 07:45:18 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '`NULL`
AND date(tasks.due_date) < `IS` `NULL`' at line 3 - Invalid query: SELECT count(tasks.id) as item_count
FROM `tasks`
WHERE date(tasks.due_date) > `IS` `NULL`
AND date(tasks.due_date) < `IS` `NULL`
ERROR - 2025-06-23 07:45:23 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '`NULL`
AND date(tasks.due_date) < `IS` `NULL`' at line 3 - Invalid query: SELECT count(tasks.id) as item_count
FROM `tasks`
WHERE date(tasks.due_date) > `IS` `NULL`
AND date(tasks.due_date) < `IS` `NULL`
ERROR - 2025-06-23 07:45:32 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '`NULL`
AND date(tasks.due_date) < `IS` `NULL`' at line 3 - Invalid query: SELECT count(tasks.id) as item_count
FROM `tasks`
WHERE date(tasks.due_date) > `IS` `NULL`
AND date(tasks.due_date) < `IS` `NULL`
ERROR - 2025-06-23 07:46:52 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '`NULL`
AND date(tasks.due_date) < `IS` `NULL`' at line 3 - Invalid query: SELECT count(tasks.id) as item_count
FROM `tasks`
WHERE date(tasks.due_date) > `IS` `NULL`
AND date(tasks.due_date) < `IS` `NULL`
ERROR - 2025-06-23 07:46:53 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '`NULL`
AND date(tasks.due_date) < `IS` `NULL`' at line 3 - Invalid query: SELECT count(tasks.id) as item_count
FROM `tasks`
WHERE date(tasks.due_date) > `IS` `NULL`
AND date(tasks.due_date) < `IS` `NULL`
ERROR - 2025-06-23 07:47:14 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '`NULL`
AND date(tasks.due_date) < `IS` `NULL`' at line 3 - Invalid query: SELECT count(tasks.id) as item_count
FROM `tasks`
WHERE date(tasks.due_date) > `IS` `NULL`
AND date(tasks.due_date) < `IS` `NULL`
ERROR - 2025-06-23 07:47:15 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '`NULL`
AND date(tasks.due_date) < `IS` `NULL`' at line 3 - Invalid query: SELECT count(tasks.id) as item_count
FROM `tasks`
WHERE date(tasks.due_date) > `IS` `NULL`
AND date(tasks.due_date) < `IS` `NULL`
ERROR - 2025-06-23 07:47:48 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '`NULL`
AND date(tasks.due_date) < `IS` `NULL`' at line 3 - Invalid query: SELECT count(tasks.id) as item_count
FROM `tasks`
WHERE date(tasks.due_date) > `IS` `NULL`
AND date(tasks.due_date) < `IS` `NULL`
ERROR - 2025-06-23 09:21:04 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:50:00
            [type] => 1
        )

    [2] => Array
        (
            [log_time] => 14:43:00
            [type] => 0
        )

)

ERROR - 2025-06-23 09:21:04 --> -1750653600
ERROR - 2025-06-23 09:34:18 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:44:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:23:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:34:18
        )

)

ERROR - 2025-06-23 09:34:18 --> -2982
ERROR - 2025-06-23 09:36:18 --> Array
(
    [0] => Array
        (
            [log_time] => 09:29:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 12:34:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:47:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:18
        )

)

ERROR - 2025-06-23 09:36:18 --> -4662
ERROR - 2025-06-23 09:36:25 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:25
        )

)

ERROR - 2025-06-23 09:36:25 --> -3935
ERROR - 2025-06-23 09:38:38 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:46:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:21:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:38:38
        )

)

ERROR - 2025-06-23 09:38:38 --> -2662
ERROR - 2025-06-23 09:39:05 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:46:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:21:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:39:05
        )

)

ERROR - 2025-06-23 09:39:05 --> -2635
ERROR - 2025-06-23 09:39:36 --> Array
(
    [0] => Array
        (
            [log_time] => 09:08:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:49:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:39:36
        )

)

ERROR - 2025-06-23 09:39:36 --> -1344
ERROR - 2025-06-23 09:39:36 --> Array
(
    [0] => Array
        (
            [log_time] => 09:08:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:49:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:39:36
        )

)

ERROR - 2025-06-23 09:39:36 --> -1344
ERROR - 2025-06-23 09:39:42 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:46:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:21:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:39:42
        )

)

ERROR - 2025-06-23 09:39:42 --> -2598
ERROR - 2025-06-23 09:39:47 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:46:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:21:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:39:47
        )

)

ERROR - 2025-06-23 09:39:47 --> -2593
ERROR - 2025-06-23 09:40:59 --> Severity: Warning --> count(): Parameter must be an array or an object that implements Countable /home/<USER>/ci_apps/application/core/MY_Controller.php 27
ERROR - 2025-06-23 09:40:59 --> Severity: Warning --> Cannot modify header information - headers already sent by (output started at /home/<USER>/ci_apps/system/core/Exceptions.php:272) /home/<USER>/ci_apps/system/helpers/url_helper.php 565
ERROR - 2025-06-23 09:41:03 --> Severity: Warning --> count(): Parameter must be an array or an object that implements Countable /home/<USER>/ci_apps/application/core/MY_Controller.php 27
ERROR - 2025-06-23 09:41:03 --> Severity: Warning --> Cannot modify header information - headers already sent by (output started at /home/<USER>/ci_apps/system/core/Exceptions.php:272) /home/<USER>/ci_apps/system/helpers/url_helper.php 565
ERROR - 2025-06-23 09:41:14 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:46:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:21:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:41:14
        )

)

ERROR - 2025-06-23 09:41:14 --> -2506
ERROR - 2025-06-23 09:41:24 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:41:24
        )

)

ERROR - 2025-06-23 09:41:24 --> -3516
ERROR - 2025-06-23 09:41:24 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:50:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:35:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:24:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:41:24
        )

)

ERROR - 2025-06-23 09:41:24 --> -5676
ERROR - 2025-06-23 09:41:31 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:41:31
        )

)

ERROR - 2025-06-23 09:41:31 --> -4769
ERROR - 2025-06-23 09:41:36 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:41:36
        )

)

ERROR - 2025-06-23 09:41:36 --> -4764
ERROR - 2025-06-23 09:41:44 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-23 09:41:44 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:46:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:21:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:41:44
        )

)

ERROR - 2025-06-23 09:41:44 --> -2476
ERROR - 2025-06-23 09:42:05 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:46:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:21:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:42:05
        )

)

ERROR - 2025-06-23 09:42:05 --> -2455
ERROR - 2025-06-23 09:42:07 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:46:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:21:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:42:07
        )

)

ERROR - 2025-06-23 09:42:07 --> -2453
ERROR - 2025-06-23 09:43:44 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:59:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:34:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:19:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:46:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:22:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:43:44
        )

)

ERROR - 2025-06-23 09:43:44 --> -5236
ERROR - 2025-06-23 09:53:55 --> Array
(
    [0] => Array
        (
            [log_time] => 09:29:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:30:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:57:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:41:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:57:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:53:55
        )

)

ERROR - 2025-06-23 09:53:55 --> -4685
ERROR - 2025-06-23 09:55:38 --> Array
(
    [0] => Array
        (
            [log_time] => 09:29:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 12:34:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:47:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:55:38
        )

)

ERROR - 2025-06-23 09:55:38 --> -3502
ERROR - 2025-06-23 09:57:53 --> Array
(
    [0] => Array
        (
            [log_time] => 09:29:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:30:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:57:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:41:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:57:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:57:53
        )

)

ERROR - 2025-06-23 09:57:53 --> -4447
ERROR - 2025-06-23 10:20:06 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:20:06
        )

)

ERROR - 2025-06-23 10:20:06 --> -2454
ERROR - 2025-06-23 10:21:13 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:46:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:21:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:21:13
        )

)

ERROR - 2025-06-23 10:21:13 --> -107
ERROR - 2025-06-23 10:31:47 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '65'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('09:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('10:00' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:30' AND '10:00')
OR (TIME(end_time) BETWEEN '09:30' AND '10:00')
 )
ERROR - 2025-06-23 10:35:47 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '65'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('09:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:00' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:30' AND '11:00')
OR (TIME(end_time) BETWEEN '09:30' AND '11:00')
 )
ERROR - 2025-06-23 10:47:32 --> Array
(
    [0] => Array
        (
            [log_time] => 09:29:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:30:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:57:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:41:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:57:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:47:32
        )

)

ERROR - 2025-06-23 10:47:32 --> -1468
ERROR - 2025-06-23 10:59:46 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:50:00
            [type] => 1
        )

    [2] => Array
        (
            [log_time] => 14:43:00
            [type] => 0
        )

)

ERROR - 2025-06-23 10:59:46 --> -1750653600
ERROR - 2025-06-23 11:08:12 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-23 11:35:33 --> Severity: Warning --> count(): Parameter must be an array or an object that implements Countable /home/<USER>/ci_apps/application/core/MY_Controller.php 27
ERROR - 2025-06-23 11:35:33 --> Severity: Warning --> Cannot modify header information - headers already sent by (output started at /home/<USER>/ci_apps/system/core/Exceptions.php:272) /home/<USER>/ci_apps/system/helpers/url_helper.php 565
ERROR - 2025-06-23 11:35:37 --> Severity: Warning --> count(): Parameter must be an array or an object that implements Countable /home/<USER>/ci_apps/application/core/MY_Controller.php 27
ERROR - 2025-06-23 11:35:37 --> Severity: Warning --> Cannot modify header information - headers already sent by (output started at /home/<USER>/ci_apps/system/core/Exceptions.php:272) /home/<USER>/ci_apps/system/helpers/url_helper.php 565
ERROR - 2025-06-23 12:26:28 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '15'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('09:32' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('10:38' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:32' AND '10:38')
OR (TIME(end_time) BETWEEN '09:32' AND '10:38')
 )
ERROR - 2025-06-23 12:32:48 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '43'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('09:28' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:05' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:28' AND '11:05')
OR (TIME(end_time) BETWEEN '09:28' AND '11:05')
 )
ERROR - 2025-06-23 13:06:04 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('12:00' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('12:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '12:00' AND '12:30')
OR (TIME(end_time) BETWEEN '12:00' AND '12:30')
 )
ERROR - 2025-06-23 13:07:05 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('10:34' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:06' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '10:34' AND '11:06')
OR (TIME(end_time) BETWEEN '10:34' AND '11:06')
 )
ERROR - 2025-06-23 13:07:51 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('11:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:59' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '11:30' AND '11:59')
OR (TIME(end_time) BETWEEN '11:30' AND '11:59')
 )
ERROR - 2025-06-23 13:08:39 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('12:31' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('13:08' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '12:31' AND '13:08')
OR (TIME(end_time) BETWEEN '12:31' AND '13:08')
 )
ERROR - 2025-06-23 14:31:01 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:50:00
            [type] => 1
        )

    [2] => Array
        (
            [log_time] => 14:43:00
            [type] => 0
        )

)

ERROR - 2025-06-23 14:31:01 --> -1750653600
ERROR - 2025-06-23 15:58:26 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '18'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('09:37' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:00' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:37' AND '11:00')
OR (TIME(end_time) BETWEEN '09:37' AND '11:00')
 )
ERROR - 2025-06-23 16:14:45 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:50:00
            [type] => 1
        )

    [2] => Array
        (
            [log_time] => 14:43:00
            [type] => 0
        )

)

ERROR - 2025-06-23 16:14:45 --> -1750653600
ERROR - 2025-06-23 16:15:06 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '18'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('11:24' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('13:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '11:24' AND '13:30')
OR (TIME(end_time) BETWEEN '11:24' AND '13:30')
 )
ERROR - 2025-06-23 16:18:23 --> Severity: Warning --> count(): Parameter must be an array or an object that implements Countable /home/<USER>/ci_apps/application/core/MY_Controller.php 27
ERROR - 2025-06-23 16:18:23 --> Severity: Warning --> Cannot modify header information - headers already sent by (output started at /home/<USER>/ci_apps/system/core/Exceptions.php:272) /home/<USER>/ci_apps/system/helpers/url_helper.php 565
ERROR - 2025-06-23 16:18:35 --> Severity: Warning --> count(): Parameter must be an array or an object that implements Countable /home/<USER>/ci_apps/application/core/MY_Controller.php 27
ERROR - 2025-06-23 16:18:35 --> Severity: Warning --> Cannot modify header information - headers already sent by (output started at /home/<USER>/ci_apps/system/core/Exceptions.php:272) /home/<USER>/ci_apps/system/helpers/url_helper.php 565
ERROR - 2025-06-23 16:19:52 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '18'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('15:00' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('15:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '15:00' AND '15:30')
OR (TIME(end_time) BETWEEN '15:00' AND '15:30')
 )
ERROR - 2025-06-23 16:39:16 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-23 16:39:16 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-23 16:39:16 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-23 16:39:16 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-23 16:39:16 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-23 16:51:17 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-23 16:53:03 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('13:09' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('13:55' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '13:09' AND '13:55')
OR (TIME(end_time) BETWEEN '13:09' AND '13:55')
 )
ERROR - 2025-06-23 16:53:45 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('14:35' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('16:50' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '14:35' AND '16:50')
OR (TIME(end_time) BETWEEN '14:35' AND '16:50')
 )
ERROR - 2025-06-23 17:02:25 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-23 17:10:45 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-23 17:11:22 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-23 18:00:57 --> Severity: Warning --> count(): Parameter must be an array or an object that implements Countable /home/<USER>/ci_apps/application/core/MY_Controller.php 27
ERROR - 2025-06-23 18:00:57 --> Severity: Warning --> Cannot modify header information - headers already sent by (output started at /home/<USER>/ci_apps/system/core/Exceptions.php:272) /home/<USER>/ci_apps/system/helpers/url_helper.php 565
ERROR - 2025-06-23 18:01:01 --> Severity: Warning --> count(): Parameter must be an array or an object that implements Countable /home/<USER>/ci_apps/application/core/MY_Controller.php 27
ERROR - 2025-06-23 18:01:01 --> Severity: Warning --> Cannot modify header information - headers already sent by (output started at /home/<USER>/ci_apps/system/core/Exceptions.php:272) /home/<USER>/ci_apps/system/helpers/url_helper.php 565
ERROR - 2025-06-23 18:01:04 --> Severity: Warning --> count(): Parameter must be an array or an object that implements Countable /home/<USER>/ci_apps/application/core/MY_Controller.php 27
ERROR - 2025-06-23 18:01:04 --> Severity: Warning --> Cannot modify header information - headers already sent by (output started at /home/<USER>/ci_apps/system/core/Exceptions.php:272) /home/<USER>/ci_apps/system/helpers/url_helper.php 565
ERROR - 2025-06-23 18:14:07 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '64'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('09:40' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:10' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:40' AND '11:10')
OR (TIME(end_time) BETWEEN '09:40' AND '11:10')
 )
ERROR - 2025-06-23 18:15:06 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '64'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('11:25' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('14:00' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '11:25' AND '14:00')
OR (TIME(end_time) BETWEEN '11:25' AND '14:00')
 )
ERROR - 2025-06-23 18:16:03 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('09:48' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:48' AND '11:30')
OR (TIME(end_time) BETWEEN '09:48' AND '11:30')
 )
ERROR - 2025-06-23 18:16:57 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('11:51' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('13:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '11:51' AND '13:30')
OR (TIME(end_time) BETWEEN '11:51' AND '13:30')
 )
ERROR - 2025-06-23 18:17:47 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('14:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('17:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '14:30' AND '17:30')
OR (TIME(end_time) BETWEEN '14:30' AND '17:30')
 )
ERROR - 2025-06-23 18:18:08 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('17:31' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('18:18' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '17:31' AND '18:18')
OR (TIME(end_time) BETWEEN '17:31' AND '18:18')
 )
ERROR - 2025-06-23 18:20:34 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '43'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('11:25' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('13:51' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '11:25' AND '13:51')
OR (TIME(end_time) BETWEEN '11:25' AND '13:51')
 )
ERROR - 2025-06-23 18:37:56 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('16:51' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('18:40' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '16:51' AND '18:40')
OR (TIME(end_time) BETWEEN '16:51' AND '18:40')
 )
ERROR - 2025-06-23 18:40:39 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '63'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('09:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:00' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:30' AND '11:00')
OR (TIME(end_time) BETWEEN '09:30' AND '11:00')
 )
ERROR - 2025-06-23 18:43:33 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '63'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('11:15' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('13:55' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '11:15' AND '13:55')
OR (TIME(end_time) BETWEEN '11:15' AND '13:55')
 )
ERROR - 2025-06-23 18:50:48 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '63'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-23'
AND   (
('14:45' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('18:40' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '14:45' AND '18:40')
OR (TIME(end_time) BETWEEN '14:45' AND '18:40')
 )
ERROR - 2025-06-23 20:59:33 --> Severity: error --> Exception: Call to a member function format() on bool /home/<USER>/ci_apps/application/helpers/ui_helper.php 133
ERROR - 2025-06-23 21:00:35 --> Severity: error --> Exception: Call to a member function format() on bool /home/<USER>/ci_apps/application/helpers/ui_helper.php 133
