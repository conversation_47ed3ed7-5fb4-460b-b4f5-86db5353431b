<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-27 09:18:07 --> Array
(
    [0] => Array
        (
            [log_time] => 09:06:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:44:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:18:07
        )

)

ERROR - 2025-06-27 09:18:07 --> -2753
ERROR - 2025-06-27 09:18:17 --> <PERSON>rray
(
    [0] => Array
        (
            [log_time] => 09:06:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:44:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:18:17
        )

)

ERROR - 2025-06-27 09:18:17 --> -2743
ERROR - 2025-06-27 09:35:12 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:42:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:17:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:35:12
        )

)

ERROR - 2025-06-27 09:35:12 --> -3588
ERROR - 2025-06-27 09:39:20 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:42:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:17:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:39:20
        )

)

ERROR - 2025-06-27 09:39:20 --> -3340
ERROR - 2025-06-27 09:41:19 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:42:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:17:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:41:19
        )

)

ERROR - 2025-06-27 09:41:19 --> -3221
ERROR - 2025-06-27 09:42:45 --> Array
(
    [0] => Array
        (
            [log_time] => 09:31:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:24:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:42:45
        )

)

ERROR - 2025-06-27 09:42:45 --> -3255
ERROR - 2025-06-27 09:43:42 --> Array
(
    [0] => Array
        (
            [log_time] => 09:38:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:24:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:04:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:43:42
        )

)

ERROR - 2025-06-27 09:43:42 --> -3678
ERROR - 2025-06-27 09:51:38 --> Array
(
    [0] => Array
        (
            [log_time] => 09:31:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:24:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:51:38
        )

)

ERROR - 2025-06-27 09:51:38 --> -2722
ERROR - 2025-06-27 10:04:04 --> Array
(
    [0] => Array
        (
            [log_time] => 09:38:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:23:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:04:04
        )

)

ERROR - 2025-06-27 10:04:04 --> -2696
ERROR - 2025-06-27 10:04:15 --> Array
(
    [0] => Array
        (
            [log_time] => 09:38:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:23:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:04:15
        )

)

ERROR - 2025-06-27 10:04:15 --> -2685
ERROR - 2025-06-27 10:04:24 --> Array
(
    [0] => Array
        (
            [log_time] => 09:31:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:24:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:04:24
        )

)

ERROR - 2025-06-27 10:04:24 --> -1896
ERROR - 2025-06-27 10:05:28 --> Array
(
    [0] => Array
        (
            [log_time] => 09:38:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:23:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:05:28
        )

)

ERROR - 2025-06-27 10:05:28 --> -2612
ERROR - 2025-06-27 10:05:42 --> Array
(
    [0] => Array
        (
            [log_time] => 09:38:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:23:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:05:42
        )

)

ERROR - 2025-06-27 10:05:42 --> -2598
ERROR - 2025-06-27 10:05:43 --> Array
(
    [0] => Array
        (
            [log_time] => 09:38:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:23:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:05:43
        )

)

ERROR - 2025-06-27 10:05:43 --> -2597
ERROR - 2025-06-27 10:05:45 --> Array
(
    [0] => Array
        (
            [log_time] => 09:38:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:23:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:05:45
        )

)

ERROR - 2025-06-27 10:05:45 --> -2595
ERROR - 2025-06-27 10:05:46 --> Array
(
    [0] => Array
        (
            [log_time] => 09:38:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:23:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:05:46
        )

)

ERROR - 2025-06-27 10:05:46 --> -2594
ERROR - 2025-06-27 10:28:13 --> Array
(
    [0] => Array
        (
            [log_time] => 09:31:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:24:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:47:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:28:13
        )

)

ERROR - 2025-06-27 10:28:13 --> -527
ERROR - 2025-06-27 14:56:02 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-27 16:55:43 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('87', '68', 'Assigned ticket to Jahana Sherin K', '[]', 'assigned', '2025-06-27 16:55:43')
ERROR - 2025-06-27 21:20:56 --> Array
(
    [photo] => Array
        (
            [name] => cropped_1751039455252.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/php4D6N8y
            [error] => 0
            [size] => 25135
        )

)

ERROR - 2025-06-27 21:20:56 --> Array
(
    [file_type] => image
    [file] => uploads/employee_photo/062025/ac0fd821ed39590d1816a967f082e3dd.jpg
)

ERROR - 2025-06-27 21:21:23 --> Array
(
    [photo] => Array
        (
            [name] => cropped_1751039483345.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpDyeonT
            [error] => 0
            [size] => 23495
        )

)

ERROR - 2025-06-27 21:21:23 --> Array
(
    [file_type] => image
    [file] => uploads/employee_photo/062025/f4ed5b1b4d86514be494e002d9d4e16a.jpg
)

ERROR - 2025-06-27 21:22:34 --> Array
(
    [photo] => Array
        (
            [name] => cropped_1751039553551.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpe5UEnL
            [error] => 0
            [size] => 20920
        )

)

ERROR - 2025-06-27 21:22:34 --> Array
(
    [file_type] => image
    [file] => uploads/employee_photo/062025/24e1a2ae553952bc347898b72cbad12f.jpg
)

ERROR - 2025-06-27 21:23:13 --> Array
(
    [photo] => Array
        (
            [name] => cropped_1751039592758.jpg
            [type] => image/jpeg
            [tmp_name] => /tmp/phpRuSbHt
            [error] => 0
            [size] => 25128
        )

)

ERROR - 2025-06-27 21:23:13 --> Array
(
    [file_type] => image
    [file] => uploads/employee_photo/062025/8fb30f8c131c92e7f69fa30c64dfff2e.jpg
)

