<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-28 09:13:27 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:53:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:24:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:13:27
        )

)

ERROR - 2025-06-28 09:13:27 --> -2733
ERROR - 2025-06-28 09:13:55 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:53:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:24:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:13:55
        )

)

ERROR - 2025-06-28 09:13:55 --> -2705
ERROR - 2025-06-28 09:14:17 --> Array
(
    [0] => Array
        (
            [log_time] => 09:09:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:18:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:18:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:14:17
        )

)

ERROR - 2025-06-28 09:14:17 --> -3103
ERROR - 2025-06-28 09:14:23 --> Array
(
    [0] => Array
        (
            [log_time] => 09:09:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:18:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:18:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:14:23
        )

)

ERROR - 2025-06-28 09:14:23 --> -3097
ERROR - 2025-06-28 09:14:56 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:53:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:24:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:14:56
        )

)

ERROR - 2025-06-28 09:14:56 --> -2644
ERROR - 2025-06-28 09:23:49 --> Array
(
    [0] => Array
        (
            [log_time] => 09:09:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:50:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:23:49
        )

)

ERROR - 2025-06-28 09:23:49 --> -3311
ERROR - 2025-06-28 09:28:24 --> Array
(
    [0] => Array
        (
            [log_time] => 09:22:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 12:41:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:50:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:28:24
        )

)

ERROR - 2025-06-28 09:28:24 --> -3756
ERROR - 2025-06-28 09:32:10 --> Array
(
    [0] => Array
        (
            [log_time] => 09:39:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:18:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:48:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:32:10
        )

)

ERROR - 2025-06-28 09:32:10 --> -4430
ERROR - 2025-06-28 09:32:10 --> Array
(
    [0] => Array
        (
            [log_time] => 09:56:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:29:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:34:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:22:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:18:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:32:10
        )

)

ERROR - 2025-06-28 09:32:10 --> -6890
ERROR - 2025-06-28 09:32:21 --> Array
(
    [0] => Array
        (
            [log_time] => 09:39:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:18:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:48:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:32:21
        )

)

ERROR - 2025-06-28 09:32:21 --> -4419
ERROR - 2025-06-28 09:33:06 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:29:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:34:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:22:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:33:06
        )

)

ERROR - 2025-06-28 09:33:06 --> -5934
ERROR - 2025-06-28 09:34:49 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:26:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:34:49
        )

)

ERROR - 2025-06-28 09:34:49 --> -2951
ERROR - 2025-06-28 09:35:02 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:26:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:35:02
        )

)

ERROR - 2025-06-28 09:35:02 --> -2938
ERROR - 2025-06-28 09:35:36 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:22:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:51:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:28:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:35:36
        )

)

ERROR - 2025-06-28 09:35:36 --> -3144
ERROR - 2025-06-28 09:38:43 --> Array
(
    [0] => Array
        (
            [log_time] => 09:31:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:27:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:49:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:30:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:14:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:38:43
        )

)

ERROR - 2025-06-28 09:38:43 --> -3497
ERROR - 2025-06-28 09:44:06 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:26:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:44:06
        )

)

ERROR - 2025-06-28 09:44:06 --> -2394
ERROR - 2025-06-28 09:45:45 --> Array
(
    [0] => Array
        (
            [log_time] => 09:31:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:27:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:49:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:30:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:14:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:45:45
        )

)

ERROR - 2025-06-28 09:45:45 --> -3075
ERROR - 2025-06-28 09:45:58 --> Array
(
    [0] => Array
        (
            [log_time] => 09:44:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:18:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:50:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:36:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:45:58
        )

)

ERROR - 2025-06-28 09:45:58 --> -4322
ERROR - 2025-06-28 09:46:10 --> Array
(
    [0] => Array
        (
            [log_time] => 09:44:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:18:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:50:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:36:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:46:10
        )

)

ERROR - 2025-06-28 09:46:10 --> -4310
ERROR - 2025-06-28 09:46:57 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:22:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:51:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:28:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:46:57
        )

)

ERROR - 2025-06-28 09:46:57 --> -2463
ERROR - 2025-06-28 09:47:59 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:28:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:58:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:39:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:22:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:47:59
        )

)

ERROR - 2025-06-28 09:47:59 --> -5821
ERROR - 2025-06-28 09:50:51 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:28:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:58:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:39:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:22:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:50:51
        )

)

ERROR - 2025-06-28 09:50:51 --> -5649
ERROR - 2025-06-28 09:54:09 --> Array
(
    [0] => Array
        (
            [log_time] => 09:31:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:27:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:49:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:30:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:14:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:54:09
        )

)

ERROR - 2025-06-28 09:54:09 --> -2571
ERROR - 2025-06-28 10:18:20 --> Array
(
    [0] => Array
        (
            [log_time] => 09:09:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:50:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 10:18:20
        )

)

ERROR - 2025-06-28 10:18:20 --> -40
ERROR - 2025-06-28 10:20:45 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:26:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:20:45
        )

)

ERROR - 2025-06-28 10:20:45 --> -195
ERROR - 2025-06-28 11:58:24 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 12:00:32 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 12:35:46 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 12:36:37 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 12:40:36 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 12:40:57 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 12:41:09 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 12:41:15 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 12:41:23 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 12:41:34 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 12:57:28 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 12:58:03 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 12:59:57 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 13:04:24 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 13:46:08 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 13:53:17 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 13:57:57 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 16:35:50 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-28 16:39:04 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
