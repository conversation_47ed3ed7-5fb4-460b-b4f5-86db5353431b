<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-20 09:11:03 --> Array
(
    [0] => Array
        (
            [log_time] => 09:09:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:33:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:11:03
        )

)

ERROR - 2025-06-20 09:11:03 --> -3057
ERROR - 2025-06-20 09:11:42 --> Array
(
    [0] => Array
        (
            [log_time] => 09:09:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:33:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:11:42
        )

)

ERROR - 2025-06-20 09:11:42 --> -3018
ERROR - 2025-06-20 09:16:46 --> Array
(
    [0] => Array
        (
            [log_time] => 09:24:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:44:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:00:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:51:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:16:46
        )

)

ERROR - 2025-06-20 09:16:46 --> -5234
ERROR - 2025-06-20 09:25:37 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:40:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:25:37
        )

)

ERROR - 2025-06-20 09:25:37 --> -3863
ERROR - 2025-06-20 09:27:42 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:40:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:27:42
        )

)

ERROR - 2025-06-20 09:27:42 --> -3738
ERROR - 2025-06-20 09:30:28 --> Array
(
    [0] => Array
        (
            [log_time] => 09:36:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:03:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:40:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:30:28
        )

)

ERROR - 2025-06-20 09:30:28 --> -4352
ERROR - 2025-06-20 09:31:05 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:41:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:31:05
        )

)

ERROR - 2025-06-20 09:31:05 --> -3955
ERROR - 2025-06-20 09:34:02 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:03:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:36:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:34:02
        )

)

ERROR - 2025-06-20 09:34:02 --> -3238
ERROR - 2025-06-20 09:35:38 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:03:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:35:38
        )

)

ERROR - 2025-06-20 09:35:38 --> -2902
ERROR - 2025-06-20 09:36:11 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:03:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:11
        )

)

ERROR - 2025-06-20 09:36:11 --> -2869
ERROR - 2025-06-20 09:36:55 --> Array
(
    [0] => Array
        (
            [log_time] => 09:29:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:31:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:54:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:24:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:55
        )

)

ERROR - 2025-06-20 09:36:55 --> -2825
ERROR - 2025-06-20 09:37:45 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-20 09:37:45 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-20 09:37:45 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-20 09:37:45 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-20 09:37:45 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-20 09:38:25 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:03:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:38:25
        )

)

ERROR - 2025-06-20 09:38:25 --> -2735
ERROR - 2025-06-20 09:38:28 --> Array
(
    [0] => Array
        (
            [log_time] => 09:29:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:30:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:54:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:30:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:38:28
        )

)

ERROR - 2025-06-20 09:38:28 --> -3152
ERROR - 2025-06-20 09:40:07 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:40:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:07
        )

)

ERROR - 2025-06-20 09:40:07 --> -3293
ERROR - 2025-06-20 09:40:07 --> Array
(
    [0] => Array
        (
            [log_time] => 09:24:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:03:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:36:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:07
        )

)

ERROR - 2025-06-20 09:40:07 --> -2513
ERROR - 2025-06-20 09:40:09 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:40:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:09
        )

)

ERROR - 2025-06-20 09:40:09 --> -3291
ERROR - 2025-06-20 09:40:10 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:40:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:10
        )

)

ERROR - 2025-06-20 09:40:10 --> -3290
ERROR - 2025-06-20 09:40:12 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:40:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:12
        )

)

ERROR - 2025-06-20 09:40:12 --> -3288
ERROR - 2025-06-20 10:00:36 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-20'
AND   (
('09:15' BETWEEN start_time AND end_time)
OR ('10:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:15' AND '10:00')
OR (end_time BETWEEN '09:15' AND '10:00')
 )
ERROR - 2025-06-20 10:00:36 --> Array
(
    [0] => Array
        (
            [log_time] => 09:24:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:44:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:00:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:51:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:00:36
        )

)

ERROR - 2025-06-20 10:00:36 --> -2604
ERROR - 2025-06-20 10:01:02 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-20 10:01:02 --> Array
(
    [0] => Array
        (
            [log_time] => 09:24:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:44:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:00:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:51:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:01:02
        )

)

ERROR - 2025-06-20 10:01:02 --> -2578
ERROR - 2025-06-20 10:01:58 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:41:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:01:58
        )

)

ERROR - 2025-06-20 10:01:58 --> -2102
ERROR - 2025-06-20 10:08:36 --> Array
(
    [0] => Array
        (
            [log_time] => 09:47:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:42:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:08:36
        )

)

ERROR - 2025-06-20 10:08:36 --> -3144
ERROR - 2025-06-20 10:28:05 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-20'
AND   (
('10:01' BETWEEN start_time AND end_time)
OR ('10:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:01' AND '10:30')
OR (end_time BETWEEN '10:01' AND '10:30')
 )
ERROR - 2025-06-20 10:35:20 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-20 10:52:41 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-20'
AND   (
('09:25' BETWEEN start_time AND end_time)
OR ('11:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:25' AND '11:20')
OR (end_time BETWEEN '09:25' AND '11:20')
 )
ERROR - 2025-06-20 11:09:22 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-20'
AND   (
('11:00' BETWEEN start_time AND end_time)
OR ('11:09' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:00' AND '11:09')
OR (end_time BETWEEN '11:00' AND '11:09')
 )
ERROR - 2025-06-20 11:28:51 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-20'
AND   (
('11:10' BETWEEN start_time AND end_time)
OR ('11:28' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:10' AND '11:28')
OR (end_time BETWEEN '11:10' AND '11:28')
 )
ERROR - 2025-06-20 11:31:47 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-20'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:01' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:01')
OR (end_time BETWEEN '09:30' AND '11:01')
 )
ERROR - 2025-06-20 12:24:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-20'
AND   (
('11:29' BETWEEN start_time AND end_time)
OR ('12:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:29' AND '12:00')
OR (end_time BETWEEN '11:29' AND '12:00')
 )
ERROR - 2025-06-20 12:38:24 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-20 12:41:28 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-20'
AND   (
('10:53' BETWEEN start_time AND end_time)
OR ('11:54' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:53' AND '11:54')
OR (end_time BETWEEN '10:53' AND '11:54')
 )
ERROR - 2025-06-20 13:45:07 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-20'
AND   (
('12:15' BETWEEN start_time AND end_time)
OR ('13:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:15' AND '13:50')
OR (end_time BETWEEN '12:15' AND '13:50')
 )
ERROR - 2025-06-20 13:55:10 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-20 13:58:49 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-20 13:59:02 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-20 13:59:07 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-20 14:17:11 --> Query error: Unknown column 'employment_status' in 'WHERE' - Invalid query: SELECT *
FROM `users`
WHERE `employment_status` = 1
AND `work_assign` = 1
ERROR - 2025-06-20 14:34:30 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-20'
AND   (
('11:40' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:40' AND '14:00')
OR (end_time BETWEEN '11:40' AND '14:00')
 )
ERROR - 2025-06-20 14:36:58 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-20'
AND   (
('11:21' BETWEEN start_time AND end_time)
OR ('13:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:21' AND '13:50')
OR (end_time BETWEEN '11:21' AND '13:50')
 )
ERROR - 2025-06-20 14:37:43 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-20'
AND   (
('14:22' BETWEEN start_time AND end_time)
OR ('17:40' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:22' AND '17:40')
OR (end_time BETWEEN '14:22' AND '17:40')
 )
ERROR - 2025-06-20 14:42:40 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-20'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:30')
OR (end_time BETWEEN '09:30' AND '11:30')
 )
ERROR - 2025-06-20 14:46:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-20'
AND   (
('11:31' BETWEEN start_time AND end_time)
OR ('13:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:31' AND '13:30')
OR (end_time BETWEEN '11:31' AND '13:30')
 )
ERROR - 2025-06-20 16:02:29 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-20 16:58:13 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-20 16:58:51 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-20 17:16:14 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:50:00
            [type] => 1
        )

    [2] => Array
        (
            [log_time] => 14:43:00
            [type] => 0
        )

)

ERROR - 2025-06-20 17:16:14 --> -1750394400
ERROR - 2025-06-20 17:31:00 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-20 17:31:00 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:50:00
            [type] => 1
        )

    [2] => Array
        (
            [log_time] => 14:43:00
            [type] => 0
        )

)

ERROR - 2025-06-20 17:31:00 --> -1750394400
ERROR - 2025-06-20 17:31:44 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-20'
AND   (
('09:17' BETWEEN start_time AND end_time)
OR ('13:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:17' AND '13:50')
OR (end_time BETWEEN '09:17' AND '13:50')
 )
ERROR - 2025-06-20 17:31:44 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:50:00
            [type] => 1
        )

    [2] => Array
        (
            [log_time] => 14:43:00
            [type] => 0
        )

)

ERROR - 2025-06-20 17:31:44 --> -1750394400
ERROR - 2025-06-20 17:34:01 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-20'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('17:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '17:30')
OR (end_time BETWEEN '14:40' AND '17:30')
 )
ERROR - 2025-06-20 17:45:13 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-20'
AND   (
('14:30' BETWEEN start_time AND end_time)
OR ('17:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:30' AND '17:50')
OR (end_time BETWEEN '14:30' AND '17:50')
 )
ERROR - 2025-06-20 17:45:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-20'
AND   (
('14:45' BETWEEN start_time AND end_time)
OR ('17:47' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:45' AND '17:47')
OR (end_time BETWEEN '14:45' AND '17:47')
 )
ERROR - 2025-06-20 17:45:57 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:50:00
            [type] => 1
        )

    [2] => Array
        (
            [log_time] => 14:43:00
            [type] => 0
        )

)

ERROR - 2025-06-20 17:45:57 --> -1750394400
ERROR - 2025-06-20 17:48:53 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-20'
AND   (
('09:32' BETWEEN start_time AND end_time)
OR ('11:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:32' AND '11:05')
OR (end_time BETWEEN '09:32' AND '11:05')
 )
ERROR - 2025-06-20 17:49:44 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-20'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('12:35' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '12:35')
OR (end_time BETWEEN '11:20' AND '12:35')
 )
ERROR - 2025-06-20 17:50:33 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-20'
AND   (
('13:30' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:30' AND '14:00')
OR (end_time BETWEEN '13:30' AND '14:00')
 )
ERROR - 2025-06-20 17:52:14 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-20'
AND   (
('14:29' BETWEEN start_time AND end_time)
OR ('17:51' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:29' AND '17:51')
OR (end_time BETWEEN '14:29' AND '17:51')
 )
ERROR - 2025-06-20 17:55:08 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-20 17:56:56 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-20'
AND   (
('09:33' BETWEEN start_time AND end_time)
OR ('10:25' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:33' AND '10:25')
OR (end_time BETWEEN '09:33' AND '10:25')
 )
ERROR - 2025-06-20 18:01:47 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-20 18:02:10 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-20'
AND   (
('08:41' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '08:41' AND '11:00')
OR (end_time BETWEEN '08:41' AND '11:00')
 )
ERROR - 2025-06-20 18:02:53 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-20'
AND   (
('11:30' BETWEEN start_time AND end_time)
OR ('13:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:30' AND '13:45')
OR (end_time BETWEEN '11:30' AND '13:45')
 )
ERROR - 2025-06-20 18:04:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-20'
AND   (
('14:20' BETWEEN start_time AND end_time)
OR ('18:03' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:20' AND '18:03')
OR (end_time BETWEEN '14:20' AND '18:03')
 )
ERROR - 2025-06-20 18:06:05 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-20'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('10:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '10:30')
OR (end_time BETWEEN '09:30' AND '10:30')
 )
ERROR - 2025-06-20 18:07:49 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-20'
AND   (
('09:27' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:27' AND '11:00')
OR (end_time BETWEEN '09:27' AND '11:00')
 )
ERROR - 2025-06-20 18:09:08 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-20'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('12:34' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '12:34')
OR (end_time BETWEEN '11:20' AND '12:34')
 )
ERROR - 2025-06-20 18:11:16 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-20'
AND   (
('11:55' BETWEEN start_time AND end_time)
OR ('13:35' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:55' AND '13:35')
OR (end_time BETWEEN '11:55' AND '13:35')
 )
ERROR - 2025-06-20 18:12:49 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-20'
AND   (
('13:20' BETWEEN start_time AND end_time)
OR ('13:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:20' AND '13:50')
OR (end_time BETWEEN '13:20' AND '13:50')
 )
ERROR - 2025-06-20 18:16:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-20'
AND   (
('11:00' BETWEEN start_time AND end_time)
OR ('12:40' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:00' AND '12:40')
OR (end_time BETWEEN '11:00' AND '12:40')
 )
ERROR - 2025-06-20 18:16:40 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-20'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('18:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '18:15')
OR (end_time BETWEEN '14:40' AND '18:15')
 )
ERROR - 2025-06-20 18:19:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-20'
AND   (
('14:35' BETWEEN start_time AND end_time)
OR ('18:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:35' AND '18:20')
OR (end_time BETWEEN '14:35' AND '18:20')
 )
ERROR - 2025-06-20 18:21:02 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-20'
AND   (
('14:00' BETWEEN start_time AND end_time)
OR ('15:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:00' AND '15:00')
OR (end_time BETWEEN '14:00' AND '15:00')
 )
ERROR - 2025-06-20 18:26:54 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-20 18:27:30 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-20'
AND   (
('15:01' BETWEEN start_time AND end_time)
OR ('17:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:01' AND '17:30')
OR (end_time BETWEEN '15:01' AND '17:30')
 )
ERROR - 2025-06-20 18:28:48 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-20'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('12:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '12:30')
OR (end_time BETWEEN '09:30' AND '12:30')
 )
ERROR - 2025-06-20 18:30:37 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-20'
AND   (
('13:10' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:10' AND '14:00')
OR (end_time BETWEEN '13:10' AND '14:00')
 )
ERROR - 2025-06-20 18:31:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-20'
AND   (
('09:34' BETWEEN start_time AND end_time)
OR ('10:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:34' AND '10:45')
OR (end_time BETWEEN '09:34' AND '10:45')
 )
ERROR - 2025-06-20 18:32:33 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-20'
AND   (
('11:00' BETWEEN start_time AND end_time)
OR ('12:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:00' AND '12:00')
OR (end_time BETWEEN '11:00' AND '12:00')
 )
ERROR - 2025-06-20 18:32:55 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-20 18:34:02 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-20'
AND   (
('12:02' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:02' AND '14:00')
OR (end_time BETWEEN '12:02' AND '14:00')
 )
ERROR - 2025-06-20 18:40:43 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-20'
AND   (
('15:00' BETWEEN start_time AND end_time)
OR ('18:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:00' AND '18:30')
OR (end_time BETWEEN '15:00' AND '18:30')
 )
ERROR - 2025-06-20 18:41:19 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-20'
AND   (
('09:31' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:31' AND '11:00')
OR (end_time BETWEEN '09:31' AND '11:00')
 )
ERROR - 2025-06-20 18:43:14 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-20'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('12:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '12:30')
OR (end_time BETWEEN '11:20' AND '12:30')
 )
ERROR - 2025-06-20 18:45:08 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-20'
AND   (
('13:44' BETWEEN start_time AND end_time)
OR ('14:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:44' AND '14:30')
OR (end_time BETWEEN '13:44' AND '14:30')
 )
ERROR - 2025-06-20 18:45:41 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-20'
AND   (
('14:32' BETWEEN start_time AND end_time)
OR ('18:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:32' AND '18:45')
OR (end_time BETWEEN '14:32' AND '18:45')
 )
ERROR - 2025-06-20 18:46:20 --> [{"task_id":8224,"start_time":"09:30","end_time":"09:45","time_taken":"00:15:00","remarks":"<p>Punch record push section was successfully updated.<\/p>"},{"task_id":12381,"start_time":"09:46","end_time":"10:30","time_taken":"00:44:00","remarks":"<p>Working on admin panel and API support tasks.<\/p>"}]
ERROR - 2025-06-20 18:52:09 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-20'
AND   (
('14:21' BETWEEN start_time AND end_time)
OR ('18:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:21' AND '18:50')
OR (end_time BETWEEN '14:21' AND '18:50')
 )
ERROR - 2025-06-20 18:52:30 --> [{"task_id":12985,"start_time":"10:45","end_time":"11:45","time_taken":"01:00:00","remarks":"<p>Worked on admin panel support.<\/p>"},{"task_id":12381,"start_time":"11:46","end_time":"12:30","time_taken":"00:44:00","remarks":"<p>Completed the Live class API.<\/p>"},{"task_id":12381,"start_time":"13:15","end_time":"13:30","time_taken":"00:15:00","remarks":"<p>Working on the assignment section.<\/p>"},{"task_id":12381,"start_time":"14:25","end_time":"17:30","time_taken":"03:05:00","remarks":"<p>Assignment related APIs and support work completed.<\/p>"},{"task_id":12562,"start_time":"17:31","end_time":"18:52","time_taken":"01:21:00","remarks":"<p>Meeting and exam section updation completed.<\/p>"}]
ERROR - 2025-06-20 19:05:51 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-20'
AND   (
('09:36' BETWEEN start_time AND end_time)
OR ('23:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:36' AND '23:00')
OR (end_time BETWEEN '09:36' AND '23:00')
 )
ERROR - 2025-06-20 19:06:24 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-20'
AND   (
('09:36' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:36' AND '11:00')
OR (end_time BETWEEN '09:36' AND '11:00')
 )
ERROR - 2025-06-20 19:07:21 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-20'
AND   (
('11:01' BETWEEN start_time AND end_time)
OR ('12:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:01' AND '12:30')
OR (end_time BETWEEN '11:01' AND '12:30')
 )
ERROR - 2025-06-20 19:07:56 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-20'
AND   (
('14:30' BETWEEN start_time AND end_time)
OR ('19:07' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:30' AND '19:07')
OR (end_time BETWEEN '14:30' AND '19:07')
 )
ERROR - 2025-06-20 22:41:08 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-20'
AND   (
('09:24' BETWEEN start_time AND end_time)
OR ('10:59' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:24' AND '10:59')
OR (end_time BETWEEN '09:24' AND '10:59')
 )
ERROR - 2025-06-20 22:45:38 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-20'
AND   (
('11:21' BETWEEN start_time AND end_time)
OR ('13:21' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:21' AND '13:21')
OR (end_time BETWEEN '11:21' AND '13:21')
 )
ERROR - 2025-06-20 22:46:23 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-20'
AND   (
('11:21' BETWEEN start_time AND end_time)
OR ('13:21' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:21' AND '13:21')
OR (end_time BETWEEN '11:21' AND '13:21')
 )
ERROR - 2025-06-20 22:54:22 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-20'
AND   (
('13:22' BETWEEN start_time AND end_time)
OR ('14:12' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:22' AND '14:12')
OR (end_time BETWEEN '13:22' AND '14:12')
 )
ERROR - 2025-06-20 22:55:52 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-20'
AND   (
('14:35' BETWEEN start_time AND end_time)
OR ('17:48' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:35' AND '17:48')
OR (end_time BETWEEN '14:35' AND '17:48')
 )
ERROR - 2025-06-20 22:57:04 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-20'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:00')
OR (end_time BETWEEN '09:30' AND '11:00')
 )
ERROR - 2025-06-20 22:59:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-20'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '14:00')
OR (end_time BETWEEN '11:20' AND '14:00')
 )
ERROR - 2025-06-20 22:59:59 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-20'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('17:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '17:50')
OR (end_time BETWEEN '14:40' AND '17:50')
 )
ERROR - 2025-06-20 23:06:01 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-20'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:20')
OR (end_time BETWEEN '09:30' AND '11:20')
 )
ERROR - 2025-06-20 23:07:17 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-20'
AND   (
('11:40' BETWEEN start_time AND end_time)
OR ('12:40' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:40' AND '12:40')
OR (end_time BETWEEN '11:40' AND '12:40')
 )
ERROR - 2025-06-20 23:09:13 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-20'
AND   (
('13:30' BETWEEN start_time AND end_time)
OR ('17:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:30' AND '17:45')
OR (end_time BETWEEN '13:30' AND '17:45')
 )
ERROR - 2025-06-20 23:10:38 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-20'
AND   (
('13:30' BETWEEN start_time AND end_time)
OR ('17:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:30' AND '17:45')
OR (end_time BETWEEN '13:30' AND '17:45')
 )
ERROR - 2025-06-20 23:12:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-20'
AND   (
('20:50' BETWEEN start_time AND end_time)
OR ('23:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '20:50' AND '23:05')
OR (end_time BETWEEN '20:50' AND '23:05')
 )
