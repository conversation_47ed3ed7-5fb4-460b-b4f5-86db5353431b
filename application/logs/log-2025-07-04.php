<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-07-04 06:46:19 --> Array
(
    [0] => Array
        (
            [log_time] => 09:15:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:39:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:50:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:48:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:15:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 06:46:19
        )

)

ERROR - 2025-07-04 06:46:19 --> -11201
ERROR - 2025-07-04 08:31:14 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 08:31:14 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 08:31:14 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 08:31:14 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 08:31:14 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 08:41:17 --> Array
(
    [0] => Array
        (
            [log_time] => 09:15:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:39:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:50:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:48:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:15:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 08:41:17
        )

)

ERROR - 2025-07-04 08:41:17 --> -4303
ERROR - 2025-07-04 09:11:16 --> Array
(
    [0] => Array
        (
            [log_time] => 09:22:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:26:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:11:16
        )

)

ERROR - 2025-07-04 09:11:16 --> -2684
ERROR - 2025-07-04 09:11:51 --> Array
(
    [0] => Array
        (
            [log_time] => 09:22:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:26:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:11:51
        )

)

ERROR - 2025-07-04 09:11:51 --> -2649
ERROR - 2025-07-04 09:11:51 --> Array
(
    [0] => Array
        (
            [log_time] => 09:22:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:26:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:11:51
        )

)

ERROR - 2025-07-04 09:11:51 --> -2649
ERROR - 2025-07-04 09:28:35 --> Array
(
    [0] => Array
        (
            [log_time] => 09:26:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:08:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:28:35
        )

)

ERROR - 2025-07-04 09:28:35 --> -2785
ERROR - 2025-07-04 09:30:01 --> Array
(
    [0] => Array
        (
            [log_time] => 09:26:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:08:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:30:01
        )

)

ERROR - 2025-07-04 09:30:01 --> -2699
ERROR - 2025-07-04 09:31:49 --> Array
(
    [0] => Array
        (
            [log_time] => 09:26:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:08:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:31:49
        )

)

ERROR - 2025-07-04 09:31:49 --> -2591
ERROR - 2025-07-04 09:34:26 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:12:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:38:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:05:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:51:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:34:26
        )

)

ERROR - 2025-07-04 09:34:26 --> -3754
ERROR - 2025-07-04 09:36:34 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:18:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:04:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:34
        )

)

ERROR - 2025-07-04 09:36:34 --> -4226
ERROR - 2025-07-04 09:38:57 --> Array
(
    [0] => Array
        (
            [log_time] => 09:15:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:39:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:50:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:48:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:15:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:38:57
        )

)

ERROR - 2025-07-04 09:38:57 --> -843
ERROR - 2025-07-04 09:41:59 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:05:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:41:59
        )

)

ERROR - 2025-07-04 09:41:59 --> -4081
ERROR - 2025-07-04 09:43:21 --> Array
(
    [0] => Array
        (
            [log_time] => 09:50:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:26:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:43:21
        )

)

ERROR - 2025-07-04 09:43:21 --> -2439
ERROR - 2025-07-04 09:43:58 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:17:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:57:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:43:58
        )

)

ERROR - 2025-07-04 09:43:58 --> -3062
ERROR - 2025-07-04 09:47:02 --> Array
(
    [0] => Array
        (
            [log_time] => 09:24:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:10:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:55:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:47:02
        )

)

ERROR - 2025-07-04 09:47:02 --> -2938
ERROR - 2025-07-04 09:52:53 --> Array
(
    [0] => Array
        (
            [log_time] => 09:42:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:17:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:51:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:57:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:55:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:52:53
        )

)

ERROR - 2025-07-04 09:52:53 --> -4867
ERROR - 2025-07-04 09:54:29 --> Array
(
    [0] => Array
        (
            [log_time] => 09:40:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:29:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:52:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:09:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:54:29
        )

)

ERROR - 2025-07-04 09:54:29 --> -3331
ERROR - 2025-07-04 10:07:59 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:18:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:04:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:07:59
        )

)

ERROR - 2025-07-04 10:07:59 --> -2341
ERROR - 2025-07-04 11:50:02 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 11:50:02 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 11:50:02 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 11:50:02 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 11:50:02 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 11:50:06 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 11:50:06 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 11:50:06 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 11:50:06 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 11:50:06 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-07-04 12:51:13 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 5 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
ERROR - 2025-07-04 12:51:35 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 6 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
AND (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) <= '0004-04-05)'
ERROR - 2025-07-04 12:51:40 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 5 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
ERROR - 2025-07-04 12:51:46 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 6 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
AND (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) <= '0004-07-05)'
ERROR - 2025-07-04 12:51:53 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 5 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
ERROR - 2025-07-04 12:51:57 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 6 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
AND (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) <= '0004-07-04)'
ERROR - 2025-07-04 12:52:02 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 5 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
ERROR - 2025-07-04 12:52:06 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 5 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
ERROR - 2025-07-04 12:52:21 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 5 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
ERROR - 2025-07-04 12:52:29 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 5 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
ERROR - 2025-07-04 12:52:33 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 5 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
ERROR - 2025-07-04 13:05:22 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 13 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
AND   (
`tickets`.`title` LIKE '%p%' ESCAPE '!'
OR  `tickets`.`description` LIKE '%p%' ESCAPE '!'
OR  `projects`.`title` LIKE '%p%' ESCAPE '!'
OR  `users`.`name` LIKE '%p%' ESCAPE '!'
OR  `tickets`.`reported_by` LIKE '%p%' ESCAPE '!'
OR  CONCAT("TKT", tickets.id) LIKE '%p%' ESCAPE '!'
 )
ERROR - 2025-07-04 13:05:26 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 5 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
ERROR - 2025-07-04 13:05:35 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 13 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
AND   (
`tickets`.`title` LIKE '%p%' ESCAPE '!'
OR  `tickets`.`description` LIKE '%p%' ESCAPE '!'
OR  `projects`.`title` LIKE '%p%' ESCAPE '!'
OR  `users`.`name` LIKE '%p%' ESCAPE '!'
OR  `tickets`.`reported_by` LIKE '%p%' ESCAPE '!'
OR  CONCAT("TKT", tickets.id) LIKE '%p%' ESCAPE '!'
 )
ERROR - 2025-07-04 13:09:43 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 5 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
ERROR - 2025-07-04 13:09:51 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 6 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-07-01)'
AND (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) <= '0002-07-04)'
ERROR - 2025-07-04 13:10:55 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 5 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '2025-07-01)'
ERROR - 2025-07-04 13:11:01 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 6 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '2025-07-01)'
AND (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) <= '2025-07-04)'
ERROR - 2025-07-04 13:11:04 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 6 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '2025-07-01)'
AND (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) <= '2025-07-04)'
ERROR - 2025-07-04 13:11:14 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 6 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '2025-07-01)'
AND (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) <= '2025-07-04)'
ERROR - 2025-07-04 13:11:22 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 6 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '2025-07-01)'
AND (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) <= '2025-07-04)'
ERROR - 2025-07-04 13:11:26 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 6 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '2025-07-01)'
AND (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) <= '2025-07-04)'
ERROR - 2025-07-04 13:11:30 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 6 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '2025-07-01)'
AND (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) <= '2025-07-04)'
ERROR - 2025-07-04 13:11:34 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 6 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '2025-07-01)'
AND (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) <= '2025-07-04)'
