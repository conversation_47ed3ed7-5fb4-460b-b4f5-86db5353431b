<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-19 09:18:29 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:18:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:18:29
        )

)

ERROR - 2025-06-19 09:18:29 --> -3511
ERROR - 2025-06-19 09:18:44 --> <PERSON>rray
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:18:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:18:44
        )

)

ERROR - 2025-06-19 09:18:44 --> -3496
ERROR - 2025-06-19 09:27:22 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:27:22
        )

)

ERROR - 2025-06-19 09:27:22 --> -4418
ERROR - 2025-06-19 09:33:12 --> Array
(
    [0] => Array
        (
            [log_time] => 09:29:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:57:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:07:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:33:12
        )

)

ERROR - 2025-06-19 09:33:12 --> -4308
ERROR - 2025-06-19 09:35:19 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:18:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:48:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:05:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:35:19
        )

)

ERROR - 2025-06-19 09:35:19 --> -4661
ERROR - 2025-06-19 09:35:25 --> Array
(
    [0] => Array
        (
            [log_time] => 12:09:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:35:25
        )

)

ERROR - 2025-06-19 09:35:25 --> -12215
ERROR - 2025-06-19 09:35:29 --> Array
(
    [0] => Array
        (
            [log_time] => 12:09:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:35:29
        )

)

ERROR - 2025-06-19 09:35:29 --> -12211
ERROR - 2025-06-19 09:35:32 --> Array
(
    [0] => Array
        (
            [log_time] => 12:09:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:35:32
        )

)

ERROR - 2025-06-19 09:35:32 --> -12208
ERROR - 2025-06-19 09:35:37 --> Array
(
    [0] => Array
        (
            [log_time] => 12:09:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:35:37
        )

)

ERROR - 2025-06-19 09:35:37 --> -12203
ERROR - 2025-06-19 09:37:02 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:59:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:37:02
        )

)

ERROR - 2025-06-19 09:37:02 --> -2818
ERROR - 2025-06-19 09:37:04 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:59:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:37:04
        )

)

ERROR - 2025-06-19 09:37:04 --> -2816
ERROR - 2025-06-19 09:40:48 --> Array
(
    [0] => Array
        (
            [log_time] => 09:31:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:32:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:48
        )

)

ERROR - 2025-06-19 09:40:48 --> -2772
ERROR - 2025-06-19 09:40:51 --> Array
(
    [0] => Array
        (
            [log_time] => 09:31:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:32:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:51
        )

)

ERROR - 2025-06-19 09:40:51 --> -2769
ERROR - 2025-06-19 09:40:55 --> Array
(
    [0] => Array
        (
            [log_time] => 09:31:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:32:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:55
        )

)

ERROR - 2025-06-19 09:40:55 --> -2765
ERROR - 2025-06-19 09:42:11 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:59:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:42:11
        )

)

ERROR - 2025-06-19 09:42:11 --> -2209
ERROR - 2025-06-19 09:44:07 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:44:07
        )

)

ERROR - 2025-06-19 09:44:07 --> -2693
ERROR - 2025-06-19 09:44:28 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:44:28
        )

)

ERROR - 2025-06-19 09:44:28 --> -2672
ERROR - 2025-06-19 09:44:56 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:44:56
        )

)

ERROR - 2025-06-19 09:44:56 --> -2644
ERROR - 2025-06-19 09:48:59 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:48:59
        )

)

ERROR - 2025-06-19 09:48:59 --> -2401
ERROR - 2025-06-19 09:50:14 --> Array
(
    [0] => Array
        (
            [log_time] => 09:35:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:50:14
        )

)

ERROR - 2025-06-19 09:50:14 --> -3526
ERROR - 2025-06-19 09:52:08 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:52:08
        )

)

ERROR - 2025-06-19 09:52:08 --> -2872
ERROR - 2025-06-19 09:54:29 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:54:29
        )

)

ERROR - 2025-06-19 09:54:29 --> -2071
ERROR - 2025-06-19 09:54:37 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:54:37
        )

)

ERROR - 2025-06-19 09:54:37 --> -2063
ERROR - 2025-06-19 09:54:49 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:54:49
        )

)

ERROR - 2025-06-19 09:54:49 --> -2051
ERROR - 2025-06-19 10:17:14 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:17:14
        )

)

ERROR - 2025-06-19 10:17:14 --> -706
ERROR - 2025-06-19 10:28:12 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:19:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:43:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:45:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:28:12
        )

)

ERROR - 2025-06-19 10:28:12 --> -48
ERROR - 2025-06-19 10:38:08 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-19'
AND   (
('09:25' BETWEEN start_time AND end_time)
OR ('10:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:25' AND '10:10')
OR (end_time BETWEEN '09:25' AND '10:10')
 )
ERROR - 2025-06-19 10:38:08 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:38:08
        )

)

ERROR - 2025-06-19 10:38:08 --> -172
ERROR - 2025-06-19 10:38:41 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 10:38:41 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:38:41
        )

)

ERROR - 2025-06-19 10:38:41 --> -139
ERROR - 2025-06-19 10:40:26 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:20:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:08:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:56:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:40:26
        )

)

ERROR - 2025-06-19 10:40:26 --> -34
ERROR - 2025-06-19 10:41:04 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-19'
AND   (
('10:11' BETWEEN start_time AND end_time)
OR ('10:41' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:11' AND '10:41')
OR (end_time BETWEEN '10:11' AND '10:41')
 )
ERROR - 2025-06-19 11:52:26 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 11:55:33 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-19'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:20')
OR (end_time BETWEEN '09:30' AND '11:20')
 )
ERROR - 2025-06-19 11:59:30 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 11:59:47 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-19'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:17' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:17')
OR (end_time BETWEEN '09:30' AND '11:17')
 )
ERROR - 2025-06-19 12:27:05 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-19'
AND   (
('10:42' BETWEEN start_time AND end_time)
OR ('11:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:42' AND '11:10')
OR (end_time BETWEEN '10:42' AND '11:10')
 )
ERROR - 2025-06-19 13:53:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-19'
AND   (
('11:44' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:44' AND '14:00')
OR (end_time BETWEEN '11:44' AND '14:00')
 )
ERROR - 2025-06-19 14:37:19 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-19'
AND   (
('11:40' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:40' AND '14:00')
OR (end_time BETWEEN '11:40' AND '14:00')
 )
ERROR - 2025-06-19 14:45:16 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-19'
AND   (
('11:47' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:47' AND '14:00')
OR (end_time BETWEEN '11:47' AND '14:00')
 )
ERROR - 2025-06-19 14:45:51 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-19'
AND   (
('14:35' BETWEEN start_time AND end_time)
OR ('17:35' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:35' AND '17:35')
OR (end_time BETWEEN '14:35' AND '17:35')
 )
ERROR - 2025-06-19 17:38:06 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 17:38:23 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 17:38:26 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 17:39:07 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 17:39:23 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-19'
AND   (
('09:36' BETWEEN start_time AND end_time)
OR ('11:16' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:36' AND '11:16')
OR (end_time BETWEEN '09:36' AND '11:16')
 )
ERROR - 2025-06-19 17:40:19 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-19'
AND   (
('11:46' BETWEEN start_time AND end_time)
OR ('14:04' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:46' AND '14:04')
OR (end_time BETWEEN '11:46' AND '14:04')
 )
ERROR - 2025-06-19 17:45:35 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-19'
AND   (
('14:41' BETWEEN start_time AND end_time)
OR ('17:47' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:41' AND '17:47')
OR (end_time BETWEEN '14:41' AND '17:47')
 )
ERROR - 2025-06-19 17:46:00 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-19'
AND   (
('09:47' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:47' AND '11:15')
OR (end_time BETWEEN '09:47' AND '11:15')
 )
ERROR - 2025-06-19 17:47:15 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-19'
AND   (
('11:30' BETWEEN start_time AND end_time)
OR ('14:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:30' AND '14:05')
OR (end_time BETWEEN '11:30' AND '14:05')
 )
ERROR - 2025-06-19 17:48:04 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-19'
AND   (
('14:55' BETWEEN start_time AND end_time)
OR ('15:25' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:55' AND '15:25')
OR (end_time BETWEEN '14:55' AND '15:25')
 )
ERROR - 2025-06-19 17:48:46 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 17:55:08 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-19'
AND   (
('09:47' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:47' AND '11:15')
OR (end_time BETWEEN '09:47' AND '11:15')
 )
ERROR - 2025-06-19 17:56:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-19'
AND   (
('14:50' BETWEEN start_time AND end_time)
OR ('18:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:50' AND '18:00')
OR (end_time BETWEEN '14:50' AND '18:00')
 )
ERROR - 2025-06-19 17:56:11 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-19'
AND   (
('11:45' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:45' AND '14:00')
OR (end_time BETWEEN '11:45' AND '14:00')
 )
ERROR - 2025-06-19 17:57:34 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 17:57:40 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-19'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:00')
OR (end_time BETWEEN '09:30' AND '11:00')
 )
ERROR - 2025-06-19 17:58:36 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-19'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('17:31' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '17:31')
OR (end_time BETWEEN '14:40' AND '17:31')
 )
ERROR - 2025-06-19 17:59:09 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-19'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:00')
OR (end_time BETWEEN '09:30' AND '11:00')
 )
ERROR - 2025-06-19 18:00:02 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-19'
AND   (
('09:33' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:33' AND '11:00')
OR (end_time BETWEEN '09:33' AND '11:00')
 )
ERROR - 2025-06-19 18:00:11 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-19'
AND   (
('11:25' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:25' AND '14:00')
OR (end_time BETWEEN '11:25' AND '14:00')
 )
ERROR - 2025-06-19 18:00:56 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-19'
AND   (
('09:55' BETWEEN start_time AND end_time)
OR ('11:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:55' AND '11:30')
OR (end_time BETWEEN '09:55' AND '11:30')
 )
ERROR - 2025-06-19 18:01:34 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-19'
AND   (
('11:15' BETWEEN start_time AND end_time)
OR ('12:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:15' AND '12:00')
OR (end_time BETWEEN '11:15' AND '12:00')
 )
ERROR - 2025-06-19 18:02:02 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-19'
AND   (
('11:50' BETWEEN start_time AND end_time)
OR ('13:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:50' AND '13:30')
OR (end_time BETWEEN '11:50' AND '13:30')
 )
ERROR - 2025-06-19 18:02:56 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-19'
AND   (
('12:01' BETWEEN start_time AND end_time)
OR ('12:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:01' AND '12:30')
OR (end_time BETWEEN '12:01' AND '12:30')
 )
ERROR - 2025-06-19 18:03:14 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-19'
AND   (
('14:15' BETWEEN start_time AND end_time)
OR ('18:02' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:15' AND '18:02')
OR (end_time BETWEEN '14:15' AND '18:02')
 )
ERROR - 2025-06-19 18:03:47 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-19'
AND   (
('14:45' BETWEEN start_time AND end_time)
OR ('18:03' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:45' AND '18:03')
OR (end_time BETWEEN '14:45' AND '18:03')
 )
ERROR - 2025-06-19 18:03:51 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-19'
AND   (
('12:31' BETWEEN start_time AND end_time)
OR ('14:03' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:31' AND '14:03')
OR (end_time BETWEEN '12:31' AND '14:03')
 )
ERROR - 2025-06-19 18:04:28 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-19'
AND   (
('14:55' BETWEEN start_time AND end_time)
OR ('17:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:55' AND '17:20')
OR (end_time BETWEEN '14:55' AND '17:20')
 )
ERROR - 2025-06-19 18:04:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-19'
AND   (
('14:45' BETWEEN start_time AND end_time)
OR ('16:04' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:45' AND '16:04')
OR (end_time BETWEEN '14:45' AND '16:04')
 )
ERROR - 2025-06-19 18:06:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-19'
AND   (
('16:05' BETWEEN start_time AND end_time)
OR ('16:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:05' AND '16:30')
OR (end_time BETWEEN '16:05' AND '16:30')
 )
ERROR - 2025-06-19 18:07:10 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 18:07:14 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-19'
AND   (
('09:35' BETWEEN start_time AND end_time)
OR ('11:21' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:35' AND '11:21')
OR (end_time BETWEEN '09:35' AND '11:21')
 )
ERROR - 2025-06-19 18:07:48 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-19'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '14:00')
OR (end_time BETWEEN '11:20' AND '14:00')
 )
ERROR - 2025-06-19 18:07:49 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-19'
AND   (
('09:27' BETWEEN start_time AND end_time)
OR ('11:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:27' AND '11:20')
OR (end_time BETWEEN '09:27' AND '11:20')
 )
ERROR - 2025-06-19 18:07:49 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-19'
AND   (
('16:31' BETWEEN start_time AND end_time)
OR ('17:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:31' AND '17:00')
OR (end_time BETWEEN '16:31' AND '17:00')
 )
ERROR - 2025-06-19 18:08:16 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 18:08:50 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-19'
AND   (
('17:08' BETWEEN start_time AND end_time)
OR ('18:08' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:08' AND '18:08')
OR (end_time BETWEEN '17:08' AND '18:08')
 )
ERROR - 2025-06-19 18:09:24 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-19'
AND   (
('11:45' BETWEEN start_time AND end_time)
OR ('14:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:45' AND '14:05')
OR (end_time BETWEEN '11:45' AND '14:05')
 )
ERROR - 2025-06-19 18:09:24 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-19'
AND   (
('09:24' BETWEEN start_time AND end_time)
OR ('11:17' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:24' AND '11:17')
OR (end_time BETWEEN '09:24' AND '11:17')
 )
ERROR - 2025-06-19 18:09:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-19'
AND   (
('11:40' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:40' AND '14:00')
OR (end_time BETWEEN '11:40' AND '14:00')
 )
ERROR - 2025-06-19 18:11:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-19'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:15')
OR (end_time BETWEEN '09:30' AND '11:15')
 )
ERROR - 2025-06-19 18:11:11 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-19'
AND   (
('14:35' BETWEEN start_time AND end_time)
OR ('18:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:35' AND '18:10')
OR (end_time BETWEEN '14:35' AND '18:10')
 )
ERROR - 2025-06-19 18:11:42 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-19'
AND   (
('14:09' BETWEEN start_time AND end_time)
OR ('14:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:09' AND '14:30')
OR (end_time BETWEEN '14:09' AND '14:30')
 )
ERROR - 2025-06-19 18:12:39 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 18:12:59 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-19'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('18:11' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '18:11')
OR (end_time BETWEEN '14:40' AND '18:11')
 )
ERROR - 2025-06-19 18:14:12 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-19'
AND   (
('11:47' BETWEEN start_time AND end_time)
OR ('13:58' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:47' AND '13:58')
OR (end_time BETWEEN '11:47' AND '13:58')
 )
ERROR - 2025-06-19 18:16:35 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-19'
AND   (
('14:32' BETWEEN start_time AND end_time)
OR ('18:13' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:32' AND '18:13')
OR (end_time BETWEEN '14:32' AND '18:13')
 )
ERROR - 2025-06-19 18:17:17 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-19'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('18:17' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '18:17')
OR (end_time BETWEEN '14:40' AND '18:17')
 )
ERROR - 2025-06-19 18:17:52 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-19'
AND   (
('11:30' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:30' AND '14:00')
OR (end_time BETWEEN '11:30' AND '14:00')
 )
ERROR - 2025-06-19 18:18:32 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-19'
AND   (
('15:00' BETWEEN start_time AND end_time)
OR ('16:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:00' AND '16:00')
OR (end_time BETWEEN '15:00' AND '16:00')
 )
ERROR - 2025-06-19 18:18:51 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-19'
AND   (
('11:30' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:30' AND '14:00')
OR (end_time BETWEEN '11:30' AND '14:00')
 )
ERROR - 2025-06-19 18:19:50 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-19'
AND   (
('14:45' BETWEEN start_time AND end_time)
OR ('15:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:45' AND '15:30')
OR (end_time BETWEEN '14:45' AND '15:30')
 )
ERROR - 2025-06-19 18:19:59 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-19'
AND   (
('09:47' BETWEEN start_time AND end_time)
OR ('11:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:47' AND '11:10')
OR (end_time BETWEEN '09:47' AND '11:10')
 )
ERROR - 2025-06-19 18:21:11 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-19'
AND   (
('16:01' BETWEEN start_time AND end_time)
OR ('18:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:01' AND '18:10')
OR (end_time BETWEEN '16:01' AND '18:10')
 )
ERROR - 2025-06-19 18:22:13 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-19'
AND   (
('15:35' BETWEEN start_time AND end_time)
OR ('17:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:35' AND '17:30')
OR (end_time BETWEEN '15:35' AND '17:30')
 )
ERROR - 2025-06-19 18:22:56 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-19'
AND   (
('15:26' BETWEEN start_time AND end_time)
OR ('18:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:26' AND '18:15')
OR (end_time BETWEEN '15:26' AND '18:15')
 )
ERROR - 2025-06-19 18:24:01 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-19'
AND   (
('17:35' BETWEEN start_time AND end_time)
OR ('18:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:35' AND '18:20')
OR (end_time BETWEEN '17:35' AND '18:20')
 )
ERROR - 2025-06-19 18:27:08 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 18:27:41 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-19'
AND   (
('16:30' BETWEEN start_time AND end_time)
OR ('17:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:30' AND '17:30')
OR (end_time BETWEEN '16:30' AND '17:30')
 )
ERROR - 2025-06-19 18:28:13 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 18:28:43 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-19'
AND   (
('17:31' BETWEEN start_time AND end_time)
OR ('18:28' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:31' AND '18:28')
OR (end_time BETWEEN '17:31' AND '18:28')
 )
ERROR - 2025-06-19 18:28:57 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 18:29:24 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-19'
AND   (
('15:30' BETWEEN start_time AND end_time)
OR ('16:29' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:30' AND '16:29')
OR (end_time BETWEEN '15:30' AND '16:29')
 )
ERROR - 2025-06-19 18:29:49 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-19 18:30:30 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-19'
AND   (
('09:31' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:31' AND '11:15')
OR (end_time BETWEEN '09:31' AND '11:15')
 )
ERROR - 2025-06-19 18:31:05 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-19'
AND   (
('11:30' BETWEEN start_time AND end_time)
OR ('14:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:30' AND '14:30')
OR (end_time BETWEEN '11:30' AND '14:30')
 )
ERROR - 2025-06-19 22:04:51 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-19'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:17' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:17')
OR (end_time BETWEEN '09:30' AND '11:17')
 )
ERROR - 2025-06-19 22:10:02 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-19'
AND   (
('11:45' BETWEEN start_time AND end_time)
OR ('13:58' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:45' AND '13:58')
OR (end_time BETWEEN '11:45' AND '13:58')
 )
ERROR - 2025-06-19 22:13:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-19'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('18:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '18:00')
OR (end_time BETWEEN '14:40' AND '18:00')
 )
ERROR - 2025-06-19 23:24:05 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-19'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:00')
OR (end_time BETWEEN '09:30' AND '11:00')
 )
ERROR - 2025-06-19 23:25:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-19'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:00')
OR (end_time BETWEEN '09:30' AND '11:00')
 )
ERROR - 2025-06-19 23:25:58 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-19'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '14:00')
OR (end_time BETWEEN '11:20' AND '14:00')
 )
ERROR - 2025-06-19 23:26:32 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-19'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('17:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '17:50')
OR (end_time BETWEEN '14:40' AND '17:50')
 )
ERROR - 2025-06-19 23:46:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-19'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:30')
OR (end_time BETWEEN '09:30' AND '11:30')
 )
ERROR - 2025-06-19 23:48:45 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-19'
AND   (
('11:50' BETWEEN start_time AND end_time)
OR ('13:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:50' AND '13:50')
OR (end_time BETWEEN '11:50' AND '13:50')
 )
ERROR - 2025-06-19 23:50:53 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-19'
AND   (
('14:25' BETWEEN start_time AND end_time)
OR ('18:25' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:25' AND '18:25')
OR (end_time BETWEEN '14:25' AND '18:25')
 )
ERROR - 2025-06-19 23:52:31 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-19'
AND   (
('22:00' BETWEEN start_time AND end_time)
OR ('23:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '22:00' AND '23:30')
OR (end_time BETWEEN '22:00' AND '23:30')
 )
