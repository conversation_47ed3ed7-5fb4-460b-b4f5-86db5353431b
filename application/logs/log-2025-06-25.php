<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-25 06:11:39 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:49:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 06:11:39
        )

)

ERROR - 2025-06-25 06:11:39 --> -14481
ERROR - 2025-06-25 06:12:23 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:49:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 06:12:23
        )

)

ERROR - 2025-06-25 06:12:23 --> -14437
ERROR - 2025-06-25 06:14:11 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:49:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 06:14:11
        )

)

ERROR - 2025-06-25 06:14:11 --> -14329
ERROR - 2025-06-25 06:27:12 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:49:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 06:27:12
        )

)

ERROR - 2025-06-25 06:27:12 --> -13548
ERROR - 2025-06-25 09:08:20 --> Array
(
    [0] => Array
        (
            [log_time] => 09:07:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:10:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:03:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:08:20
        )

)

ERROR - 2025-06-25 09:08:20 --> -3100
ERROR - 2025-06-25 09:16:36 --> Array
(
    [0] => Array
        (
            [log_time] => 09:49:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:47:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:59:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:03:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:16:36
        )

)

ERROR - 2025-06-25 09:16:36 --> -4104
ERROR - 2025-06-25 09:16:49 --> Array
(
    [0] => Array
        (
            [log_time] => 09:49:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:47:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:59:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:03:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:16:49
        )

)

ERROR - 2025-06-25 09:16:49 --> -4091
ERROR - 2025-06-25 09:35:21 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:00:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:24:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:43:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:35:21
        )

)

ERROR - 2025-06-25 09:35:21 --> -3759
ERROR - 2025-06-25 09:35:46 --> Array
(
    [0] => Array
        (
            [log_time] => 09:29:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:27:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:51:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 16:27:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:35:46
        )

)

ERROR - 2025-06-25 09:35:46 --> -10514
ERROR - 2025-06-25 09:36:47 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:59:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:25:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:04:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:44:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:47
        )

)

ERROR - 2025-06-25 09:36:47 --> -3973
ERROR - 2025-06-25 09:36:47 --> Array
(
    [0] => Array
        (
            [log_time] => 10:02:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:48:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:52:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:47
        )

)

ERROR - 2025-06-25 09:36:47 --> -6673
ERROR - 2025-06-25 09:36:52 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:52
        )

)

ERROR - 2025-06-25 09:36:52 --> -3788
ERROR - 2025-06-25 09:36:52 --> Array
(
    [0] => Array
        (
            [log_time] => 09:24:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:46:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:20:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:43:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:52
        )

)

ERROR - 2025-06-25 09:36:52 --> -3728
ERROR - 2025-06-25 09:36:53 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:49:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:36:53
        )

)

ERROR - 2025-06-25 09:36:53 --> -2167
ERROR - 2025-06-25 09:38:16 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:49:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:38:16
        )

)

ERROR - 2025-06-25 09:38:16 --> -2084
ERROR - 2025-06-25 09:38:47 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:01:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:49:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:38:47
        )

)

ERROR - 2025-06-25 09:38:47 --> -2053
ERROR - 2025-06-25 09:44:11 --> Array
(
    [0] => Array
        (
            [log_time] => 09:05:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 11:37:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 14:09:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:44:11
        )

)

ERROR - 2025-06-25 09:44:11 --> -529
ERROR - 2025-06-25 09:44:38 --> Array
(
    [0] => Array
        (
            [log_time] => 09:05:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 11:37:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 14:09:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:44:38
        )

)

ERROR - 2025-06-25 09:44:38 --> -502
ERROR - 2025-06-25 09:44:49 --> Array
(
    [0] => Array
        (
            [log_time] => 09:05:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 11:37:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 14:09:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:44:49
        )

)

ERROR - 2025-06-25 09:44:49 --> -491
ERROR - 2025-06-25 09:44:53 --> Array
(
    [0] => Array
        (
            [log_time] => 09:05:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 11:21:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 11:37:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 14:09:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:35:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:44:53
        )

)

ERROR - 2025-06-25 09:44:53 --> -487
ERROR - 2025-06-25 09:47:10 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:28:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:51:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:48:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:23:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:47:10
        )

)

ERROR - 2025-06-25 09:47:10 --> -2450
ERROR - 2025-06-25 09:49:01 --> Array
(
    [0] => Array
        (
            [log_time] => 09:07:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:10:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:03:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:49:01
        )

)

ERROR - 2025-06-25 09:49:01 --> -659
ERROR - 2025-06-25 09:49:06 --> Array
(
    [0] => Array
        (
            [log_time] => 09:26:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:10:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:03:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 16:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 16:05:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:49:06
        )

)

ERROR - 2025-06-25 09:49:06 --> -1974
ERROR - 2025-06-25 09:55:30 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:28:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:51:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:48:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:23:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:55:30
        )

)

ERROR - 2025-06-25 09:55:30 --> -1950
ERROR - 2025-06-25 09:56:00 --> Array
(
    [0] => Array
        (
            [log_time] => 09:24:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:02:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:27:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:05:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:43:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:56:00
        )

)

ERROR - 2025-06-25 09:56:00 --> -1860
ERROR - 2025-06-25 10:07:28 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:07:28
        )

)

ERROR - 2025-06-25 10:07:28 --> -1952
ERROR - 2025-06-25 10:07:28 --> Array
(
    [0] => Array
        (
            [log_time] => 09:24:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:46:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:20:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:43:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:07:28
        )

)

ERROR - 2025-06-25 10:07:28 --> -1892
ERROR - 2025-06-25 10:09:13 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:28:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:51:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:48:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:23:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:09:13
        )

)

ERROR - 2025-06-25 10:09:13 --> -1127
ERROR - 2025-06-25 10:09:15 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:28:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:51:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:48:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:23:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:09:15
        )

)

ERROR - 2025-06-25 10:09:15 --> -1125
ERROR - 2025-06-25 10:17:50 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-25 10:47:18 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-25'
AND   (
('09:25' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('10:45' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:25' AND '10:45')
OR (TIME(end_time) BETWEEN '09:25' AND '10:45')
 )
ERROR - 2025-06-25 11:42:01 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-25 11:47:01 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-25 12:06:11 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-25 12:42:50 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 13 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-06-24)'
AND   (
`tickets`.`title` LIKE '%ign%' ESCAPE '!'
OR  `tickets`.`description` LIKE '%ign%' ESCAPE '!'
OR  `projects`.`title` LIKE '%ign%' ESCAPE '!'
OR  `users`.`name` LIKE '%ign%' ESCAPE '!'
OR  `tickets`.`reported_by` LIKE '%ign%' ESCAPE '!'
OR  CONCAT("TKT", tickets.id) LIKE '%ign%' ESCAPE '!'
 )
ERROR - 2025-06-25 12:44:30 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-25'
AND   (
('10:46' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:54' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '10:46' AND '11:54')
OR (TIME(end_time) BETWEEN '10:46' AND '11:54')
 )
ERROR - 2025-06-25 13:14:03 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 13 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-06-23)'
AND   (
`tickets`.`title` LIKE '%ign%' ESCAPE '!'
OR  `tickets`.`description` LIKE '%ign%' ESCAPE '!'
OR  `projects`.`title` LIKE '%ign%' ESCAPE '!'
OR  `users`.`name` LIKE '%ign%' ESCAPE '!'
OR  `tickets`.`reported_by` LIKE '%ign%' ESCAPE '!'
OR  CONCAT("TKT", tickets.id) LIKE '%ign%' ESCAPE '!'
 )
ERROR - 2025-06-25 13:14:26 --> Query error: You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 13 - Invalid query: SELECT COUNT(*) as count
FROM `tickets`
LEFT JOIN `projects` ON `projects`.`id` = `tickets`.`project_id`
LEFT JOIN `users` ON `users`.`id` = `tickets`.`user_id`
WHERE (`tickets`.`ticket_date` IS NOT NULL AND DATE(tickets.ticket_date) >= '0002-06-23)'
AND   (
`tickets`.`title` LIKE '%ign%' ESCAPE '!'
OR  `tickets`.`description` LIKE '%ign%' ESCAPE '!'
OR  `projects`.`title` LIKE '%ign%' ESCAPE '!'
OR  `users`.`name` LIKE '%ign%' ESCAPE '!'
OR  `tickets`.`reported_by` LIKE '%ign%' ESCAPE '!'
OR  CONCAT("TKT", tickets.id) LIKE '%ign%' ESCAPE '!'
 )
ERROR - 2025-06-25 13:36:14 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '61'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-25'
AND   (
('12:05' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('14:05' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '12:05' AND '14:05')
OR (TIME(end_time) BETWEEN '12:05' AND '14:05')
 )
ERROR - 2025-06-25 15:53:54 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-25 17:04:30 --> Severity: error --> Exception: Call to a member function insert_ticket_history() on null /home/<USER>/ci_apps/application/controllers/app/Tickets.php 479
ERROR - 2025-06-25 18:23:29 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-25'
AND   (
('09:28' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('11:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '09:28' AND '11:30')
OR (TIME(end_time) BETWEEN '09:28' AND '11:30')
 )
ERROR - 2025-06-25 18:24:25 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-25'
AND   (
('11:31' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('12:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '11:31' AND '12:30')
OR (TIME(end_time) BETWEEN '11:31' AND '12:30')
 )
ERROR - 2025-06-25 18:24:53 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-25'
AND   (
('12:31' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('13:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '12:31' AND '13:30')
OR (TIME(end_time) BETWEEN '12:31' AND '13:30')
 )
ERROR - 2025-06-25 18:25:17 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '5'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-25'
AND   (
('14:15' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('18:25' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '14:15' AND '18:25')
OR (TIME(end_time) BETWEEN '14:15' AND '18:25')
 )
ERROR - 2025-06-25 18:33:46 --> Severity: Warning --> count(): Parameter must be an array or an object that implements Countable /home/<USER>/ci_apps/application/core/MY_Controller.php 27
ERROR - 2025-06-25 18:33:46 --> Severity: Warning --> Cannot modify header information - headers already sent by (output started at /home/<USER>/ci_apps/system/core/Exceptions.php:272) /home/<USER>/ci_apps/system/helpers/url_helper.php 565
