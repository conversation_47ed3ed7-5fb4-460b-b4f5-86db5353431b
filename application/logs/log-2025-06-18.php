<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-18 00:58:55 --> Array
(
    [0] => Array
        (
            [log_time] => 09:29:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:32:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:51:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:26:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 00:58:55
        )

)

ERROR - 2025-06-18 00:58:55 --> -33965
ERROR - 2025-06-18 09:30:20 --> <PERSON>rray
(
    [0] => Array
        (
            [log_time] => 09:40:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:44:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:09:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:00:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:48:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 15:55:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 16:17:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:30:20
        )

)

ERROR - 2025-06-18 09:30:20 --> -6280
ERROR - 2025-06-18 09:32:45 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:32:45
        )

)

ERROR - 2025-06-18 09:32:45 --> -4275
ERROR - 2025-06-18 09:32:52 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:32:52
        )

)

ERROR - 2025-06-18 09:32:52 --> -4268
ERROR - 2025-06-18 09:33:09 --> Array
(
    [0] => Array
        (
            [log_time] => 09:23:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:40:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:05:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:40:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:33:09
        )

)

ERROR - 2025-06-18 09:33:09 --> -2991
ERROR - 2025-06-18 09:33:10 --> Array
(
    [0] => Array
        (
            [log_time] => 09:23:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:40:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:05:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:40:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:33:10
        )

)

ERROR - 2025-06-18 09:33:10 --> -2990
ERROR - 2025-06-18 09:33:25 --> Array
(
    [0] => Array
        (
            [log_time] => 09:01:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 14:00:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:48:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:33:25
        )

)

ERROR - 2025-06-18 09:33:25 --> -935
ERROR - 2025-06-18 09:34:35 --> Array
(
    [0] => Array
        (
            [log_time] => 09:23:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:40:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:05:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:40:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:34:35
        )

)

ERROR - 2025-06-18 09:34:35 --> -2905
ERROR - 2025-06-18 09:34:41 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:04:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:34:41
        )

)

ERROR - 2025-06-18 09:34:41 --> -4399
ERROR - 2025-06-18 09:36:20 --> Array
(
    [0] => Array
        (
            [log_time] => 09:31:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:04:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:20
        )

)

ERROR - 2025-06-18 09:36:20 --> -4180
ERROR - 2025-06-18 09:36:30 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:33:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:30
        )

)

ERROR - 2025-06-18 09:36:30 --> -3090
ERROR - 2025-06-18 09:37:27 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:37:27
        )

)

ERROR - 2025-06-18 09:37:27 --> -3633
ERROR - 2025-06-18 09:37:48 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:37:48
        )

)

ERROR - 2025-06-18 09:37:48 --> -3612
ERROR - 2025-06-18 09:38:05 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:04:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:38:05
        )

)

ERROR - 2025-06-18 09:38:05 --> -3235
ERROR - 2025-06-18 09:38:18 --> Array
(
    [0] => Array
        (
            [log_time] => 09:31:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:38:18
        )

)

ERROR - 2025-06-18 09:38:18 --> -4362
ERROR - 2025-06-18 09:38:32 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 09:38:32 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:38:32
        )

)

ERROR - 2025-06-18 09:38:32 --> -3568
ERROR - 2025-06-18 09:40:33 --> Array
(
    [0] => Array
        (
            [log_time] => 09:23:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:17:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:04:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:33
        )

)

ERROR - 2025-06-18 09:40:33 --> -3327
ERROR - 2025-06-18 09:43:10 --> Array
(
    [0] => Array
        (
            [log_time] => 09:23:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:17:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:04:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:43:10
        )

)

ERROR - 2025-06-18 09:43:10 --> -3170
ERROR - 2025-06-18 09:45:04 --> Array
(
    [0] => Array
        (
            [log_time] => 09:23:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:17:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:04:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:45:04
        )

)

ERROR - 2025-06-18 09:45:04 --> -3056
ERROR - 2025-06-18 09:56:44 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:56:44
        )

)

ERROR - 2025-06-18 09:56:44 --> -2476
ERROR - 2025-06-18 10:11:37 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:11:37
        )

)

ERROR - 2025-06-18 10:11:37 --> -1583
ERROR - 2025-06-18 10:12:45 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:12:45
        )

)

ERROR - 2025-06-18 10:12:45 --> -1875
ERROR - 2025-06-18 10:13:24 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-17'
AND   (
('16:50' BETWEEN start_time AND end_time)
OR ('17:16' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:50' AND '17:16')
OR (end_time BETWEEN '16:50' AND '17:16')
 )
ERROR - 2025-06-18 10:13:25 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:13:25
        )

)

ERROR - 2025-06-18 10:13:25 --> -1835
ERROR - 2025-06-18 10:13:29 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:45:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:06:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:13:29
        )

)

ERROR - 2025-06-18 10:13:29 --> -1831
ERROR - 2025-06-18 10:26:56 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:04:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:26:56
        )

)

ERROR - 2025-06-18 10:26:56 --> -1264
ERROR - 2025-06-18 10:30:28 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:30:28
        )

)

ERROR - 2025-06-18 10:30:28 --> -452
ERROR - 2025-06-18 10:30:31 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:30:31
        )

)

ERROR - 2025-06-18 10:30:31 --> -449
ERROR - 2025-06-18 10:30:35 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:16:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:39:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:30:35
        )

)

ERROR - 2025-06-18 10:30:35 --> -445
ERROR - 2025-06-18 10:33:51 --> Array
(
    [0] => Array
        (
            [log_time] => 09:40:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:44:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:09:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:00:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:48:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 15:55:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 16:17:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 10:33:51
        )

)

ERROR - 2025-06-18 10:33:51 --> -2469
ERROR - 2025-06-18 10:37:44 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:15:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:46:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:04:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:49:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:37:44
        )

)

ERROR - 2025-06-18 10:37:44 --> -616
ERROR - 2025-06-18 11:47:19 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-18'
AND   (
('09:29' BETWEEN start_time AND end_time)
OR ('11:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:29' AND '11:20')
OR (end_time BETWEEN '09:29' AND '11:20')
 )
ERROR - 2025-06-18 11:51:22 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-18'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:19' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:19')
OR (end_time BETWEEN '09:30' AND '11:19')
 )
ERROR - 2025-06-18 11:51:49 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-18'
AND   (
('11:44' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:44' AND '14:00')
OR (end_time BETWEEN '11:44' AND '14:00')
 )
ERROR - 2025-06-18 12:23:51 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 12:26:07 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-18'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:20')
OR (end_time BETWEEN '09:30' AND '11:20')
 )
ERROR - 2025-06-18 12:29:22 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-18'
AND   (
('09:27' BETWEEN start_time AND end_time)
OR ('11:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:27' AND '11:20')
OR (end_time BETWEEN '09:27' AND '11:20')
 )
ERROR - 2025-06-18 12:30:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-18'
AND   (
('11:45' BETWEEN start_time AND end_time)
OR ('12:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:45' AND '12:30')
OR (end_time BETWEEN '11:45' AND '12:30')
 )
ERROR - 2025-06-18 13:12:44 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-18'
AND   (
('12:31' BETWEEN start_time AND end_time)
OR ('13:12' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:31' AND '13:12')
OR (end_time BETWEEN '12:31' AND '13:12')
 )
ERROR - 2025-06-18 14:08:04 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-18'
AND   (
('13:13' BETWEEN start_time AND end_time)
OR ('14:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:13' AND '14:10')
OR (end_time BETWEEN '13:13' AND '14:10')
 )
ERROR - 2025-06-18 14:40:14 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-18'
AND   (
('11:45' BETWEEN start_time AND end_time)
OR ('14:06' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:45' AND '14:06')
OR (end_time BETWEEN '11:45' AND '14:06')
 )
ERROR - 2025-06-18 14:46:43 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-18'
AND   (
('11:45' BETWEEN start_time AND end_time)
OR ('14:04' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:45' AND '14:04')
OR (end_time BETWEEN '11:45' AND '14:04')
 )
ERROR - 2025-06-18 14:54:01 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-18'
AND   (
('11:57' BETWEEN start_time AND end_time)
OR ('14:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:57' AND '14:10')
OR (end_time BETWEEN '11:57' AND '14:10')
 )
ERROR - 2025-06-18 15:07:40 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 15:08:13 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 15:08:41 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 15:09:21 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 15:41:54 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 15:43:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-18'
AND   (
('11:44' BETWEEN start_time AND end_time)
OR ('14:08' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:44' AND '14:08')
OR (end_time BETWEEN '11:44' AND '14:08')
 )
ERROR - 2025-06-18 15:43:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-18'
AND   (
('14:44' BETWEEN start_time AND end_time)
OR ('16:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:44' AND '16:00')
OR (end_time BETWEEN '14:44' AND '16:00')
 )
ERROR - 2025-06-18 15:44:53 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-18'
AND   (
('16:01' BETWEEN start_time AND end_time)
OR ('17:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:01' AND '17:45')
OR (end_time BETWEEN '16:01' AND '17:45')
 )
ERROR - 2025-06-18 15:45:42 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-18'
AND   (
('14:55' BETWEEN start_time AND end_time)
OR ('15:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:55' AND '15:45')
OR (end_time BETWEEN '14:55' AND '15:45')
 )
ERROR - 2025-06-18 16:25:47 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 17:08:04 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-18'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('10:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '10:00')
OR (end_time BETWEEN '09:30' AND '10:00')
 )
ERROR - 2025-06-18 17:09:08 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-18'
AND   (
('10:01' BETWEEN start_time AND end_time)
OR ('10:55' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:01' AND '10:55')
OR (end_time BETWEEN '10:01' AND '10:55')
 )
ERROR - 2025-06-18 17:12:46 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 17:13:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-18'
AND   (
('09:28' BETWEEN start_time AND end_time)
OR ('14:08' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:28' AND '14:08')
OR (end_time BETWEEN '09:28' AND '14:08')
 )
ERROR - 2025-06-18 17:31:00 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-18'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('17:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '17:30')
OR (end_time BETWEEN '14:40' AND '17:30')
 )
ERROR - 2025-06-18 17:32:12 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-18'
AND   (
('14:58' BETWEEN start_time AND end_time)
OR ('17:33' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:58' AND '17:33')
OR (end_time BETWEEN '14:58' AND '17:33')
 )
ERROR - 2025-06-18 17:45:26 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-18'
AND   (
('10:08' BETWEEN start_time AND end_time)
OR ('11:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:08' AND '11:30')
OR (end_time BETWEEN '10:08' AND '11:30')
 )
ERROR - 2025-06-18 17:46:04 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-18'
AND   (
('11:31' BETWEEN start_time AND end_time)
OR ('12:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:31' AND '12:00')
OR (end_time BETWEEN '11:31' AND '12:00')
 )
ERROR - 2025-06-18 17:48:38 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-18'
AND   (
('12:01' BETWEEN start_time AND end_time)
OR ('13:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:01' AND '13:00')
OR (end_time BETWEEN '12:01' AND '13:00')
 )
ERROR - 2025-06-18 17:49:54 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-18'
AND   (
('13:01' BETWEEN start_time AND end_time)
OR ('13:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:01' AND '13:30')
OR (end_time BETWEEN '13:01' AND '13:30')
 )
ERROR - 2025-06-18 17:50:28 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-18'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('15:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '15:30')
OR (end_time BETWEEN '14:40' AND '15:30')
 )
ERROR - 2025-06-18 17:56:44 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 17:57:02 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-18'
AND   (
('15:31' BETWEEN start_time AND end_time)
OR ('17:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:31' AND '17:00')
OR (end_time BETWEEN '15:31' AND '17:00')
 )
ERROR - 2025-06-18 17:58:00 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-18'
AND   (
('09:25' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:25' AND '11:15')
OR (end_time BETWEEN '09:25' AND '11:15')
 )
ERROR - 2025-06-18 17:58:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-18'
AND   (
('11:50' BETWEEN start_time AND end_time)
OR ('13:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:50' AND '13:30')
OR (end_time BETWEEN '11:50' AND '13:30')
 )
ERROR - 2025-06-18 17:58:31 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-18'
AND   (
('17:01' BETWEEN start_time AND end_time)
OR ('17:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:01' AND '17:20')
OR (end_time BETWEEN '17:01' AND '17:20')
 )
ERROR - 2025-06-18 17:58:37 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-18'
AND   (
('16:30' BETWEEN start_time AND end_time)
OR ('16:59' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:30' AND '16:59')
OR (end_time BETWEEN '16:30' AND '16:59')
 )
ERROR - 2025-06-18 17:59:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-18'
AND   (
('13:31' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:31' AND '14:00')
OR (end_time BETWEEN '13:31' AND '14:00')
 )
ERROR - 2025-06-18 17:59:39 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-18'
AND   (
('17:21' BETWEEN start_time AND end_time)
OR ('17:59' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:21' AND '17:59')
OR (end_time BETWEEN '17:21' AND '17:59')
 )
ERROR - 2025-06-18 18:00:50 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-18'
AND   (
('15:00' BETWEEN start_time AND end_time)
OR ('16:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:00' AND '16:45')
OR (end_time BETWEEN '15:00' AND '16:45')
 )
ERROR - 2025-06-18 18:02:41 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-18'
AND   (
('09:35' BETWEEN start_time AND end_time)
OR ('11:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:35' AND '11:20')
OR (end_time BETWEEN '09:35' AND '11:20')
 )
ERROR - 2025-06-18 18:03:21 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-18'
AND   (
('16:46' BETWEEN start_time AND end_time)
OR ('17:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:46' AND '17:15')
OR (end_time BETWEEN '16:46' AND '17:15')
 )
ERROR - 2025-06-18 18:13:01 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-18'
AND   (
('17:21' BETWEEN start_time AND end_time)
OR ('18:12' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:21' AND '18:12')
OR (end_time BETWEEN '17:21' AND '18:12')
 )
ERROR - 2025-06-18 18:14:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-18'
AND   (
('14:45' BETWEEN start_time AND end_time)
OR ('18:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:45' AND '18:15')
OR (end_time BETWEEN '14:45' AND '18:15')
 )
ERROR - 2025-06-18 18:18:39 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-18'
AND   (
('15:46' BETWEEN start_time AND end_time)
OR ('18:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:46' AND '18:30')
OR (end_time BETWEEN '15:46' AND '18:30')
 )
ERROR - 2025-06-18 18:28:45 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-18'
AND   (
('12:09' BETWEEN start_time AND end_time)
OR ('14:06' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:09' AND '14:06')
OR (end_time BETWEEN '12:09' AND '14:06')
 )
ERROR - 2025-06-18 18:35:34 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-18'
AND   (
('14:56' BETWEEN start_time AND end_time)
OR ('16:29' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:56' AND '16:29')
OR (end_time BETWEEN '14:56' AND '16:29')
 )
ERROR - 2025-06-18 18:35:46 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-18'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('10:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '10:00')
OR (end_time BETWEEN '09:30' AND '10:00')
 )
ERROR - 2025-06-18 18:36:32 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 18:37:20 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-18'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:15')
OR (end_time BETWEEN '09:30' AND '11:15')
 )
ERROR - 2025-06-18 18:37:41 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-18'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('10:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '10:30')
OR (end_time BETWEEN '09:30' AND '10:30')
 )
ERROR - 2025-06-18 18:39:01 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-18'
AND   (
('10:38' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:38' AND '11:00')
OR (end_time BETWEEN '10:38' AND '11:00')
 )
ERROR - 2025-06-18 18:39:02 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-18'
AND   (
('17:00' BETWEEN start_time AND end_time)
OR ('17:55' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:00' AND '17:55')
OR (end_time BETWEEN '17:00' AND '17:55')
 )
ERROR - 2025-06-18 18:40:15 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-18'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('12:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '12:00')
OR (end_time BETWEEN '11:20' AND '12:00')
 )
ERROR - 2025-06-18 18:40:32 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-18'
AND   (
('17:56' BETWEEN start_time AND end_time)
OR ('18:39' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:56' AND '18:39')
OR (end_time BETWEEN '17:56' AND '18:39')
 )
ERROR - 2025-06-18 18:40:49 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-18'
AND   (
('12:01' BETWEEN start_time AND end_time)
OR ('13:01' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:01' AND '13:01')
OR (end_time BETWEEN '12:01' AND '13:01')
 )
ERROR - 2025-06-18 18:41:25 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-18'
AND   (
('11:40' BETWEEN start_time AND end_time)
OR ('13:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:40' AND '13:50')
OR (end_time BETWEEN '11:40' AND '13:50')
 )
ERROR - 2025-06-18 18:41:26 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-18'
AND   (
('09:35' BETWEEN start_time AND end_time)
OR ('13:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:35' AND '13:00')
OR (end_time BETWEEN '09:35' AND '13:00')
 )
ERROR - 2025-06-18 18:41:50 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-18'
AND   (
('13:02' BETWEEN start_time AND end_time)
OR ('14:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:02' AND '14:10')
OR (end_time BETWEEN '13:02' AND '14:10')
 )
ERROR - 2025-06-18 18:42:12 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-18'
AND   (
('14:45' BETWEEN start_time AND end_time)
OR ('15:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:45' AND '15:00')
OR (end_time BETWEEN '14:45' AND '15:00')
 )
ERROR - 2025-06-18 18:42:24 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 18:43:07 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 18:43:38 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-18'
AND   (
('13:01' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:01' AND '14:00')
OR (end_time BETWEEN '13:01' AND '14:00')
 )
ERROR - 2025-06-18 18:44:10 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-18'
AND   (
('15:01' BETWEEN start_time AND end_time)
OR ('15:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:01' AND '15:20')
OR (end_time BETWEEN '15:01' AND '15:20')
 )
ERROR - 2025-06-18 18:45:02 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-18'
AND   (
('15:21' BETWEEN start_time AND end_time)
OR ('15:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:21' AND '15:45')
OR (end_time BETWEEN '15:21' AND '15:45')
 )
ERROR - 2025-06-18 18:45:31 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-18'
AND   (
('14:55' BETWEEN start_time AND end_time)
OR ('18:40' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:55' AND '18:40')
OR (end_time BETWEEN '14:55' AND '18:40')
 )
ERROR - 2025-06-18 18:46:02 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-18'
AND   (
('09:37' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:37' AND '11:00')
OR (end_time BETWEEN '09:37' AND '11:00')
 )
ERROR - 2025-06-18 18:46:30 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-18'
AND   (
('11:30' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:30' AND '14:00')
OR (end_time BETWEEN '11:30' AND '14:00')
 )
ERROR - 2025-06-18 18:46:48 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-18'
AND   (
('15:46' BETWEEN start_time AND end_time)
OR ('17:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:46' AND '17:00')
OR (end_time BETWEEN '15:46' AND '17:00')
 )
ERROR - 2025-06-18 18:47:12 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-18'
AND   (
('14:55' BETWEEN start_time AND end_time)
OR ('18:46' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:55' AND '18:46')
OR (end_time BETWEEN '14:55' AND '18:46')
 )
ERROR - 2025-06-18 18:47:22 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-18'
AND   (
('17:01' BETWEEN start_time AND end_time)
OR ('18:47' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:01' AND '18:47')
OR (end_time BETWEEN '17:01' AND '18:47')
 )
ERROR - 2025-06-18 18:47:53 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-18'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('10:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '10:00')
OR (end_time BETWEEN '09:30' AND '10:00')
 )
ERROR - 2025-06-18 18:48:53 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 18:50:28 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-18'
AND   (
('10:01' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:01' AND '11:00')
OR (end_time BETWEEN '10:01' AND '11:00')
 )
ERROR - 2025-06-18 18:51:23 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-18'
AND   (
('11:30' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:30' AND '14:00')
OR (end_time BETWEEN '11:30' AND '14:00')
 )
ERROR - 2025-06-18 18:51:42 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-18'
AND   (
('09:32' BETWEEN start_time AND end_time)
OR ('11:03' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:32' AND '11:03')
OR (end_time BETWEEN '09:32' AND '11:03')
 )
ERROR - 2025-06-18 18:53:18 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-18'
AND   (
('15:00' BETWEEN start_time AND end_time)
OR ('18:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:00' AND '18:30')
OR (end_time BETWEEN '15:00' AND '18:30')
 )
ERROR - 2025-06-18 18:54:49 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-18'
AND   (
('11:21' BETWEEN start_time AND end_time)
OR ('14:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:21' AND '14:05')
OR (end_time BETWEEN '11:21' AND '14:05')
 )
ERROR - 2025-06-18 18:56:34 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-18'
AND   (
('15:01' BETWEEN start_time AND end_time)
OR ('15:33' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:01' AND '15:33')
OR (end_time BETWEEN '15:01' AND '15:33')
 )
ERROR - 2025-06-18 19:02:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-18'
AND   (
('15:35' BETWEEN start_time AND end_time)
OR ('19:02' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:35' AND '19:02')
OR (end_time BETWEEN '15:35' AND '19:02')
 )
ERROR - 2025-06-18 20:19:12 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-18'
AND   (
('11:45' BETWEEN start_time AND end_time)
OR ('14:07' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:45' AND '14:07')
OR (end_time BETWEEN '11:45' AND '14:07')
 )
ERROR - 2025-06-18 20:20:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-18'
AND   (
('14:55' BETWEEN start_time AND end_time)
OR ('18:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:55' AND '18:00')
OR (end_time BETWEEN '14:55' AND '18:00')
 )
ERROR - 2025-06-18 20:29:36 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-18'
AND   (
('09:29' BETWEEN start_time AND end_time)
OR ('10:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:29' AND '10:50')
OR (end_time BETWEEN '09:29' AND '10:50')
 )
ERROR - 2025-06-18 20:30:37 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 20:32:12 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-18'
AND   (
('10:51' BETWEEN start_time AND end_time)
OR ('11:22' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:51' AND '11:22')
OR (end_time BETWEEN '10:51' AND '11:22')
 )
ERROR - 2025-06-18 20:37:52 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-18'
AND   (
('11:45' BETWEEN start_time AND end_time)
OR ('14:07' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:45' AND '14:07')
OR (end_time BETWEEN '11:45' AND '14:07')
 )
ERROR - 2025-06-18 20:41:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-18'
AND   (
('14:55' BETWEEN start_time AND end_time)
OR ('18:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:55' AND '18:20')
OR (end_time BETWEEN '14:55' AND '18:20')
 )
ERROR - 2025-06-18 23:04:30 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-18'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:00')
OR (end_time BETWEEN '09:30' AND '11:00')
 )
ERROR - 2025-06-18 23:05:26 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-18'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '14:00')
OR (end_time BETWEEN '11:20' AND '14:00')
 )
ERROR - 2025-06-18 23:06:25 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-18'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('17:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '17:50')
OR (end_time BETWEEN '14:40' AND '17:50')
 )
ERROR - 2025-06-18 23:32:28 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-18'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:30')
OR (end_time BETWEEN '09:30' AND '11:30')
 )
ERROR - 2025-06-18 23:37:17 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-18'
AND   (
('11:55' BETWEEN start_time AND end_time)
OR ('14:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:55' AND '14:10')
OR (end_time BETWEEN '11:55' AND '14:10')
 )
ERROR - 2025-06-18 23:39:07 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-18'
AND   (
('14:45' BETWEEN start_time AND end_time)
OR ('18:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:45' AND '18:20')
OR (end_time BETWEEN '14:45' AND '18:20')
 )
ERROR - 2025-06-18 23:39:58 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-18 23:40:39 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-18'
AND   (
('21:45' BETWEEN start_time AND end_time)
OR ('23:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '21:45' AND '23:30')
OR (end_time BETWEEN '21:45' AND '23:30')
 )
