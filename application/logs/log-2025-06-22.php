<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-22 00:04:01 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 00:04:01
        )

)

ERROR - 2025-06-22 00:04:01 --> -38279
ERROR - 2025-06-22 00:04:55 --> <PERSON>rray
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 00:04:55
        )

)

ERROR - 2025-06-22 00:04:55 --> -38225
ERROR - 2025-06-22 00:12:42 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 00:12:42
        )

)

ERROR - 2025-06-22 00:12:42 --> -37758
ERROR - 2025-06-22 00:12:49 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 00:12:49
        )

)

ERROR - 2025-06-22 00:12:49 --> -37751
ERROR - 2025-06-22 00:21:43 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 00:21:43
        )

)

ERROR - 2025-06-22 00:21:43 --> -37217
ERROR - 2025-06-22 00:21:46 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 00:21:46
        )

)

ERROR - 2025-06-22 00:21:46 --> -37214
ERROR - 2025-06-22 00:51:14 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 00:51:14
        )

)

ERROR - 2025-06-22 00:51:14 --> -35446
ERROR - 2025-06-22 00:51:18 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 00:51:18
        )

)

ERROR - 2025-06-22 00:51:18 --> -35442
ERROR - 2025-06-22 00:53:43 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 00:53:43
        )

)

ERROR - 2025-06-22 00:53:43 --> -35297
ERROR - 2025-06-22 00:56:54 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 00:56:54 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 00:56:54 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 00:56:54 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 00:56:54 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 06:14:55 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 06:14:55
        )

)

ERROR - 2025-06-22 06:14:55 --> -16025
ERROR - 2025-06-22 06:23:53 --> Severity: error --> Exception: Call to undefined method Projects_m::get_priority_level() /home/<USER>/ci_apps/application/views/app/projects/index.php 119
ERROR - 2025-06-22 06:23:55 --> Severity: error --> Exception: Call to undefined method Projects_m::get_priority_level() /home/<USER>/ci_apps/application/views/app/projects/index.php 119
ERROR - 2025-06-22 06:23:58 --> Severity: error --> Exception: Call to undefined method Projects_m::get_priority_level() /home/<USER>/ci_apps/application/views/app/projects/index.php 119
ERROR - 2025-06-22 06:24:05 --> Severity: error --> Exception: Call to undefined method Projects_m::get_priority_level() /home/<USER>/ci_apps/application/views/app/projects/index.php 119
ERROR - 2025-06-22 06:24:07 --> Severity: error --> Exception: Call to undefined method Projects_m::get_priority_level() /home/<USER>/ci_apps/application/views/app/projects/index.php 119
ERROR - 2025-06-22 06:24:26 --> Severity: error --> Exception: Call to undefined method Projects_m::get_priority_level() /home/<USER>/ci_apps/application/views/app/projects/index.php 119
ERROR - 2025-06-22 06:24:28 --> Severity: error --> Exception: Call to undefined method Projects_m::get_priority_level() /home/<USER>/ci_apps/application/views/app/projects/index.php 119
ERROR - 2025-06-22 06:24:35 --> Severity: error --> Exception: Call to undefined method Projects_m::get_priority_level() /home/<USER>/ci_apps/application/views/app/projects/index.php 119
ERROR - 2025-06-22 06:35:48 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 06:35:48
        )

)

ERROR - 2025-06-22 06:35:48 --> -14772
ERROR - 2025-06-22 06:56:50 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 06:56:50 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 06:56:50 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 06:56:50 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 06:56:50 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 07:06:35 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 07:06:35 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 07:06:35 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 07:06:35 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 07:06:35 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 07:07:17 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 07:07:17 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 07:07:17 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 07:07:17 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 07:07:17 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 07:18:35 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 07:18:35
        )

)

ERROR - 2025-06-22 07:18:35 --> -12205
ERROR - 2025-06-22 08:15:39 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 08:15:39
        )

)

ERROR - 2025-06-22 08:15:39 --> -8781
ERROR - 2025-06-22 08:16:59 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 08:16:59
        )

)

ERROR - 2025-06-22 08:16:59 --> -8701
ERROR - 2025-06-22 08:17:40 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 08:17:40
        )

)

ERROR - 2025-06-22 08:17:40 --> -8660
ERROR - 2025-06-22 08:18:59 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 08:18:59
        )

)

ERROR - 2025-06-22 08:18:59 --> -8581
ERROR - 2025-06-22 08:19:09 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 08:19:09
        )

)

ERROR - 2025-06-22 08:19:09 --> -8571
ERROR - 2025-06-22 08:19:13 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 08:19:13
        )

)

ERROR - 2025-06-22 08:19:13 --> -8567
ERROR - 2025-06-22 08:22:09 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 08:22:09
        )

)

ERROR - 2025-06-22 08:22:09 --> -8391
ERROR - 2025-06-22 08:50:42 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 08:50:42
        )

)

ERROR - 2025-06-22 08:50:42 --> -6678
ERROR - 2025-06-22 09:01:46 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:01:46
        )

)

ERROR - 2025-06-22 09:01:46 --> -6014
ERROR - 2025-06-22 09:13:50 --> Array
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:13:50
        )

)

ERROR - 2025-06-22 09:13:50 --> -5290
ERROR - 2025-06-22 10:46:45 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 10:46:45 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 10:46:45 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 10:46:45 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 10:46:45 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 12:09:44 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 12:09:44 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 12:09:44 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 12:09:44 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 12:09:44 --> Severity: Warning --> in_array() expects parameter 2 to be array, null given /home/<USER>/ci_apps/application/views/app/projects/edit.php 78
ERROR - 2025-06-22 22:47:56 --> Severity: error --> Exception: Call to a member function format() on bool /home/<USER>/ci_apps/application/views/app/tasks/task_description.php 49
ERROR - 2025-06-22 23:27:29 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '3'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-22'
AND   (
('00:00' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('02:00' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '00:00' AND '02:00')
OR (TIME(end_time) BETWEEN '00:00' AND '02:00')
 )
ERROR - 2025-06-22 23:47:20 --> Query error: Unknown column 'c.name' in 'SELECT' - Invalid query: SELECT `t`.*, `p`.`title` as `project_title`, `u`.`name` as `assigned_user_name`, `c`.`name` as `client_name`
FROM `tasks` `t`
LEFT JOIN `projects` `p` ON `t`.`project_id` = `p`.`id`
LEFT JOIN `users` `u` ON `t`.`user_id` = `u`.`id`
LEFT JOIN `clients` `c` ON `t`.`client_id` = `c`.`id`
WHERE `t`.`id` = '8409'
