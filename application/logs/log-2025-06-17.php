<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-17 09:30:45 --> Array
(
    [0] => Array
        (
            [log_time] => 09:56:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:09:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:34:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:45:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:36:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:30:45
        )

)

ERROR - 2025-06-17 09:30:45 --> -6075
ERROR - 2025-06-17 09:34:37 --> <PERSON>rray
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:09:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:34:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:45:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:25:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:34:37
        )

)

ERROR - 2025-06-17 09:34:37 --> -3623
ERROR - 2025-06-17 09:36:09 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:17:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:04:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:09
        )

)

ERROR - 2025-06-17 09:36:09 --> -4431
ERROR - 2025-06-17 09:36:10 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:17:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:04:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:10
        )

)

ERROR - 2025-06-17 09:36:10 --> -4430
ERROR - 2025-06-17 09:36:13 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:17:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:04:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:13
        )

)

ERROR - 2025-06-17 09:36:13 --> -4427
ERROR - 2025-06-17 09:36:15 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:14:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 15:02:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 15:35:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:15
        )

)

ERROR - 2025-06-17 09:36:15 --> -2865
ERROR - 2025-06-17 09:36:24 --> Array
(
    [0] => Array
        (
            [log_time] => 09:10:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:45:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:25:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:24
        )

)

ERROR - 2025-06-17 09:36:24 --> -2376
ERROR - 2025-06-17 09:36:26 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:17:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:04:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:26
        )

)

ERROR - 2025-06-17 09:36:26 --> -4414
ERROR - 2025-06-17 09:36:40 --> Array
(
    [0] => Array
        (
            [log_time] => 09:10:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:45:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:25:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:40
        )

)

ERROR - 2025-06-17 09:36:40 --> -2360
ERROR - 2025-06-17 09:36:41 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:14:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:36:41
        )

)

ERROR - 2025-06-17 09:36:41 --> -2479
ERROR - 2025-06-17 09:37:00 --> Array
(
    [0] => Array
        (
            [log_time] => 09:48:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:14:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:37:00
        )

)

ERROR - 2025-06-17 09:37:00 --> -4320
ERROR - 2025-06-17 09:37:09 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:14:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:37:09
        )

)

ERROR - 2025-06-17 09:37:09 --> -2451
ERROR - 2025-06-17 09:40:40 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:09:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:45:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:40:40
        )

)

ERROR - 2025-06-17 09:40:40 --> -3440
ERROR - 2025-06-17 09:42:30 --> Array
(
    [0] => Array
        (
            [log_time] => 10:01:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:34:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:16:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:05:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:42:30
        )

)

ERROR - 2025-06-17 09:42:30 --> -5670
ERROR - 2025-06-17 09:42:59 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:44:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:00:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:40:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 16:42:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 16:43:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:42:59
        )

)

ERROR - 2025-06-17 09:42:59 --> -2641
ERROR - 2025-06-17 09:51:50 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:14:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:51:50
        )

)

ERROR - 2025-06-17 09:51:50 --> -1570
ERROR - 2025-06-17 09:58:22 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:58:22
        )

)

ERROR - 2025-06-17 09:58:22 --> -2498
ERROR - 2025-06-17 09:58:22 --> Array
(
    [0] => Array
        (
            [log_time] => 09:43:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:06:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:44:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:36:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:58:22
        )

)

ERROR - 2025-06-17 09:58:22 --> -3818
ERROR - 2025-06-17 09:58:23 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:58:23
        )

)

ERROR - 2025-06-17 09:58:23 --> -2497
ERROR - 2025-06-17 09:58:23 --> Array
(
    [0] => Array
        (
            [log_time] => 09:43:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:06:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:44:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:36:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:58:23
        )

)

ERROR - 2025-06-17 09:58:23 --> -3817
ERROR - 2025-06-17 10:01:03 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:14:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:01:03
        )

)

ERROR - 2025-06-17 10:01:03 --> -1017
ERROR - 2025-06-17 10:01:08 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:14:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:01:08
        )

)

ERROR - 2025-06-17 10:01:08 --> -1012
ERROR - 2025-06-17 10:01:34 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 10:01:34 --> Array
(
    [0] => Array
        (
            [log_time] => 09:17:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:14:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:01:34
        )

)

ERROR - 2025-06-17 10:01:34 --> -986
ERROR - 2025-06-17 10:06:31 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:06:31
        )

)

ERROR - 2025-06-17 10:06:31 --> -2009
ERROR - 2025-06-17 11:14:45 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-17'
AND   (
('09:40' BETWEEN start_time AND end_time)
OR ('10:44' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:40' AND '10:44')
OR (end_time BETWEEN '09:40' AND '10:44')
 )
ERROR - 2025-06-17 11:15:51 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 11:43:52 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 11:45:11 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-17'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:15')
OR (end_time BETWEEN '09:30' AND '11:15')
 )
ERROR - 2025-06-17 11:48:33 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-17'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:16' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:16')
OR (end_time BETWEEN '09:30' AND '11:16')
 )
ERROR - 2025-06-17 11:59:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-17'
AND   (
('09:31' BETWEEN start_time AND end_time)
OR ('11:17' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:31' AND '11:17')
OR (end_time BETWEEN '09:31' AND '11:17')
 )
ERROR - 2025-06-17 12:31:18 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-17'
AND   (
('11:09' BETWEEN start_time AND end_time)
OR ('12:25' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:09' AND '12:25')
OR (end_time BETWEEN '11:09' AND '12:25')
 )
ERROR - 2025-06-17 13:14:33 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-17'
AND   (
('12:26' BETWEEN start_time AND end_time)
OR ('13:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:26' AND '13:15')
OR (end_time BETWEEN '12:26' AND '13:15')
 )
ERROR - 2025-06-17 13:42:38 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-17'
AND   (
('11:46' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:46' AND '14:00')
OR (end_time BETWEEN '11:46' AND '14:00')
 )
ERROR - 2025-06-17 13:43:19 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 14:00:26 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-17'
AND   (
('13:16' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:16' AND '14:00')
OR (end_time BETWEEN '13:16' AND '14:00')
 )
ERROR - 2025-06-17 14:44:46 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-17'
AND   (
('11:35' BETWEEN start_time AND end_time)
OR ('14:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:35' AND '14:05')
OR (end_time BETWEEN '11:35' AND '14:05')
 )
ERROR - 2025-06-17 14:54:36 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 14:56:10 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-17'
AND   (
('11:45' BETWEEN start_time AND end_time)
OR ('14:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:45' AND '14:15')
OR (end_time BETWEEN '11:45' AND '14:15')
 )
ERROR - 2025-06-17 16:24:20 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-17'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('16:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '16:30')
OR (end_time BETWEEN '14:40' AND '16:30')
 )
ERROR - 2025-06-17 16:29:50 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-17'
AND   (
('16:31' BETWEEN start_time AND end_time)
OR ('17:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:31' AND '17:45')
OR (end_time BETWEEN '16:31' AND '17:45')
 )
ERROR - 2025-06-17 17:38:15 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-17'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('17:38' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '17:38')
OR (end_time BETWEEN '14:40' AND '17:38')
 )
ERROR - 2025-06-17 17:40:57 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 17:41:23 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 17:41:49 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 17:43:17 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-17'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:15')
OR (end_time BETWEEN '09:30' AND '11:15')
 )
ERROR - 2025-06-17 17:44:25 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-17'
AND   (
('09:39' BETWEEN start_time AND end_time)
OR ('10:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:39' AND '10:30')
OR (end_time BETWEEN '09:39' AND '10:30')
 )
ERROR - 2025-06-17 17:44:28 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-17'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('23:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '23:00')
OR (end_time BETWEEN '09:30' AND '23:00')
 )
ERROR - 2025-06-17 17:44:50 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-17'
AND   (
('09:32' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:32' AND '11:00')
OR (end_time BETWEEN '09:32' AND '11:00')
 )
ERROR - 2025-06-17 17:44:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-17'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:00')
OR (end_time BETWEEN '09:30' AND '11:00')
 )
ERROR - 2025-06-17 17:45:11 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 17:45:39 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-17'
AND   (
('10:31' BETWEEN start_time AND end_time)
OR ('12:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:31' AND '12:00')
OR (end_time BETWEEN '10:31' AND '12:00')
 )
ERROR - 2025-06-17 17:45:43 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-17'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '14:00')
OR (end_time BETWEEN '11:20' AND '14:00')
 )
ERROR - 2025-06-17 17:46:16 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-17'
AND   (
('12:01' BETWEEN start_time AND end_time)
OR ('13:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:01' AND '13:30')
OR (end_time BETWEEN '12:01' AND '13:30')
 )
ERROR - 2025-06-17 17:47:05 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-17'
AND   (
('11:50' BETWEEN start_time AND end_time)
OR ('14:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:50' AND '14:10')
OR (end_time BETWEEN '11:50' AND '14:10')
 )
ERROR - 2025-06-17 17:47:24 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-17'
AND   (
('14:45' BETWEEN start_time AND end_time)
OR ('15:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:45' AND '15:10')
OR (end_time BETWEEN '14:45' AND '15:10')
 )
ERROR - 2025-06-17 17:47:51 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '44'
AND `job_date` = '2025-06-17'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('17:40' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '17:40')
OR (end_time BETWEEN '14:40' AND '17:40')
 )
ERROR - 2025-06-17 17:48:08 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-17'
AND   (
('15:11' BETWEEN start_time AND end_time)
OR ('15:35' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:11' AND '15:35')
OR (end_time BETWEEN '15:11' AND '15:35')
 )
ERROR - 2025-06-17 17:48:55 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-17'
AND   (
('14:55' BETWEEN start_time AND end_time)
OR ('16:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:55' AND '16:00')
OR (end_time BETWEEN '14:55' AND '16:00')
 )
ERROR - 2025-06-17 17:50:08 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-17'
AND   (
('16:01' BETWEEN start_time AND end_time)
OR ('17:40' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:01' AND '17:40')
OR (end_time BETWEEN '16:01' AND '17:40')
 )
ERROR - 2025-06-17 17:50:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-17'
AND   (
('15:36' BETWEEN start_time AND end_time)
OR ('17:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:36' AND '17:00')
OR (end_time BETWEEN '15:36' AND '17:00')
 )
ERROR - 2025-06-17 17:51:16 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 17:51:19 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-17'
AND   (
('17:01' BETWEEN start_time AND end_time)
OR ('17:51' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:01' AND '17:51')
OR (end_time BETWEEN '17:01' AND '17:51')
 )
ERROR - 2025-06-17 17:52:13 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-17'
AND   (
('09:50' BETWEEN start_time AND end_time)
OR ('23:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:50' AND '23:20')
OR (end_time BETWEEN '09:50' AND '23:20')
 )
ERROR - 2025-06-17 17:59:41 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-17'
AND   (
('17:52' BETWEEN start_time AND end_time)
OR ('17:55' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:52' AND '17:55')
OR (end_time BETWEEN '17:52' AND '17:55')
 )
ERROR - 2025-06-17 18:00:26 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-17'
AND   (
('17:52' BETWEEN start_time AND end_time)
OR ('17:57' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:52' AND '17:57')
OR (end_time BETWEEN '17:52' AND '17:57')
 )
ERROR - 2025-06-17 18:00:48 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-17'
AND   (
('11:17' BETWEEN start_time AND end_time)
OR ('13:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:17' AND '13:00')
OR (end_time BETWEEN '11:17' AND '13:00')
 )
ERROR - 2025-06-17 18:01:03 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '5'
AND `job_date` = '2025-06-17'
AND   (
('17:52' BETWEEN start_time AND end_time)
OR ('18:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:52' AND '18:00')
OR (end_time BETWEEN '17:52' AND '18:00')
 )
ERROR - 2025-06-17 18:01:18 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-17'
AND   (
('09:50' BETWEEN start_time AND end_time)
OR ('11:16' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:50' AND '11:16')
OR (end_time BETWEEN '09:50' AND '11:16')
 )
ERROR - 2025-06-17 18:01:32 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-17'
AND   (
('13:01' BETWEEN start_time AND end_time)
OR ('14:01' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:01' AND '14:01')
OR (end_time BETWEEN '13:01' AND '14:01')
 )
ERROR - 2025-06-17 18:02:48 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 18:03:11 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-17'
AND   (
('14:45' BETWEEN start_time AND end_time)
OR ('16:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:45' AND '16:00')
OR (end_time BETWEEN '14:45' AND '16:00')
 )
ERROR - 2025-06-17 18:03:15 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-17'
AND   (
('11:47' BETWEEN start_time AND end_time)
OR ('14:06' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:47' AND '14:06')
OR (end_time BETWEEN '11:47' AND '14:06')
 )
ERROR - 2025-06-17 18:04:51 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-17'
AND   (
('14:49' BETWEEN start_time AND end_time)
OR ('15:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:49' AND '15:30')
OR (end_time BETWEEN '14:49' AND '15:30')
 )
ERROR - 2025-06-17 18:05:12 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-17'
AND   (
('09:35' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:35' AND '11:00')
OR (end_time BETWEEN '09:35' AND '11:00')
 )
ERROR - 2025-06-17 18:05:21 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-17'
AND   (
('15:04' BETWEEN start_time AND end_time)
OR ('17:31' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:04' AND '17:31')
OR (end_time BETWEEN '15:04' AND '17:31')
 )
ERROR - 2025-06-17 18:05:30 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-17'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('10:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '10:00')
OR (end_time BETWEEN '09:30' AND '10:00')
 )
ERROR - 2025-06-17 18:05:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-17'
AND   (
('09:50' BETWEEN start_time AND end_time)
OR ('11:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:50' AND '11:20')
OR (end_time BETWEEN '09:50' AND '11:20')
 )
ERROR - 2025-06-17 18:05:57 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 18:06:12 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-17'
AND   (
('14:47' BETWEEN start_time AND end_time)
OR ('16:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:47' AND '16:30')
OR (end_time BETWEEN '14:47' AND '16:30')
 )
ERROR - 2025-06-17 18:06:17 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-17'
AND   (
('11:15' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:15' AND '14:00')
OR (end_time BETWEEN '11:15' AND '14:00')
 )
ERROR - 2025-06-17 18:06:38 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 18:06:40 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-17'
AND   (
('09:50' BETWEEN start_time AND end_time)
OR ('11:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:50' AND '11:20')
OR (end_time BETWEEN '09:50' AND '11:20')
 )
ERROR - 2025-06-17 18:07:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-17'
AND   (
('09:51' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:51' AND '11:00')
OR (end_time BETWEEN '09:51' AND '11:00')
 )
ERROR - 2025-06-17 18:07:25 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-17'
AND   (
('16:31' BETWEEN start_time AND end_time)
OR ('18:06' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:31' AND '18:06')
OR (end_time BETWEEN '16:31' AND '18:06')
 )
ERROR - 2025-06-17 18:07:26 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-17'
AND   (
('10:01' BETWEEN start_time AND end_time)
OR ('10:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:01' AND '10:45')
OR (end_time BETWEEN '10:01' AND '10:45')
 )
ERROR - 2025-06-17 18:07:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-17'
AND   (
('09:32' BETWEEN start_time AND end_time)
OR ('11:16' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:32' AND '11:16')
OR (end_time BETWEEN '09:32' AND '11:16')
 )
ERROR - 2025-06-17 18:07:28 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 18:07:37 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 18:07:53 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-17'
AND   (
('14:45' BETWEEN start_time AND end_time)
OR ('16:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:45' AND '16:00')
OR (end_time BETWEEN '14:45' AND '16:00')
 )
ERROR - 2025-06-17 18:07:55 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-17'
AND   (
('16:01' BETWEEN start_time AND end_time)
OR ('18:06' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:01' AND '18:06')
OR (end_time BETWEEN '16:01' AND '18:06')
 )
ERROR - 2025-06-17 18:07:59 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-17'
AND   (
('11:40' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:40' AND '14:00')
OR (end_time BETWEEN '11:40' AND '14:00')
 )
ERROR - 2025-06-17 18:08:00 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 18:08:36 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-17'
AND   (
('10:46' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:46' AND '11:00')
OR (end_time BETWEEN '10:46' AND '11:00')
 )
ERROR - 2025-06-17 18:08:38 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-17'
AND   (
('11:35' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:35' AND '14:00')
OR (end_time BETWEEN '11:35' AND '14:00')
 )
ERROR - 2025-06-17 18:10:00 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 18:10:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-17'
AND   (
('17:32' BETWEEN start_time AND end_time)
OR ('18:09' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:32' AND '18:09')
OR (end_time BETWEEN '17:32' AND '18:09')
 )
ERROR - 2025-06-17 18:11:21 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-17'
AND   (
('11:15' BETWEEN start_time AND end_time)
OR ('13:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:15' AND '13:45')
OR (end_time BETWEEN '11:15' AND '13:45')
 )
ERROR - 2025-06-17 18:11:50 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '18'
AND `job_date` = '2025-06-17'
AND   (
('15:30' BETWEEN start_time AND end_time)
OR ('18:11' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:30' AND '18:11')
OR (end_time BETWEEN '15:30' AND '18:11')
 )
ERROR - 2025-06-17 18:12:17 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-17'
AND   (
('13:46' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:46' AND '14:00')
OR (end_time BETWEEN '13:46' AND '14:00')
 )
ERROR - 2025-06-17 18:14:00 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-17'
AND   (
('14:30' BETWEEN start_time AND end_time)
OR ('15:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:30' AND '15:15')
OR (end_time BETWEEN '14:30' AND '15:15')
 )
ERROR - 2025-06-17 18:14:49 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-17'
AND   (
('16:05' BETWEEN start_time AND end_time)
OR ('16:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:05' AND '16:30')
OR (end_time BETWEEN '16:05' AND '16:30')
 )
ERROR - 2025-06-17 18:14:54 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-17'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('18:14' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '18:14')
OR (end_time BETWEEN '14:40' AND '18:14')
 )
ERROR - 2025-06-17 18:14:58 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 18:15:31 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-17'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('18:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '18:15')
OR (end_time BETWEEN '14:40' AND '18:15')
 )
ERROR - 2025-06-17 18:15:45 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-17'
AND   (
('09:01' BETWEEN start_time AND end_time)
OR ('14:03' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:01' AND '14:03')
OR (end_time BETWEEN '09:01' AND '14:03')
 )
ERROR - 2025-06-17 18:16:05 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-17'
AND   (
('16:35' BETWEEN start_time AND end_time)
OR ('18:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:35' AND '18:15')
OR (end_time BETWEEN '16:35' AND '18:15')
 )
ERROR - 2025-06-17 18:16:25 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '58'
AND `job_date` = '2025-06-17'
AND   (
('14:48' BETWEEN start_time AND end_time)
OR ('18:18' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:48' AND '18:18')
OR (end_time BETWEEN '14:48' AND '18:18')
 )
ERROR - 2025-06-17 18:16:33 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 18:16:38 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-17'
AND   (
('11:47' BETWEEN start_time AND end_time)
OR ('12:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:47' AND '12:30')
OR (end_time BETWEEN '11:47' AND '12:30')
 )
ERROR - 2025-06-17 18:16:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-17'
AND   (
('15:16' BETWEEN start_time AND end_time)
OR ('18:14' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:16' AND '18:14')
OR (end_time BETWEEN '15:16' AND '18:14')
 )
ERROR - 2025-06-17 18:18:42 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-17'
AND   (
('12:31' BETWEEN start_time AND end_time)
OR ('14:06' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:31' AND '14:06')
OR (end_time BETWEEN '12:31' AND '14:06')
 )
ERROR - 2025-06-17 18:18:52 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-17'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('18:18' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '18:18')
OR (end_time BETWEEN '14:40' AND '18:18')
 )
ERROR - 2025-06-17 18:19:59 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-17'
AND   (
('14:47' BETWEEN start_time AND end_time)
OR ('15:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:47' AND '15:45')
OR (end_time BETWEEN '14:47' AND '15:45')
 )
ERROR - 2025-06-17 18:21:58 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-17'
AND   (
('14:47' BETWEEN start_time AND end_time)
OR ('18:25' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:47' AND '18:25')
OR (end_time BETWEEN '14:47' AND '18:25')
 )
ERROR - 2025-06-17 18:33:33 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-17'
AND   (
('16:00' BETWEEN start_time AND end_time)
OR ('18:33' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:00' AND '18:33')
OR (end_time BETWEEN '16:00' AND '18:33')
 )
ERROR - 2025-06-17 18:34:41 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-17'
AND   (
('09:52' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:52' AND '11:15')
OR (end_time BETWEEN '09:52' AND '11:15')
 )
ERROR - 2025-06-17 18:35:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-17'
AND   (
('11:16' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:16' AND '14:00')
OR (end_time BETWEEN '11:16' AND '14:00')
 )
ERROR - 2025-06-17 18:36:10 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-17 18:36:38 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-17'
AND   (
('15:46' BETWEEN start_time AND end_time)
OR ('16:49' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:46' AND '16:49')
OR (end_time BETWEEN '15:46' AND '16:49')
 )
ERROR - 2025-06-17 18:36:48 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-17'
AND   (
('14:50' BETWEEN start_time AND end_time)
OR ('15:57' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:50' AND '15:57')
OR (end_time BETWEEN '14:50' AND '15:57')
 )
ERROR - 2025-06-17 18:39:43 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-17'
AND   (
('17:17' BETWEEN start_time AND end_time)
OR ('18:38' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:17' AND '18:38')
OR (end_time BETWEEN '17:17' AND '18:38')
 )
ERROR - 2025-06-17 19:24:02 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-17'
AND   (
('09:27' BETWEEN start_time AND end_time)
OR ('11:17' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:27' AND '11:17')
OR (end_time BETWEEN '09:27' AND '11:17')
 )
ERROR - 2025-06-17 19:35:26 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-17'
AND   (
('11:45' BETWEEN start_time AND end_time)
OR ('14:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:45' AND '14:05')
OR (end_time BETWEEN '11:45' AND '14:05')
 )
ERROR - 2025-06-17 19:48:23 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-17'
AND   (
('14:52' BETWEEN start_time AND end_time)
OR ('18:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:52' AND '18:30')
OR (end_time BETWEEN '14:52' AND '18:30')
 )
ERROR - 2025-06-17 20:50:11 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-17'
AND   (
('09:23' BETWEEN start_time AND end_time)
OR ('11:17' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:23' AND '11:17')
OR (end_time BETWEEN '09:23' AND '11:17')
 )
ERROR - 2025-06-17 22:19:51 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-17'
AND   (
('11:45' BETWEEN start_time AND end_time)
OR ('14:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:45' AND '14:05')
OR (end_time BETWEEN '11:45' AND '14:05')
 )
ERROR - 2025-06-17 22:21:02 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-17'
AND   (
('14:50' BETWEEN start_time AND end_time)
OR ('18:40' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:50' AND '18:40')
OR (end_time BETWEEN '14:50' AND '18:40')
 )
ERROR - 2025-06-17 23:00:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-17'
AND   (
('07:00' BETWEEN start_time AND end_time)
OR ('08:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '07:00' AND '08:30')
OR (end_time BETWEEN '07:00' AND '08:30')
 )
ERROR - 2025-06-17 23:04:23 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-17'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:20')
OR (end_time BETWEEN '09:30' AND '11:20')
 )
ERROR - 2025-06-17 23:05:20 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-17'
AND   (
('11:45' BETWEEN start_time AND end_time)
OR ('13:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:45' AND '13:50')
OR (end_time BETWEEN '11:45' AND '13:50')
 )
ERROR - 2025-06-17 23:06:35 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-17'
AND   (
('14:30' BETWEEN start_time AND end_time)
OR ('18:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:30' AND '18:00')
OR (end_time BETWEEN '14:30' AND '18:00')
 )
