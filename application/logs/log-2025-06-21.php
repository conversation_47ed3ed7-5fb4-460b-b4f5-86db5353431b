<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-21 07:43:11 --> Array
(
    [0] => Array
        (
            [log_time] => 09:24:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:59:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:39:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:12:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 07:43:11
        )

)

ERROR - 2025-06-21 07:43:11 --> -9349
ERROR - 2025-06-21 07:58:44 --> <PERSON>rray
(
    [0] => Array
        (
            [log_time] => 09:25:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:13:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:33:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:40:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 13:37:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 07:58:44
        )

)

ERROR - 2025-06-21 07:58:44 --> -9796
ERROR - 2025-06-21 09:28:51 --> Array
(
    [0] => Array
        (
            [log_time] => 09:14:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:30:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:00:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:00:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 12:15:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:50:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:28:51
        )

)

ERROR - 2025-06-21 09:28:51 --> -4149
ERROR - 2025-06-21 09:28:51 --> Array
(
    [0] => Array
        (
            [log_time] => 09:14:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:30:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:00:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 12:00:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 12:15:00
            [type] => 1
        )

    [5] => Array
        (
            [log_time] => 13:50:00
            [type] => 0
        )

    [6] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [7] => Array
        (
            [type] => 0
            [log_time] => 09:28:51
        )

)

ERROR - 2025-06-21 09:28:51 --> -4149
ERROR - 2025-06-21 10:04:53 --> Array
(
    [0] => Array
        (
            [log_time] => 09:29:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 12:34:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 13:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:47:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:27:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:04:53
        )

)

ERROR - 2025-06-21 10:04:53 --> -2947
ERROR - 2025-06-21 10:44:40 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:44:40
        )

)

ERROR - 2025-06-21 10:44:40 --> -980
ERROR - 2025-06-21 10:44:58 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:44:58
        )

)

ERROR - 2025-06-21 10:44:58 --> -962
ERROR - 2025-06-21 10:47:43 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:47:43
        )

)

ERROR - 2025-06-21 10:47:43 --> -797
ERROR - 2025-06-21 10:48:26 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:48:26
        )

)

ERROR - 2025-06-21 10:48:26 --> -754
ERROR - 2025-06-21 10:50:37 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:50:37
        )

)

ERROR - 2025-06-21 10:50:37 --> -623
ERROR - 2025-06-21 10:53:43 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-20'
AND   (
('14:29' BETWEEN start_time AND end_time)
OR ('15:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:29' AND '15:30')
OR (end_time BETWEEN '14:29' AND '15:30')
 )
ERROR - 2025-06-21 10:53:43 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:53:43
        )

)

ERROR - 2025-06-21 10:53:43 --> -437
ERROR - 2025-06-21 10:55:02 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-21 10:55:03 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:55:03
        )

)

ERROR - 2025-06-21 10:55:03 --> -357
ERROR - 2025-06-21 11:00:58 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-20'
AND   (
('15:31' BETWEEN start_time AND end_time)
OR ('18:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:31' AND '18:00')
OR (end_time BETWEEN '15:31' AND '18:00')
 )
ERROR - 2025-06-21 11:00:59 --> Array
(
    [0] => Array
        (
            [log_time] => 09:37:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:25:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 10:53:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:33:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:29:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 11:00:59
        )

)

ERROR - 2025-06-21 11:00:59 --> -1
ERROR - 2025-06-21 11:02:51 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-20'
AND   (
('18:01' BETWEEN start_time AND end_time)
OR ('18:16' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '18:01' AND '18:16')
OR (end_time BETWEEN '18:01' AND '18:16')
 )
ERROR - 2025-06-21 11:10:28 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-21'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:10')
OR (end_time BETWEEN '09:30' AND '11:10')
 )
ERROR - 2025-06-21 11:33:21 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-21 13:29:07 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-21'
AND   (
('11:11' BETWEEN start_time AND end_time)
OR ('13:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:11' AND '13:30')
OR (end_time BETWEEN '11:11' AND '13:30')
 )
ERROR - 2025-06-21 13:42:13 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-21'
AND   (
('11:11' BETWEEN start_time AND end_time)
OR ('13:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:11' AND '13:45')
OR (end_time BETWEEN '11:11' AND '13:45')
 )
ERROR - 2025-06-21 13:46:26 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-21'
AND   (
('11:11' BETWEEN start_time AND end_time)
OR ('13:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:11' AND '13:50')
OR (end_time BETWEEN '11:11' AND '13:50')
 )
ERROR - 2025-06-21 15:37:49 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-21 15:39:12 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '30'
AND `job_date` = '2025-06-21'
AND   (
('13:00' BETWEEN start_time AND end_time)
OR ('14:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '13:00' AND '14:05')
OR (end_time BETWEEN '13:00' AND '14:05')
 )
ERROR - 2025-06-21 18:19:55 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-21'
AND   (
('15:00' BETWEEN start_time AND end_time)
OR ('18:20' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:00' AND '18:20')
OR (end_time BETWEEN '15:00' AND '18:20')
 )
ERROR - 2025-06-21 19:30:41 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-21'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('13:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '13:30')
OR (end_time BETWEEN '09:30' AND '13:30')
 )
ERROR - 2025-06-21 19:40:58 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-21'
AND   (
('14:45' BETWEEN start_time AND end_time)
OR ('18:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:45' AND '18:00')
OR (end_time BETWEEN '14:45' AND '18:00')
 )
ERROR - 2025-06-21 21:36:12 --> Query error: Unknown column 'status' in 'WHERE' - Invalid query: SELECT COUNT(*) as completed
FROM `tasks`
WHERE `project_id` = '195'
AND `status` = 'completed'
AND DATE(updated_on) = '2025-05-23'
ERROR - 2025-06-21 21:38:20 --> Query error: Unknown column 'status' in 'WHERE' - Invalid query: SELECT COUNT(*) as completed
FROM `tasks`
WHERE `project_id` = '230'
AND `status` = 'completed'
AND DATE(updated_on) = '2025-05-23'
