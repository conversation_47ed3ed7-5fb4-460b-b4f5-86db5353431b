<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-30 07:24:46 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:59:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:28:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:10:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 07:24:46
        )

)

ERROR - 2025-06-30 07:24:46 --> -11354
ERROR - 2025-06-30 07:24:51 --> <PERSON>rray
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:59:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:28:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:10:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 07:24:51
        )

)

ERROR - 2025-06-30 07:24:51 --> -11349
ERROR - 2025-06-30 07:27:22 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:59:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:28:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:10:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 07:27:22
        )

)

ERROR - 2025-06-30 07:27:22 --> -11198
ERROR - 2025-06-30 07:31:03 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:59:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:28:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:10:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 07:31:03
        )

)

ERROR - 2025-06-30 07:31:03 --> -10977
ERROR - 2025-06-30 07:31:06 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:59:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:28:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:10:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 07:31:06
        )

)

ERROR - 2025-06-30 07:31:06 --> -10974
ERROR - 2025-06-30 07:37:21 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:59:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:28:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:10:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 07:37:21
        )

)

ERROR - 2025-06-30 07:37:21 --> -10599
ERROR - 2025-06-30 09:13:32 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:13:32
        )

)

ERROR - 2025-06-30 09:13:32 --> -5188
ERROR - 2025-06-30 09:14:42 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:34:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:14:42
        )

)

ERROR - 2025-06-30 09:14:42 --> -2538
ERROR - 2025-06-30 09:26:20 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:26:20
        )

)

ERROR - 2025-06-30 09:26:20 --> -4420
ERROR - 2025-06-30 09:28:12 --> Array
(
    [0] => Array
        (
            [log_time] => 09:04:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:55:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:34:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:28:12
        )

)

ERROR - 2025-06-30 09:28:12 --> -888
ERROR - 2025-06-30 09:28:37 --> Array
(
    [0] => Array
        (
            [log_time] => 09:04:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:55:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:34:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:28:37
        )

)

ERROR - 2025-06-30 09:28:37 --> -863
ERROR - 2025-06-30 09:31:25 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:58:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:38:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:11:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:31:25
        )

)

ERROR - 2025-06-30 09:31:25 --> -3275
ERROR - 2025-06-30 09:37:58 --> Array
(
    [0] => Array
        (
            [log_time] => 08:58:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:39:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:26:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:37:58
        )

)

ERROR - 2025-06-30 09:37:58 --> -422
ERROR - 2025-06-30 09:38:56 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:38:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:14:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:57:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:38:56
        )

)

ERROR - 2025-06-30 09:38:56 --> -3784
ERROR - 2025-06-30 09:54:31 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:38:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:14:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:57:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:54:31
        )

)

ERROR - 2025-06-30 09:54:31 --> -2849
ERROR - 2025-06-30 09:58:08 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:06:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:38:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:14:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:57:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:58:08
        )

)

ERROR - 2025-06-30 09:58:08 --> -2692
ERROR - 2025-06-30 09:58:12 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:38:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:14:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:57:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:58:12
        )

)

ERROR - 2025-06-30 09:58:12 --> -2628
ERROR - 2025-06-30 09:58:33 --> Array
(
    [0] => Array
        (
            [log_time] => 09:28:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:38:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:14:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:57:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:58:33
        )

)

ERROR - 2025-06-30 09:58:33 --> -2607
ERROR - 2025-06-30 09:59:28 --> Array
(
    [0] => Array
        (
            [log_time] => 09:21:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:58:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:47:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:59:28
        )

)

ERROR - 2025-06-30 09:59:28 --> -2432
ERROR - 2025-06-30 10:04:25 --> Array
(
    [0] => Array
        (
            [log_time] => 09:27:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:09:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:38:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:16:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:58:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:04:25
        )

)

ERROR - 2025-06-30 10:04:25 --> -2015
ERROR - 2025-06-30 10:15:42 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:59:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:21:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:28:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:10:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:15:42
        )

)

ERROR - 2025-06-30 10:15:42 --> -1098
ERROR - 2025-06-30 10:42:41 --> Array
(
    [0] => Array
        (
            [log_time] => 09:32:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:07:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:35:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:14:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:57:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:42:41
        )

)

ERROR - 2025-06-30 10:42:41 --> -19
ERROR - 2025-06-30 10:46:57 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('101', '44', 'Updated ticket status to \'Closed\'', '[]', 'closed', '2025-06-30 10:46:57')
ERROR - 2025-06-30 10:49:23 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('104', '44', 'Assigned ticket to Sainul Abid C K', '[]', 'assigned', '2025-06-30 10:49:23')
ERROR - 2025-06-30 10:49:26 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('104', '44', 'Assigned ticket to Sainul Abid C K', '[]', 'assigned', '2025-06-30 10:49:26')
ERROR - 2025-06-30 10:49:39 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('104', '44', 'Updated ticket status to \'Closed\'', '[]', 'closed', '2025-06-30 10:49:39')
ERROR - 2025-06-30 10:51:12 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('96', '44', 'Updated ticket status to \'Closed\'', '[]', 'closed', '2025-06-30 10:51:12')
ERROR - 2025-06-30 10:51:44 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('85', '44', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-30 10:51:44')
ERROR - 2025-06-30 10:51:46 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('85', '44', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-30 10:51:46')
ERROR - 2025-06-30 10:51:47 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('85', '44', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-30 10:51:47')
ERROR - 2025-06-30 10:51:47 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('85', '44', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-30 10:51:47')
ERROR - 2025-06-30 10:51:49 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('85', '44', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-30 10:51:49')
ERROR - 2025-06-30 10:51:51 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('85', '44', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-30 10:51:51')
ERROR - 2025-06-30 10:51:54 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('85', '44', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-30 10:51:54')
ERROR - 2025-06-30 10:51:56 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('85', '44', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-30 10:51:56')
ERROR - 2025-06-30 10:52:43 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('85', '44', 'Assigned ticket to Ameer Suhail ', '[]', 'assigned', '2025-06-30 10:52:43')
ERROR - 2025-06-30 12:04:23 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('103', '44', 'Updated ticket status to \'Assigned\'', '[]', 'assigned', '2025-06-30 12:04:23')
ERROR - 2025-06-30 12:04:39 --> Query error: Table 'pmstrogon_apps.ticket_history' doesn't exist - Invalid query: INSERT INTO `ticket_history` (`ticket_id`, `user_id`, `remarks`, `files`, `status`, `created_at`) VALUES ('103', '44', 'Updated ticket status to \'Closed\'', '[]', 'closed', '2025-06-30 12:04:39')
ERROR - 2025-06-30 12:05:52 --> Severity: error --> Exception: Call to a member function get() on null /home/<USER>/ci_apps/application/views/app/tickets/ajax_add.php 3
ERROR - 2025-06-30 13:50:10 --> SELECT COUNT(*) as overlap_count
FROM `work_history`
WHERE `user_id` = '3'
AND `item_type` = 'task'
AND DATE(start_time) = '2025-06-30'
AND   (
('12:30' BETWEEN TIME(start_time) AND TIME(end_time))
OR ('13:50' BETWEEN TIME(start_time) AND TIME(end_time))
OR (TIME(start_time) BETWEEN '12:30' AND '13:50')
OR (TIME(end_time) BETWEEN '12:30' AND '13:50')
 )
