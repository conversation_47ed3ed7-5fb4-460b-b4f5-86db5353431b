<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-06-16 06:18:53 --> Array
(
    [0] => Array
        (
            [log_time] => 09:34:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:29:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:54:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:26:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 06:18:53
        )

)

ERROR - 2025-06-16 06:18:53 --> -15427
ERROR - 2025-06-16 06:29:39 --> Array
(
    [0] => Array
        (
            [log_time] => 09:34:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:29:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:54:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:49:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:26:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 06:29:39
        )

)

ERROR - 2025-06-16 06:29:39 --> -14781
ERROR - 2025-06-16 09:19:23 --> Array
(
    [0] => Array
        (
            [log_time] => 09:02:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 14:40:00
            [type] => 1
        )

    [3] => Array
        (
            [type] => 0
            [log_time] => 09:19:23
        )

)

ERROR - 2025-06-16 09:19:23 --> -1417
ERROR - 2025-06-16 09:31:22 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 09:38:35 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:20:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:00:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:46:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:38:35
        )

)

ERROR - 2025-06-16 09:38:35 --> -4285
ERROR - 2025-06-16 09:41:25 --> Array
(
    [0] => Array
        (
            [log_time] => 14:12:00
            [type] => 1
        )

    [1] => Array
        (
            [type] => 0
            [log_time] => 09:41:25
        )

)

ERROR - 2025-06-16 09:41:25 --> -16235
ERROR - 2025-06-16 09:41:25 --> Array
(
    [0] => Array
        (
            [log_time] => 14:12:00
            [type] => 1
        )

    [1] => Array
        (
            [type] => 0
            [log_time] => 09:41:25
        )

)

ERROR - 2025-06-16 09:41:25 --> -16235
ERROR - 2025-06-16 09:41:44 --> Array
(
    [0] => Array
        (
            [log_time] => 14:12:00
            [type] => 1
        )

    [1] => Array
        (
            [type] => 0
            [log_time] => 09:41:44
        )

)

ERROR - 2025-06-16 09:41:44 --> -16216
ERROR - 2025-06-16 09:41:53 --> Array
(
    [0] => Array
        (
            [log_time] => 14:12:00
            [type] => 1
        )

    [1] => Array
        (
            [type] => 0
            [log_time] => 09:41:53
        )

)

ERROR - 2025-06-16 09:41:53 --> -16207
ERROR - 2025-06-16 09:50:26 --> Array
(
    [0] => Array
        (
            [log_time] => 09:22:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:20:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:03:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:38:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 09:50:26
        )

)

ERROR - 2025-06-16 09:50:26 --> -2254
ERROR - 2025-06-16 10:03:26 --> Array
(
    [0] => Array
        (
            [log_time] => 09:30:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:19:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 14:03:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:25:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:03:26
        )

)

ERROR - 2025-06-16 10:03:26 --> -1114
ERROR - 2025-06-16 10:05:45 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:20:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:48:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:05:45
        )

)

ERROR - 2025-06-16 10:05:45 --> -2835
ERROR - 2025-06-16 10:05:50 --> Array
(
    [0] => Array
        (
            [log_time] => 09:33:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 10:49:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:20:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:59:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:48:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:05:50
        )

)

ERROR - 2025-06-16 10:05:50 --> -2830
ERROR - 2025-06-16 10:14:03 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-16'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('10:13' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '10:13')
OR (end_time BETWEEN '09:30' AND '10:13')
 )
ERROR - 2025-06-16 10:30:46 --> Array
(
    [0] => Array
        (
            [log_time] => 09:34:00
            [type] => 1
        )

    [1] => Array
        (
            [log_time] => 11:29:00
            [type] => 0
        )

    [2] => Array
        (
            [log_time] => 11:54:00
            [type] => 1
        )

    [3] => Array
        (
            [log_time] => 13:50:00
            [type] => 0
        )

    [4] => Array
        (
            [log_time] => 14:30:00
            [type] => 1
        )

    [5] => Array
        (
            [type] => 0
            [log_time] => 10:30:46
        )

)

ERROR - 2025-06-16 10:30:46 --> -494
ERROR - 2025-06-16 10:48:13 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 11:40:00 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 11:40:16 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 11:40:32 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-16'
AND   (
('10:14' BETWEEN start_time AND end_time)
OR ('11:14' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:14' AND '11:14')
OR (end_time BETWEEN '10:14' AND '11:14')
 )
ERROR - 2025-06-16 11:40:56 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-16'
AND   (
('11:36' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:36' AND '14:00')
OR (end_time BETWEEN '11:36' AND '14:00')
 )
ERROR - 2025-06-16 11:42:47 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 11:44:16 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-16'
AND   (
('09:48' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:48' AND '11:15')
OR (end_time BETWEEN '09:48' AND '11:15')
 )
ERROR - 2025-06-16 12:58:36 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-16'
AND   (
('12:00' BETWEEN start_time AND end_time)
OR ('13:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:00' AND '13:00')
OR (end_time BETWEEN '12:00' AND '13:00')
 )
ERROR - 2025-06-16 13:00:45 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-16'
AND   (
('11:34' BETWEEN start_time AND end_time)
OR ('11:59' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:34' AND '11:59')
OR (end_time BETWEEN '11:34' AND '11:59')
 )
ERROR - 2025-06-16 13:08:40 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-16'
AND   (
('10:00' BETWEEN start_time AND end_time)
OR ('11:07' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:00' AND '11:07')
OR (end_time BETWEEN '10:00' AND '11:07')
 )
ERROR - 2025-06-16 13:14:41 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-16'
AND   (
('12:00' BETWEEN start_time AND end_time)
OR ('13:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '12:00' AND '13:15')
OR (end_time BETWEEN '12:00' AND '13:15')
 )
ERROR - 2025-06-16 14:28:21 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 14:29:32 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 14:33:10 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-16'
AND   (
('09:20' BETWEEN start_time AND end_time)
OR ('10:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:20' AND '10:30')
OR (end_time BETWEEN '09:20' AND '10:30')
 )
ERROR - 2025-06-16 14:36:14 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-16'
AND   (
('09:20' BETWEEN start_time AND end_time)
OR ('11:02' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:20' AND '11:02')
OR (end_time BETWEEN '09:20' AND '11:02')
 )
ERROR - 2025-06-16 14:37:31 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-16'
AND   (
('09:20' BETWEEN start_time AND end_time)
OR ('11:01' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:20' AND '11:01')
OR (end_time BETWEEN '09:20' AND '11:01')
 )
ERROR - 2025-06-16 14:39:39 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-16'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('13:46' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '13:46')
OR (end_time BETWEEN '11:20' AND '13:46')
 )
ERROR - 2025-06-16 14:54:22 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 14:56:18 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-16'
AND   (
('14:40' BETWEEN start_time AND end_time)
OR ('16:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:40' AND '16:30')
OR (end_time BETWEEN '14:40' AND '16:30')
 )
ERROR - 2025-06-16 15:04:28 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 15:06:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-16'
AND   (
('11:30' BETWEEN start_time AND end_time)
OR ('13:55' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:30' AND '13:55')
OR (end_time BETWEEN '11:30' AND '13:55')
 )
ERROR - 2025-06-16 15:08:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-16'
AND   (
('14:05' BETWEEN start_time AND end_time)
OR ('15:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:05' AND '15:10')
OR (end_time BETWEEN '14:05' AND '15:10')
 )
ERROR - 2025-06-16 16:49:04 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-16'
AND   (
('15:11' BETWEEN start_time AND end_time)
OR ('18:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:11' AND '18:15')
OR (end_time BETWEEN '15:11' AND '18:15')
 )
ERROR - 2025-06-16 17:04:30 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 17:04:57 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-16'
AND   (
('16:31' BETWEEN start_time AND end_time)
OR ('17:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:31' AND '17:00')
OR (end_time BETWEEN '16:31' AND '17:00')
 )
ERROR - 2025-06-16 17:05:25 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-16'
AND   (
('17:01' BETWEEN start_time AND end_time)
OR ('17:35' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:01' AND '17:35')
OR (end_time BETWEEN '17:01' AND '17:35')
 )
ERROR - 2025-06-16 17:10:01 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 17:11:09 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 17:11:36 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '65'
AND `job_date` = '2025-06-16'
AND   (
('17:36' BETWEEN start_time AND end_time)
OR ('17:40' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:36' AND '17:40')
OR (end_time BETWEEN '17:36' AND '17:40')
 )
ERROR - 2025-06-16 17:22:17 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-16'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:10')
OR (end_time BETWEEN '09:30' AND '11:10')
 )
ERROR - 2025-06-16 17:28:05 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-16'
AND   (
('11:20' BETWEEN start_time AND end_time)
OR ('15:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:20' AND '15:00')
OR (end_time BETWEEN '11:20' AND '15:00')
 )
ERROR - 2025-06-16 17:34:42 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '67'
AND `job_date` = '2025-06-16'
AND   (
('14:36' BETWEEN start_time AND end_time)
OR ('17:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:36' AND '17:30')
OR (end_time BETWEEN '14:36' AND '17:30')
 )
ERROR - 2025-06-16 17:44:23 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 17:45:01 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-16'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('10:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '10:00')
OR (end_time BETWEEN '09:30' AND '10:00')
 )
ERROR - 2025-06-16 17:45:49 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-16'
AND   (
('10:01' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:01' AND '14:00')
OR (end_time BETWEEN '10:01' AND '14:00')
 )
ERROR - 2025-06-16 17:47:27 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-16'
AND   (
('14:30' BETWEEN start_time AND end_time)
OR ('15:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:30' AND '15:30')
OR (end_time BETWEEN '14:30' AND '15:30')
 )
ERROR - 2025-06-16 17:49:37 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-16'
AND   (
('15:31' BETWEEN start_time AND end_time)
OR ('17:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:31' AND '17:15')
OR (end_time BETWEEN '15:31' AND '17:15')
 )
ERROR - 2025-06-16 17:50:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '10'
AND `job_date` = '2025-06-16'
AND   (
('17:16' BETWEEN start_time AND end_time)
OR ('17:49' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '17:16' AND '17:49')
OR (end_time BETWEEN '17:16' AND '17:49')
 )
ERROR - 2025-06-16 17:51:33 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-16'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:11' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:11')
OR (end_time BETWEEN '09:30' AND '11:11')
 )
ERROR - 2025-06-16 17:52:35 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '63'
AND `job_date` = '2025-06-16'
AND   (
('15:45' BETWEEN start_time AND end_time)
OR ('17:50' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:45' AND '17:50')
OR (end_time BETWEEN '15:45' AND '17:50')
 )
ERROR - 2025-06-16 17:56:17 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '64'
AND `job_date` = '2025-06-16'
AND   (
('14:35' BETWEEN start_time AND end_time)
OR ('17:55' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:35' AND '17:55')
OR (end_time BETWEEN '14:35' AND '17:55')
 )
ERROR - 2025-06-16 17:57:58 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-16'
AND   (
('11:35' BETWEEN start_time AND end_time)
OR ('13:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:35' AND '13:45')
OR (end_time BETWEEN '11:35' AND '13:45')
 )
ERROR - 2025-06-16 18:01:25 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-16'
AND   (
('09:28' BETWEEN start_time AND end_time)
OR ('11:11' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:28' AND '11:11')
OR (end_time BETWEEN '09:28' AND '11:11')
 )
ERROR - 2025-06-16 18:02:03 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-16'
AND   (
('10:00' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:00' AND '11:00')
OR (end_time BETWEEN '10:00' AND '11:00')
 )
ERROR - 2025-06-16 18:05:00 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-16'
AND   (
('11:25' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:25' AND '14:00')
OR (end_time BETWEEN '11:25' AND '14:00')
 )
ERROR - 2025-06-16 18:05:22 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '61'
AND `job_date` = '2025-06-16'
AND   (
('15:11' BETWEEN start_time AND end_time)
OR ('18:11' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:11' AND '18:11')
OR (end_time BETWEEN '15:11' AND '18:11')
 )
ERROR - 2025-06-16 18:05:38 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '62'
AND `job_date` = '2025-06-16'
AND   (
('14:16' BETWEEN start_time AND end_time)
OR ('18:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:16' AND '18:05')
OR (end_time BETWEEN '14:16' AND '18:05')
 )
ERROR - 2025-06-16 18:06:14 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '60'
AND `job_date` = '2025-06-16'
AND   (
('14:50' BETWEEN start_time AND end_time)
OR ('18:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:50' AND '18:05')
OR (end_time BETWEEN '14:50' AND '18:05')
 )
ERROR - 2025-06-16 18:09:01 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-16'
AND   (
('09:48' BETWEEN start_time AND end_time)
OR ('11:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:48' AND '11:00')
OR (end_time BETWEEN '09:48' AND '11:00')
 )
ERROR - 2025-06-16 18:13:18 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-16'
AND   (
('11:18' BETWEEN start_time AND end_time)
OR ('14:03' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:18' AND '14:03')
OR (end_time BETWEEN '11:18' AND '14:03')
 )
ERROR - 2025-06-16 18:14:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-16'
AND   (
('15:01' BETWEEN start_time AND end_time)
OR ('15:31' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:01' AND '15:31')
OR (end_time BETWEEN '15:01' AND '15:31')
 )
ERROR - 2025-06-16 18:16:29 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '51'
AND `job_date` = '2025-06-16'
AND   (
('15:32' BETWEEN start_time AND end_time)
OR ('18:14' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:32' AND '18:14')
OR (end_time BETWEEN '15:32' AND '18:14')
 )
ERROR - 2025-06-16 18:26:21 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-16'
AND   (
('09:37' BETWEEN start_time AND end_time)
OR ('10:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:37' AND '10:30')
OR (end_time BETWEEN '09:37' AND '10:30')
 )
ERROR - 2025-06-16 18:27:29 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 18:28:28 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-16'
AND   (
('10:31' BETWEEN start_time AND end_time)
OR ('11:07' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '10:31' AND '11:07')
OR (end_time BETWEEN '10:31' AND '11:07')
 )
ERROR - 2025-06-16 18:29:20 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-16'
AND   (
('11:34' BETWEEN start_time AND end_time)
OR ('13:17' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:34' AND '13:17')
OR (end_time BETWEEN '11:34' AND '13:17')
 )
ERROR - 2025-06-16 18:30:42 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-16'
AND   (
('14:06' BETWEEN start_time AND end_time)
OR ('15:40' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:06' AND '15:40')
OR (end_time BETWEEN '14:06' AND '15:40')
 )
ERROR - 2025-06-16 18:32:55 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-16'
AND   (
('15:41' BETWEEN start_time AND end_time)
OR ('16:54' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:41' AND '16:54')
OR (end_time BETWEEN '15:41' AND '16:54')
 )
ERROR - 2025-06-16 18:34:18 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-16'
AND   (
('16:55' BETWEEN start_time AND end_time)
OR ('18:33' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:55' AND '18:33')
OR (end_time BETWEEN '16:55' AND '18:33')
 )
ERROR - 2025-06-16 18:36:15 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-16'
AND   (
('15:41' BETWEEN start_time AND end_time)
OR ('16:16' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:41' AND '16:16')
OR (end_time BETWEEN '15:41' AND '16:16')
 )
ERROR - 2025-06-16 18:36:53 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '15'
AND `job_date` = '2025-06-16'
AND   (
('16:17' BETWEEN start_time AND end_time)
OR ('16:54' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '16:17' AND '16:54')
OR (end_time BETWEEN '16:17' AND '16:54')
 )
ERROR - 2025-06-16 18:40:59 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 18:41:37 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-16'
AND   (
('15:00' BETWEEN start_time AND end_time)
OR ('18:41' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:00' AND '18:41')
OR (end_time BETWEEN '15:00' AND '18:41')
 )
ERROR - 2025-06-16 18:42:18 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-16'
AND   (
('09:43' BETWEEN start_time AND end_time)
OR ('11:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:43' AND '11:05')
OR (end_time BETWEEN '09:43' AND '11:05')
 )
ERROR - 2025-06-16 18:43:09 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '16'
AND `job_date` = '2025-06-16'
AND   (
('11:25' BETWEEN start_time AND end_time)
OR ('14:05' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:25' AND '14:05')
OR (end_time BETWEEN '11:25' AND '14:05')
 )
ERROR - 2025-06-16 18:55:13 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 20:45:32 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-16'
AND   (
('08:15' BETWEEN start_time AND end_time)
OR ('09:25' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '08:15' AND '09:25')
OR (end_time BETWEEN '08:15' AND '09:25')
 )
ERROR - 2025-06-16 20:46:09 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-16'
AND   (
('09:40' BETWEEN start_time AND end_time)
OR ('11:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:40' AND '11:15')
OR (end_time BETWEEN '09:40' AND '11:15')
 )
ERROR - 2025-06-16 20:50:43 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 20:51:37 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-16'
AND   (
('11:30' BETWEEN start_time AND end_time)
OR ('14:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:30' AND '14:00')
OR (end_time BETWEEN '11:30' AND '14:00')
 )
ERROR - 2025-06-16 20:52:28 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '20'
AND `job_date` = '2025-06-16'
AND   (
('14:35' BETWEEN start_time AND end_time)
OR ('19:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:35' AND '19:00')
OR (end_time BETWEEN '14:35' AND '19:00')
 )
ERROR - 2025-06-16 21:25:36 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-16'
AND   (
('11:35' BETWEEN start_time AND end_time)
OR ('13:45' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:35' AND '13:45')
OR (end_time BETWEEN '11:35' AND '13:45')
 )
ERROR - 2025-06-16 21:27:19 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-16'
AND   (
('15:30' BETWEEN start_time AND end_time)
OR ('18:30' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '15:30' AND '18:30')
OR (end_time BETWEEN '15:30' AND '18:30')
 )
ERROR - 2025-06-16 21:28:14 --> <p>You did not select a file to upload.</p>
ERROR - 2025-06-16 21:29:02 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '43'
AND `job_date` = '2025-06-16'
AND   (
('14:30' BETWEEN start_time AND end_time)
OR ('15:29' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:30' AND '15:29')
OR (end_time BETWEEN '14:30' AND '15:29')
 )
ERROR - 2025-06-16 23:17:42 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-16'
AND   (
('09:30' BETWEEN start_time AND end_time)
OR ('11:21' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '09:30' AND '11:21')
OR (end_time BETWEEN '09:30' AND '11:21')
 )
ERROR - 2025-06-16 23:21:06 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-16'
AND   (
('11:42' BETWEEN start_time AND end_time)
OR ('14:10' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '11:42' AND '14:10')
OR (end_time BETWEEN '11:42' AND '14:10')
 )
ERROR - 2025-06-16 23:24:14 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-16'
AND   (
('14:46' BETWEEN start_time AND end_time)
OR ('18:00' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '14:46' AND '18:00')
OR (end_time BETWEEN '14:46' AND '18:00')
 )
ERROR - 2025-06-16 23:25:09 --> SELECT COUNT(*) as overlap_count
FROM `task_assign`
WHERE `user_id` = '3'
AND `job_date` = '2025-06-16'
AND   (
('22:00' BETWEEN start_time AND end_time)
OR ('23:15' BETWEEN start_time AND end_time)
OR (start_time BETWEEN '22:00' AND '23:15')
OR (end_time BETWEEN '22:00' AND '23:15')
 )
