<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
 * Get User IP Address
 */
if (!function_exists('get_client_ip')){
	// Function to get the client IP address
	function get_client_ip() {
		$ipaddress = '';
		if (isset($_SERVER['HTTP_CLIENT_IP']))
			$ipaddress = $_SERVER['HTTP_CLIENT_IP'];
		else if(isset($_SERVER['HTTP_X_FORWARDED_FOR']))
			$ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
		else if(isset($_SERVER['HTTP_X_FORWARDED']))
			$ipaddress = $_SERVER['HTTP_X_FORWARDED'];
		else if(isset($_SERVER['HTTP_FORWARDED_FOR']))
			$ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
		else if(isset($_SERVER['HTTP_FORWARDED']))
			$ipaddress = $_SERVER['HTTP_FORWARDED'];
		else if(isset($_SERVER['REMOTE_ADDR']))
			$ipaddress = $_SERVER['REMOTE_ADDR'];
		else
			$ipaddress = 'UNKNOWN';
		return $ipaddress;
	}
}

/*
 * Validate Login Attempt
 */
if (!function_exists('validate_login_attempt')){
	function validate_login_attempt(): bool {
		if($_SESSION['captcha']['verify']){
			$_SESSION['ip_address'] = get_client_ip();
			$_SESSION['attempt_count'] = $_SESSION['attempt_count'] > 0 ? $_SESSION['attempt_count'] + 1 : 1;
			return true;
		}
		if($_SESSION['ip_address'] == get_client_ip()){
			$_SESSION['ip_address'] = get_client_ip();
			$_SESSION['attempt_count'] = $_SESSION['attempt_count'] > 0 ? $_SESSION['attempt_count'] + 1 : 1;
			if($_SESSION['attempt_count'] > 3){
				return false;
			}else{
				return true;
			}
		}else{
			$_SESSION['ip_address'] = get_client_ip();
			$_SESSION['attempt_count'] = $_SESSION['attempt_count'] > 0 ? $_SESSION['attempt_count'] + 1 : 1;
			return true;
		}

	}
}


/*
 * Reset Login Attempt
 */
if (!function_exists('reset_login_attempt')){
	function reset_login_attempt() {
		unset($_SESSION['ip_address']);
		unset($_SESSION['attempt_count']);
		unset($_SESSION['captcha']);
	}
}

/*
 * Generate Captcha
 */
if (!function_exists('generate_captcha')){
	function generate_captcha() {
		$_SESSION['captcha']['value1'] 	= rand(10, 90);
		$_SESSION['captcha']['value2'] 	= rand(10, 90);
		$_SESSION['captcha']['answer'] 	= $_SESSION['captcha']['value1'] + $_SESSION['captcha']['value2'];
		$_SESSION['captcha']['label'] 	= md5(sha1($_SESSION['captcha']['value1'] + $_SESSION['captcha']['value2']));
		return $_SESSION['captcha'];
	}
}

/*
 * Validate Captcha
 */
if (!function_exists('validate_captcha')){
	function validate_captcha($user_captcha): bool {
		$_SESSION['attempt_count'] = $_SESSION['attempt_count'] ?? 0;
		if($_SESSION['attempt_count'] > 3){
			$_SESSION['captcha']['verify'] = $_SESSION['captcha']['answer'] == $user_captcha[$_SESSION['captcha']['label']];
			$return = $_SESSION['captcha']['verify'];
			if($return){
				reset_login_attempt();
			}
		}else{
			$return = true;
		}
		return $return;
	}
}

/*
 * Get User Role
 */
if (! function_exists('get_user_role_title')) {
	function get_user_role_title($role_id = '') {
		$CI	=&	get_instance();
		$CI->load->database();
		$CI->db->where('id', $role_id);
		return $CI->db->get('roles')->row('title') ?? '';
	}
}

/*
 * Is Logged In
 */
if (! function_exists('check_login')) {
	function check_login(): bool {
		if (isset($_SESSION['user_id']) && $_SESSION['user_id'] > 0) {
			return TRUE;
		}else{
			redirect(base_url('login/index'));
		}
		return FALSE;
	}
}

/*
 * Log Out
 */
if (! function_exists('logout')) {
	function logout(): void {
		$CI	=&	get_instance();
		$CI->session->sess_destroy();
		redirect(base_url('login/index/'));
	}
}

/*
 * Get User ID
 */
if (! function_exists('get_user_id')) {
	function get_user_id() {
		return $_SESSION['user_id'];
	}
}

/*
 * Get User Role ID
 */
if (! function_exists('get_role_id')) {
	function get_role_id() {
		return $_SESSION['role_id'];
	}
}

/*
 * Get School ID
 */
if (! function_exists('get_school_id')) {
	function get_school_id() {
		return $_SESSION['school_id'] ?? 0;
	}
}

/*
 * Get School NAME
 */
if (! function_exists('get_school_name')) {
	function get_school_name() {
		$school_id = get_school_id();

        $CI	=&	get_instance();
        $CI->load->database();
        $CI->db->where('id', $school_id);
        return $CI->db->get('school')->row('title') ?? '';
	}
}

/*
 * Is Super Admin
 */
if (! function_exists('is_super_admin')) {
	function is_super_admin(): bool {
		return get_role_id() == 0;
	}
}

/*
 * Is Admin
 */
if (! function_exists('is_admin')) {
	function is_admin(): bool {
		return get_role_id() == 1;
	}
}

/*
 * Is HR Admin
 */
if (! function_exists('is_hr')) {
	function is_hr(): bool {
		return get_role_id() == 2;
	}
}

/*
 * Is Project Manager
 */
if (! function_exists('is_app_lead')) {
	function is_app_lead(): bool {
		return get_user_id() == 5;
	}
}
if (! function_exists('is_team_lead')) {
	function is_team_lead(): bool {
		return get_role_id() == 3;
	}
}
if (! function_exists('is_project_manager')) {
	function is_project_manager(): bool {
		return get_role_id() == 3;
	}
}

/*
 * Is Employee
 */
if (! function_exists('is_employee')) {
	function is_employee(): bool {
		return get_role_id() == 4;
	}
}

/*
 * Is Trainee
 */
if (! function_exists('is_trainee')) {
	function is_trainee(): bool {
		return get_role_id() == 6;
	}
}

/*
 * Is BDE
 */
if (! function_exists('is_bde')) {
	function is_bde(): bool {
		return get_role_id() == 9;
	}
}

/*
 * Is Technical Support
 */
if (! function_exists('is_technical_support')) {
	function is_technical_support(): bool {
		return get_role_id() == 5;
	}
}


/*
 * Is Tester
 */
if (! function_exists('is_tester')) {
	function is_tester(): bool {
		$testers = [53, 55, 30];
		return in_array(get_user_id(), $testers);
	}
}


/*
 * Check role permission
 */
if (! function_exists('check_role_permission')) {
	function check_role_permission($permission = '') {
		$role_id = get_role_id();
	}
}

/*
 * Has Permission
 */
if (! function_exists('has_permission')) {
	function has_permission($permission = '') {
		if (is_super_admin()){ // check if super admin
            return has_permission_super_admin($permission);
		}elseif (is_admin()){ // check if admin
            return has_permission_admin($permission);
		}elseif (is_hr()){ // check if HR admin
			return has_permission_hr($permission);
		}elseif (is_project_manager()){ // check if project manager
			return has_permission_project_manager($permission);
		}elseif (is_team_lead()){ // check if project manager
			return has_permission_team_lead($permission);
		}elseif (is_app_lead()){ // check if project manager
			return has_permission_app_lead($permission);
		}elseif (is_employee()){ // check if employee
			return has_permission_employee($permission);
		}elseif (is_technical_support()){ // check if technical support
			return has_permission_technical_support($permission);
		}elseif (is_trainee()){ // check if technical support
			return has_permission_trainee($permission);
		}elseif (is_bde()){ // check if bde
			return has_permission_bde($permission);
		}
	}
}



if (! function_exists('has_permission_super_admin')) {
	function has_permission_super_admin($permission = ''): bool {
		$permissions = [
			'student/index',
			'time_log/daily_log',
		];
        return !in_array($permission, $permissions);
	}
}

if (! function_exists('has_permission_admin')) {
	function has_permission_admin($permission = ''): bool {
		$permissions = [
            'dashboard/index',
            'reports',
            'pending_report_employee/index',
            'pending_report_project/index',
            'time_log_report/daily_report',
            'work_report',
            'users/index',
            'users/add',
            'users/edit',
            'users/delete',
            'tasks/index',
            'tasks/add',
            'tasks/edit',
            'tasks/delete',
            'task_assign',
            'todo/index',
            'todo/add',
            'todo/edit',
            'todo/delete',
            'projects/index',
            'projects/add',
            'projects/edit',
            'clients/index',
            'clients/add',
            'project_schedule/add',
            'project_schedule/edit',
            'project_schedule/delete',
            'project_schedule/index',
            'tasks_report/index',
            'project_report/project_employee',
            'attendance_report/attendance_status_report',
            'attendance_report/late_coming_report',
		];
        return in_array($permission, $permissions);
	}
}
if (! function_exists('has_permission_hr')) {
    function has_permission_hr($permission = ''): bool {
        $permissions = [
            'dashboard/index',
            'reports',
            'pending_report_employee/index',
            'pending_report_project/index',
            'time_log_report/daily_report',
            'tasks_report/task_overview',
            'work_report',
            'users/index',
            'users/add',
            'users/edit',
            'users/delete',
            'tasks/index',
            'tasks/add',
            'tasks/edit',
            'tasks/delete',
            'task_assign',
            'todo/index',
            'todo/add',
            'todo/edit',
            'todo/delete',
            'projects/index',
            'projects/add',
            'clients/index',
            'clients/add',
            'time_log/daily_log',
            'documents/index',
            'documents/add',
            'documents/edit',
            'documents/delete',
            'project_schedule/add',
            'project_schedule/edit',
            'project_schedule/delete',
            'project_schedule/index',
            'tasks_report/index',
            'project_report/project_employee',
            'attendance_report/attendance_status_report',
            'attendance_report/late_coming_report',
        ];
        return in_array($permission, $permissions);
    }
}
if (! function_exists('has_permission_team_lead')) {
    function has_permission_team_lead($permission = ''): bool {
        $permissions = [
            'dashboard/index',
            'reports',
            'pending_report_employee/index',
            'pending_report_project/index',
            'time_log_report/daily_report',
            'tasks_report/task_overview',
            'work_report',
            'users/index',
            'users/add',
            'users/edit',
            'users/delete',
            'tasks/index',
            'tasks/add',
            'tasks/edit',
            'tasks/delete',
            'task_assign',
            'todo/index',
            'todo/add',
            'todo/edit',
            'todo/delete',
            'projects/index',
            'projects/add',
            'projects/edit',
            'clients/index',
            'clients/add',
        ];
        return in_array($permission, $permissions);
    }
}

if (! function_exists('has_permission_project_manager')) {
    function has_permission_project_manager($permission = ''): bool {
        $permissions = [
            'settings',
            'dashboard/index',
            'reports',
            'pending_report_employee/index',
            'pending_report_project/index',
            'time_log_report/daily_report',
            'tasks_report/task_overview',
            'work_report',
            'users/index',
            'users/add',
            'users/edit',
            'users/delete',
            'tasks/index',
            'tasks/add',
            'tasks/edit',
            'tasks/delete',
            'task_assign',
            'todo/index',
            'todo/add',
            'todo/edit',
            'todo/delete',
            'projects/index',
            'projects/add',
            'projects/edit',
            'clients/index',
            'clients/add',
            'documents/index',
            'documents/add',
            'documents/edit',
            'documents/delete',
            'project_schedule/add',
            'project_schedule/edit',
            'project_schedule/delete',
            'project_schedule/index',
            'tasks_report/index',
            'time_log/daily_log',
            'teams/index',
            'teams/add',
            'teams/edit',
            'project_phases/index',
            'project_phases/add',
            'project_phases/edit',
            'project_phases/delete',
            'project_assign/index',
            'project_assign/add',
            'project_assign/edit',
            'aptitude_summary/index',
            'tickets/index',
            'tickets/add',
            'tickets/edit',
            'tickets/delete',
            'tickets/view',
            'project_report/project_employee',
            'attendance_report/attendance_status_report',
            'attendance_report/late_coming_report'
        ];
        return in_array($permission, $permissions);
    }
}
if (! function_exists('has_permission_employee')) {
    function has_permission_employee($permission = ''): bool {
        $permissions = [
            'dashboard/index',
            'tasks/add',
        ];
        return in_array($permission, $permissions);
    }
}

if (! function_exists('has_permission_app_lead')) {
    function has_permission_app_lead($permission = ''): bool {
        $permissions = [
            'dashboard/index',
            'tasks/add',
            'team_members/index',
        ];
        return in_array($permission, $permissions);
    }
}

if (! function_exists('has_permission_trainee')) {
    function has_permission_trainee($permission = ''): bool {
        $permissions = [
            'dashboard/index',
        ];
        return in_array($permission, $permissions);
    }
}

if (! function_exists('has_permission_bde')) {
    function has_permission_bde($permission = ''): bool {
        $permissions = [
            'dashboard/index',
            'tasks/add',
            'projects/index',
            'projects/add',
            'clients/index',
            'clients/add',
            'project_schedule/index',
            'project_schedule/add'
        ];
        return in_array($permission, $permissions);
    }
}

if (! function_exists('has_permission_technical_support')) {
    function has_permission_technical_support($permission = ''): bool {
        $permissions = [
            'reports',
            'pending_report_project/index',
            'dashboard/index',
            'tasks/index',
            'tasks/add',
            'tasks/edit',
            'tasks/delete',
            'projects/index',
            'projects/add',
            'projects/edit',
            'clients/index',
            'clients/add',
            'project_schedule/add',
            'project_schedule/edit',
            'project_schedule/delete',
            'project_schedule/index',
            'tickets/index',
            'tickets/add', 
            'tickets/edit',
            'tickets/delete',
            'tickets/view',
        ];
        return in_array($permission, $permissions);
    }
}
