<div class="max-width-1500 p-3">
    <!-- Tasks Section Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="modern-header-card">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="d-flex align-items-center">
                        <div class="header-icon">
                            <i class="bi bi-kanban"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="mb-0 font-weight-bold text-dark">Task Management</h4>
                            <p class="text-muted mb-0">Monitor and manage your team's tasks</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <button class="btn btn-primary btn-sm mr-2" onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add', 'Add New Task')">
                            <i class="bi bi-plus-circle"></i> Add Task
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="window.location.href='<?= base_url('app/tasks/index'); ?>'">
                            <i class="bi bi-list-ul"></i> View All
                        </button>
                    </div>
                </div>
                
                <!-- Task Statistics -->
                <div class="row">
                    <div class="col-md-3 col-6">
                        <div class="stat-card stat-pending">
                            <div class="stat-icon">
                                <i class="bi bi-clock-history"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number"><?=$tasks_pending_count ?? 0?></div>
                                <div class="stat-label">Pending</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-card stat-overdue">
                            <div class="stat-icon">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number"><?=$tasks_pending_due_count ?? 0?></div>
                                <div class="stat-label">Overdue</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-card stat-progress">
                            <div class="stat-icon">
                                <i class="bi bi-arrow-repeat"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number"><?= isset($tasks) ? count(array_filter($tasks, function($t) { return $t['task_status'] == 'assigned'; })) : 0 ?></div>
                                <div class="stat-label">In Progress</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="stat-card stat-completed">
                            <div class="stat-icon">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number"><?= isset($tasks) ? count(array_filter($tasks, function($t) { return $t['task_status'] == 'completed'; })) : 0 ?></div>
                                <div class="stat-label">Completed</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 col-12">
            <!-- Modern Task Cards -->
            <div class="tasks-container">
                <div class="container-header">
                    <h5 class="mb-0"><i class="bi bi-list-task"></i> Active Tasks</h5>
                    <div class="filter-tabs">
                        <button class="filter-btn active" data-filter="all">All</button>
                        <button class="filter-btn" data-filter="pending">Pending</button>
                        <button class="filter-btn" data-filter="assigned">Assigned</button>
                        <button class="filter-btn" data-filter="testing">Testing</button>
                    </div>
                </div>

                <div class="tasks-grid" id="tasks-grid">
                    <?php
                    if (isset($tasks) && isset($projects) && isset($users)){
                        foreach ($tasks as $task){
                            $priority_class = '';
                            $due_class = '';
                            $current_date = date('Y-m-d');
                            
                            switch($task['task_priority']) {
                                case 'critical': $priority_class = 'priority-critical'; break;
                                case 'high': $priority_class = 'priority-high'; break;
                                case 'medium': $priority_class = 'priority-medium'; break;
                                case 'low': $priority_class = 'priority-low'; break;
                            }
                            
                            if ($task['due_date'] < $current_date) {
                                $due_class = 'overdue';
                            } elseif ($task['due_date'] == $current_date) {
                                $due_class = 'due-today';
                            }
                            
                            $completed_class = ($task['task_status'] == 'completed') ? 'task-completed' : '';
                            ?>
                            <div class="modern-task-card <?=$priority_class?> <?=$due_class?> <?=$completed_class?>" 
                                 data-status="<?=$task['task_status']?>" 
                                 onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/task_details_employee'); ?>', 'Task Details')">
                                
                                <!-- Task Header -->
                                <div class="task-header">
                                    <div class="task-id">
                                        <span class="task-number">TRG-<?=$task['id']?></span>
                                    </div>
                                    <div class="task-status">
                                        <span class="status-badge status-<?=$task['task_status']?>">
                                            <i class="<?= get_task_status_icon($task['task_status']) ?>"></i>
                                            <?= ucfirst($task['task_status']) ?>
                                        </span>
                                    </div>
                                </div>

                                <!-- Task Content -->
                                <div class="task-content">
                                    <h6 class="task-title"><?= htmlspecialchars($task['title']) ?></h6>
                                    
                                    <?php if (!empty($task['description'])): ?>
                                    <p class="task-description"><?= htmlspecialchars(substr($task['description'], 0, 100)) ?><?= strlen($task['description']) > 100 ? '...' : '' ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="task-meta">
                                        <div class="meta-item">
                                            <i class="bi bi-folder"></i>
                                            <span><?= isset($projects[$task['project_id']]) ? htmlspecialchars($projects[$task['project_id']]) : 'N/A' ?></span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="bi bi-person"></i>
                                            <span><?= isset($users[$task['created_by']]) ? htmlspecialchars($users[$task['created_by']]) : 'N/A' ?></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Task Footer -->
                                <div class="task-footer">
                                    <div class="task-priority">
                                        <span class="priority-badge priority-<?=$task['task_priority']?>">
                                            <?= ucfirst($task['task_priority']) ?>
                                        </span>
                                    </div>
                                    <div class="task-due">
                                        <i class="bi bi-calendar-event"></i>
                                        <span><?= DateTime::createFromFormat('Y-m-d', $task['due_date'])->format('M d, Y') ?></span>
                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                    } else {
                        echo '<div class="no-tasks"><i class="bi bi-inbox"></i><p>No tasks available</p></div>';
                    }
                    ?>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-12">
            <!-- Quick Actions & Summary -->
            <div class="sidebar-section">
                <div class="quick-actions-card">
                    <h5 class="section-title"><i class="bi bi-lightning"></i> Quick Actions</h5>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add', 'Add New Task')">
                            <i class="bi bi-plus-circle"></i>
                            <span>New Task</span>
                        </button>
                        <button class="action-btn" onclick="window.location.href='<?= base_url('app/tasks/index'); ?>'">
                            <i class="bi bi-list-ul"></i>
                            <span>View All Tasks</span>
                        </button>
                        <button class="action-btn" onclick="window.location.href='<?= base_url('app/tasks/index?task_status=pending'); ?>'">
                            <i class="bi bi-clock-history"></i>
                            <span>Pending Tasks</span>
                        </button>
                        <button class="action-btn" onclick="window.location.href='<?= base_url('app/tasks/index?task_status=assigned'); ?>'">
                            <i class="bi bi-person-check"></i>
                            <span>My Tasks</span>
                        </button>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="activity-card">
                    <h5 class="section-title"><i class="bi bi-clock-history"></i> Recent Activity</h5>
                    <div class="activity-list">
                        <?php
                        if (isset($tasks) && count($tasks) > 0) {
                            $recent_tasks = array_slice($tasks, 0, 5);
                            foreach ($recent_tasks as $task) {
                                $time_diff = time() - strtotime($task['updated_on']);
                                $time_ago = '';
                                if ($time_diff < 3600) {
                                    $time_ago = floor($time_diff / 60) . ' min ago';
                                } elseif ($time_diff < 86400) {
                                    $time_ago = floor($time_diff / 3600) . ' hours ago';
                                } else {
                                    $time_ago = floor($time_diff / 86400) . ' days ago';
                                }
                                ?>
                                <div class="activity-item">
                                    <div class="activity-icon status-<?=$task['task_status']?>">
                                        <i class="<?= get_task_status_icon($task['task_status']) ?>"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title"><?= htmlspecialchars(substr($task['title'], 0, 30)) ?><?= strlen($task['title']) > 30 ? '...' : '' ?></div>
                                        <div class="activity-meta"><?= $time_ago ?></div>
                                    </div>
                                </div>
                                <?php
                            }
                        } else {
                            echo '<div class="no-activity"><i class="bi bi-inbox"></i><p>No recent activity</p></div>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<style>
    /* Modern Task Dashboard Styles */
    .modern-header-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 30px;
        color: white;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .header-icon {
        width: 60px;
        height: 60px;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }
    
    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-left: 4px solid #e0e0e0;
        display: flex;
        align-items: center;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .stat-card.stat-pending { border-left-color: #ff6b6b; }
    .stat-card.stat-overdue { border-left-color: #ee5a52; }
    .stat-card.stat-progress { border-left-color: #4ecdc4; }
    .stat-card.stat-completed { border-left-color: #45b7d1; }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        margin-right: 15px;
    }
    
    .stat-pending .stat-icon { background: rgba(255,107,107,0.1); color: #ff6b6b; }
    .stat-overdue .stat-icon { background: rgba(238,90,82,0.1); color: #ee5a52; }
    .stat-progress .stat-icon { background: rgba(78,205,196,0.1); color: #4ecdc4; }
    .stat-completed .stat-icon { background: rgba(69,183,209,0.1); color: #45b7d1; }
    
    .stat-number {
        font-size: 24px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 12px;
        color: #7f8c8d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .tasks-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 20px;
    }
    
    .container-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f8f9fa;
    }
    
    .container-header h5 {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
    }
    
    .filter-tabs {
        display: flex;
        gap: 8px;
    }
    
    .filter-btn {
        padding: 8px 16px;
        border: 2px solid #e9ecef;
        background: white;
        border-radius: 25px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #6c757d;
    }
    
    .filter-btn:hover, .filter-btn.active {
        background: #667eea;
        border-color: #667eea;
        color: white;
    }
    
    .tasks-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }
    
    .modern-task-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid #e9ecef;
        position: relative;
        overflow: hidden;
    }
    
    .modern-task-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .modern-task-card.priority-critical { border-left: 4px solid #dc3545; }
    .modern-task-card.priority-high { border-left: 4px solid #fd7e14; }
    .modern-task-card.priority-medium { border-left: 4px solid #ffc107; }
    .modern-task-card.priority-low { border-left: 4px solid #28a745; }
    
    .modern-task-card.overdue {
        background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
        border-color: #fc8181;
    }
    
    .modern-task-card.due-today {
        background: linear-gradient(135deg, #fff7ed 0%, #feebc8 100%);
        border-color: #f6ad55;
    }
    
    .modern-task-card.task-completed {
        opacity: 0.7;
        background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
    }
    
    .task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .task-number {
        font-size: 11px;
        font-weight: 600;
        color: #6c757d;
        background: #f8f9fa;
        padding: 4px 8px;
        border-radius: 6px;
    }
    
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 11px;
        font-weight: 500;
        text-transform: uppercase;
    }
    
    .status-badge.status-pending { background: #fff5f5; color: #c53030; }
    .status-badge.status-assigned { background: #ebf8ff; color: #2b6cb0; }
    .status-badge.status-testing { background: #f7fafc; color: #4a5568; }
    .status-badge.status-completed { background: #f0fff4; color: #22543d; }
    .status-badge.status-on_hold { background: #fffbeb; color: #d69e2e; }
    
    .task-title {
        font-size: 16px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 10px;
        line-height: 1.4;
    }
    
    .task-description {
        font-size: 13px;
        color: #718096;
        line-height: 1.5;
        margin-bottom: 15px;
    }
    
    .task-meta {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 15px;
    }
    
    .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: #718096;
    }
    
    .meta-item i {
        width: 14px;
        color: #a0aec0;
    }
    
    .task-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 15px;
        border-top: 1px solid #e2e8f0;
    }
    
    .priority-badge {
        font-size: 10px;
        font-weight: 600;
        text-transform: uppercase;
        padding: 3px 8px;
        border-radius: 12px;
    }
    
    .priority-badge.priority-critical { background: #fed7d7; color: #c53030; }
    .priority-badge.priority-high { background: #feebc8; color: #dd6b20; }
    .priority-badge.priority-medium { background: #fefcbf; color: #d69e2e; }
    .priority-badge.priority-low { background: #c6f6d5; color: #22543d; }
    
    .task-due {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 11px;
        color: #718096;
    }
    
    .no-tasks {
        text-align: center;
        padding: 60px 20px;
        color: #a0aec0;
    }
    
    .no-tasks i {
        font-size: 48px;
        margin-bottom: 15px;
    }
    
    /* Sidebar Styles */
    .sidebar-section {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }
    
    .quick-actions-card, .activity-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    }
    
    .section-title {
        color: #2c3e50;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .action-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }
    
    .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        padding: 20px 15px;
        border: 2px solid #e9ecef;
        background: white;
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        color: #6c757d;
    }
    
    .action-btn:hover {
        background: #f8f9fa;
        border-color: #667eea;
        color: #667eea;
        transform: translateY(-2px);
    }
    
    .action-btn i {
        font-size: 20px;
    }
    
    .action-btn span {
        font-size: 12px;
        font-weight: 500;
        text-align: center;
    }
    
    .activity-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .activity-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        border-radius: 8px;
        transition: background-color 0.3s ease;
    }
    
    .activity-item:hover {
        background: #f8f9fa;
    }
    
    .activity-icon {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: white;
    }
    
    .activity-icon.status-pending { background: #ff6b6b; }
    .activity-icon.status-assigned { background: #4ecdc4; }
    .activity-icon.status-testing { background: #a0aec0; }
    .activity-icon.status-completed { background: #45b7d1; }
    .activity-icon.status-on_hold { background: #ffd93d; }
    
    .activity-title {
        font-size: 13px;
        font-weight: 500;
        color: #2d3748;
        margin-bottom: 3px;
    }
    
    .activity-meta {
        font-size: 11px;
        color: #a0aec0;
    }
    
    .no-activity {
        text-align: center;
        padding: 40px 20px;
        color: #a0aec0;
    }
    
    .no-activity i {
        font-size: 32px;
        margin-bottom: 10px;
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
        .tasks-grid {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            grid-template-columns: 1fr;
        }
        
        .container-header {
            flex-direction: column;
            gap: 15px;
        }
        
        .modern-header-card {
            padding: 20px;
        }
        
        .stat-card {
            padding: 15px;
        }
    }
    
    /* Legacy styles for compatibility */
    .single_task_item:hover, .single_todo_item:hover {
        opacity: 0.6;
        cursor: pointer;
    }
    
    .task_description {
        font-size: 14px;
        overflow: hidden;
        max-width: 100%;
        text-overflow: ellipsis;
    }
</style>

<script>
// Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const taskCards = document.querySelectorAll('.modern-task-card');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.dataset.filter;
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter tasks
            taskCards.forEach(card => {
                const taskStatus = card.dataset.status;
                if (filter === 'all' || taskStatus === filter) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
});
</script>


