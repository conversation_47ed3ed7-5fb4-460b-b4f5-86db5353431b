<?php
    $start_date_formatted = DateTime::createFromFormat('Y-m-d', $start_date)->format('d-m-Y');
    $end_date_formatted = DateTime::createFromFormat('Y-m-d', $end_date)->format('d-m-Y');
?>

<div class="container-fluid p-0" style="min-width: 1200px;">
    
    
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title">
                <?= strtoupper($page_title ?? '')?>
                (<?= "{$start_date_formatted} to {$end_date_formatted}" ?>)
            </h3>
            <div class="float-right">
                <input type="date" name="end_date" id="end_date" onchange="get_value()" value="<?=$end_date?>"
                       class="form-control date_input">
            </div>
            <div class="float-right mr-2">
                <input type="date" name="start_date" id="start_date" onchange="get_value()" value="<?=$start_date?>"
                       class="form-control date_input">
            </div>
        </div>
        
        <!-- /.card-header -->
        <div class="card-body p-1" id="download_area">
            <?php if (isset($users) && isset($attendance_report) && isset($date_range)) { ?>
                <div class="shadow-pro pt-2 pb-2 bg-white p-1">
                    <div class="p-3 text-center bg-primary" style="background-color: rgb(234,254,244)">
                        <div style="font-size: 22px; font-weight: bold;">Attendance Status Report</div>
                        <div style="font-size: 20px;">
                            (<?= "{$start_date_formatted} to {$end_date_formatted}" ?>)
                        </div>
                    </div>

                    <!-- Legend -->
                    <div class="p-3 bg-light">
                        <div class="row">
                            <div class="col-md-2">
                                <span class="attendance-legend attendance-P"></span> Present (P)
                            </div>
                            <div class="col-md-2">
                                <span class="attendance-legend attendance-A"></span> Absent (A)
                            </div>
                            <div class="col-md-2">
                                <span class="attendance-legend attendance-WH"></span> Work From Home (WH)
                            </div>
                            <div class="col-md-2">
                                <span class="attendance-legend attendance-OF"></span> Off (OF)
                            </div>
                            <div class="col-md-2">
                                <span class="attendance-legend attendance-OD"></span> On Duty (OD)
                            </div>
                            <div class="col-md-2">
                                <span class="attendance-legend attendance-HD"></span> Half Day (HD)
                            </div>
                        </div>
                        <?php if (count($date_range) > 15) { ?>
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="alert alert-info alert-sm mb-0" style="padding: 8px 12px; font-size: 13px;">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Tip:</strong> Use horizontal scroll or drag to view all dates. Employee names remain fixed for easy reference.
                                </div>
                            </div>
                        </div>
                        <?php } ?>
                    </div>

                    <div class="table-container-wrapper">
                        <div class="table-responsive-horizontal">
                            <table class="table table-bordered table-sm attendance-report-table">
                            <thead>
                                <tr style="background-color: #f1f2f8; font-size: 12px">
                                    <th rowspan="2" class="employee-header">Employee</th>
                                    <th colspan="<?= count($date_range) ?>" class="text-center" style="background-color: #e3f2fd;">Attendance by Date</th>
                                    <th colspan="6" class="text-center" style="background-color: #f3e5f5;">Summary</th>
                                </tr>
                                <tr style="background-color: #f8f9fa; font-size: 11px">
                                    <?php foreach ($date_range as $date) {
                                        $date_obj = DateTime::createFromFormat('Y-m-d', $date);
                                        $day = $date_obj->format('d');
                                        $month = $date_obj->format('M');
                                    ?>
                                        <th class="date-header" title="<?= $date_obj->format('d-m-Y') ?>" style="background-color: #e3f2fd;">
                                            <div style="line-height: 1.2;">
                                                <strong><?= $day ?></strong><br>
                                                <small style="font-size: 9px;"><?= $month ?></small>
                                            </div>
                                        </th>
                                    <?php } ?>
                                    <th class="summary-header" style="background-color: #f3e5f5;">P</th>
                                    <th class="summary-header" style="background-color: #f3e5f5;">A</th>
                                    <th class="summary-header" style="background-color: #f3e5f5;">WH</th>
                                    <th class="summary-header" style="background-color: #f3e5f5;">OF</th>
                                    <th class="summary-header" style="background-color: #f3e5f5;">OD</th>
                                    <th class="summary-header" style="background-color: #f3e5f5;">HD</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if (empty($attendance_report)) {
                                    ?>
                                    <tr>
                                        <td colspan="<?= count($date_range) + 7 ?>" class="text-center text-muted">
                                            <h4>No attendance records found for the selected date range</h4>
                                        </td>
                                    </tr>
                                    <?php
                                } else {
                                    foreach ($users as $user) {
                                        $user_id = $user['id'];
                                        $user_data = $attendance_report[$user_id] ?? null;
                                        if (!$user_data) continue;
                                        
                                        // Check if user is inactive
                                        $is_inactive = $user['employee_status'] == 0;
                                    ?>
                                        <tr <?= $is_inactive ? 'class="inactive-employee"' : '' ?>>
                                            <td class="employee-name <?= $is_inactive ? 'inactive-name' : '' ?>">
                                                <strong><?= $user['name'] ?></strong>
                                                <?php if ($is_inactive) { ?>
                                                    <br><small class="text-danger"><i class="fas fa-exclamation-triangle"></i> Inactive</small>
                                                <?php } ?>
                                            </td>
                                            <?php foreach ($date_range as $date) {
                                                $day_data = $user_data['daily_attendance'][$date] ?? null;
                                                $attendance = $day_data['attendance'] ?? null;
                                                $remarks = $day_data['remarks'] ?? '';

                                                // Create tooltip text
                                                $tooltip_text = '';
                                                if ($attendance) {
                                                    $attendance_labels = [
                                                        'P' => 'Present',
                                                        'A' => 'Absent', 
                                                        'WH' => 'Work From Home',
                                                        'OF' => 'Off',
                                                        'OD' => 'On Duty',
                                                        'HD' => 'Half Day'
                                                    ];
                                                    $tooltip_text = $attendance_labels[$attendance] ?? $attendance;
                                                    if ($remarks) {
                                                        $tooltip_text .= ' - ' . $remarks;
                                                    }
                                                } else {
                                                    $tooltip_text = 'No record';
                                                }
                                            ?>
                                                <td class="attendance-cell attendance-<?= $attendance ?: 'none' ?>"
                                                    title="<?= $tooltip_text ?>">
                                                    <?php if ($attendance == 'P') { ?>
                                                        <i class="fas fa-check-circle text-success"></i>
                                                    <?php } elseif ($attendance == 'A') { ?>
                                                        <i class="fas fa-times-circle text-danger"></i>
                                                    <?php } elseif ($attendance == 'WH') { ?>
                                                        <i class="fas fa-home text-warning"></i>
                                                    <?php } elseif ($attendance == 'OF') { ?>
                                                        <i class="fas fa-calendar-times text-info"></i>
                                                    <?php } elseif ($attendance == 'OD') { ?>
                                                        <i class="fas fa-briefcase text-primary"></i>
                                                    <?php } elseif ($attendance == 'HD') { ?>
                                                        <i class="fas fa-clock text-secondary"></i>
                                                    <?php } else { ?>
                                                        <i class="fas fa-minus text-muted"></i>
                                                    <?php } ?>
                                                </td>
                                            <?php } ?>
                                            <td class="summary-cell text-success"><strong><?= $user_data['summary']['P'] ?></strong></td>
                                            <td class="summary-cell text-danger"><strong><?= $user_data['summary']['A'] ?></strong></td>
                                            <td class="summary-cell text-warning"><strong><?= $user_data['summary']['WH'] ?></strong></td>
                                            <td class="summary-cell text-info"><strong><?= $user_data['summary']['OF'] ?></strong></td>
                                            <td class="summary-cell text-primary"><strong><?= $user_data['summary']['OD'] ?></strong></td>
                                            <td class="summary-cell text-secondary"><strong><?= $user_data['summary']['HD'] ?></strong></td>
                                        </tr>
                                    <?php
                                    }
                                }
                                ?>
                            </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php } ?>
        </div>
        
        <div class="text-center p-2 d-none">
            <button id="download_image" class="btn btn-primary">Download Image</button>
            <button onclick="exportToCSV()" class="btn btn-success">Export CSV</button>
        </div>
        <div id="canvasContainer">
            <canvas id="canvas" style="display: none"></canvas>
        </div>
        <div id="imageContainer" style="display: none"></div>
    </div>
</div>

<script type="text/javascript">
    function get_value(){
        var start_date = $('#start_date').val();
        var end_date = $('#end_date').val();
        window.location.href='<?=base_url('app/attendance_report/attendance_status_report/?')?>start_date=' + start_date + '&end_date=' + end_date;
    }

    function exportToCSV() {
        // CSV export functionality
        var csv = [];
        var table = document.querySelector('.attendance-report-table');
        var rows = table.querySelectorAll('tr');
        
        for (var i = 0; i < rows.length; i++) {
            var row = [], cols = rows[i].querySelectorAll('td, th');
            
            for (var j = 0; j < cols.length; j++) {
                var cellText = cols[j].innerText.replace(/"/g, '""');
                row.push('"' + cellText + '"');
            }
            
            csv.push(row.join(','));
        }
        
        var csvFile = new Blob([csv.join('\n')], {type: 'text/csv'});
        var downloadLink = document.createElement('a');
        downloadLink.download = 'attendance_status_report_<?= $start_date ?>_to_<?= $end_date ?>.csv';
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = 'none';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
    }

    // Enhanced scrolling experience
    document.addEventListener('DOMContentLoaded', function() {
        const tableContainer = document.querySelector('.table-responsive-horizontal');
        const table = document.querySelector('.attendance-report-table');

        if (tableContainer && table) {
            // Calculate required table width based on columns
            const dateColumns = <?= count($date_range ?? []) ?>;
            const summaryColumns = 6;
            const employeeColumnWidth = 180;
            const dateColumnWidth = 45;
            const summaryColumnWidth = 80;

            const requiredTableWidth = employeeColumnWidth + (dateColumns * dateColumnWidth) + (summaryColumns * summaryColumnWidth);
            const availableWidth = tableContainer.clientWidth;

            // Set table width and determine if scrolling is needed
            if (requiredTableWidth > availableWidth) {
                // Table needs more space than available - enable horizontal scroll
                table.style.width = requiredTableWidth + 'px';
                table.style.minWidth = requiredTableWidth + 'px';
                tableContainer.classList.add('needs-scroll');
            } else {
                // Table fits within available space - use full width
                table.style.width = '100%';
                table.style.minWidth = availableWidth + 'px';
                tableContainer.classList.remove('needs-scroll');

                // Redistribute column widths for better use of space
                redistributeColumnWidths(dateColumns, summaryColumns, availableWidth);
            }

            // Update container max-width based on screen size
            updateContainerWidth();
            // Add smooth scrolling
            tableContainer.style.scrollBehavior = 'smooth';

            // Add keyboard navigation
            tableContainer.addEventListener('keydown', function(e) {
                const scrollAmount = 100;
                switch(e.key) {
                    case 'ArrowLeft':
                        e.preventDefault();
                        this.scrollLeft -= scrollAmount;
                        break;
                    case 'ArrowRight':
                        e.preventDefault();
                        this.scrollLeft += scrollAmount;
                        break;
                }
            });

            // Make table container focusable for keyboard navigation
            tableContainer.setAttribute('tabindex', '0');

            // Add scroll indicators if content overflows
            function updateScrollIndicators() {
                const isScrollable = tableContainer.scrollWidth > tableContainer.clientWidth;
                const isAtStart = tableContainer.scrollLeft <= 0;
                const isAtEnd = tableContainer.scrollLeft >= tableContainer.scrollWidth - tableContainer.clientWidth - 1;

                // Remove existing indicators
                document.querySelectorAll('.scroll-indicator').forEach(el => el.remove());

                if (isScrollable) {
                    if (!isAtEnd) {
                        const rightIndicator = document.createElement('div');
                        rightIndicator.className = 'scroll-indicator scroll-right';
                        rightIndicator.innerHTML = '<i class="fas fa-chevron-right"></i>';
                        tableContainer.parentNode.appendChild(rightIndicator);
                    }

                    if (!isAtStart) {
                        const leftIndicator = document.createElement('div');
                        leftIndicator.className = 'scroll-indicator scroll-left';
                        leftIndicator.innerHTML = '<i class="fas fa-chevron-left"></i>';
                        tableContainer.parentNode.appendChild(leftIndicator);
                    }
                }
            }

            // Update indicators and shadow effects on scroll
            tableContainer.addEventListener('scroll', function() {
                updateScrollIndicators();

                // Update shadow effect based on scroll position
                const scrollLeft = this.scrollLeft;
                this.setAttribute('data-scroll', scrollLeft);

                // Add visual feedback for scrolling
                if (scrollLeft > 0) {
                    this.classList.add('is-scrolled');
                } else {
                    this.classList.remove('is-scrolled');
                }
            });

            window.addEventListener('resize', function() {
                updateScrollIndicators();
                updateContainerWidth();
                // Recalculate table layout on resize
                const event = new Event('DOMContentLoaded');
                document.dispatchEvent(event);
            });
            updateScrollIndicators(); // Initial call
        }

        // Function to update container width based on screen size
        function updateContainerWidth() {
            const container = document.querySelector('.table-responsive-horizontal');
            if (container) {
                const screenWidth = window.innerWidth;
                let maxWidth;

                if (screenWidth <= 768) {
                    // Mobile: Use almost full width
                    maxWidth = 'calc(100vw - 40px)';
                } else if (screenWidth <= 1024) {
                    // Tablet: Account for sidebar
                    maxWidth = 'calc(100vw - 200px)';
                } else {
                    // Desktop: Account for sidebar and margins
                    maxWidth = 'calc(100vw - 280px)';
                }

                container.style.maxWidth = maxWidth;
            }
        }

        // Function to redistribute column widths when table fits in container
        function redistributeColumnWidths(dateColumns, summaryColumns, availableWidth) {
            const employeeWidth = 180;
            const remainingWidth = availableWidth - employeeWidth;
            const totalScrollableColumns = dateColumns + summaryColumns;

            if (totalScrollableColumns > 0) {
                const avgColumnWidth = Math.max(45, Math.floor(remainingWidth / totalScrollableColumns));

                // Update date column widths
                document.querySelectorAll('.date-header, .attendance-cell').forEach(cell => {
                    cell.style.width = avgColumnWidth + 'px';
                    cell.style.minWidth = avgColumnWidth + 'px';
                    cell.style.maxWidth = avgColumnWidth + 'px';
                });

                // Update summary column widths (slightly wider)
                const summaryWidth = Math.max(80, avgColumnWidth + 20);
                document.querySelectorAll('.summary-header, .summary-cell').forEach(cell => {
                    cell.style.width = summaryWidth + 'px';
                    cell.style.minWidth = summaryWidth + 'px';
                    cell.style.maxWidth = summaryWidth + 'px';
                });
            }
        }
    });
</script>

<style>
    .date_input{
        font-size: 18px!important;border-radius: 40px!important;width: 150px;color:#fff!important;background-image: linear-gradient(90deg, #23384E, #166686)!important;
        cursor: pointer !important;
    }
    .date_input:hover{
        background-image: linear-gradient(90deg, #166686, #23384E)!important;
    }

    .attendance-report-table {
        font-size: 12px;
    }

    .employee-header {
        min-width: 180px;
        background-color: #e9ecef !important;
        vertical-align: middle;
        font-weight: bold;
        text-align: left;
        padding: 12px 15px !important;
    }

    .employee-name {
        background-color: #f8f9fa;
        min-width: 180px;
        vertical-align: middle;
        text-align: left;
        padding: 12px 15px !important;
        border-right: 3px solid #007bff;
    }

    .inactive-employee {
        background-color: #ffe6e6 !important;
    }

    .inactive-name {
        background-color: #ffcccc !important;
        border-right: 3px solid #dc3545 !important;
    }

    .date-header {
        min-width: 35px;
        text-align: center;
        background-color: #e9ecef !important;
    }

    .summary-header {
        min-width: 60px;
        text-align: center;
        background-color: #d1ecf1 !important;
    }

    .attendance-cell {
        text-align: center;
        vertical-align: middle;
        width: 35px;
        height: 35px;
        padding: 5px !important;
    }

    .summary-cell {
        text-align: center;
        vertical-align: middle;
        background-color: #f8f9fa;
    }

    /* Attendance status colors */
    .attendance-P {
        background-color: #d4edda !important; /* Green for Present */
    }

    .attendance-A {
        background-color: #f8d7da !important; /* Red for Absent */
    }

    .attendance-WH {
        background-color: #fff3cd !important; /* Yellow for Work From Home */
    }

    .attendance-OF {
        background-color: #d1ecf1 !important; /* Light blue for Off */
    }

    .attendance-OD {
        background-color: #b3d9ff !important; /* Medium blue for On Duty */
    }

    .attendance-HD {
        background-color: #cce5ff !important; /* Light blue for Half Day */
    }

    .attendance-none {
        background-color: #ffffff !important; /* White for no record */
    }

    .attendance-legend {
        display: inline-block;
        width: 20px;
        height: 15px;
        margin-right: 5px;
        border: 1px solid #ccc;
    }

    .table-container-wrapper {
        position: relative;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        background: white;
        width: 100%;
    }

    .table-responsive-horizontal {
        max-height: 600px;
        overflow-x: auto;
        overflow-y: auto;
        position: relative;
        width: 100%;
        /* Calculate dynamic width based on viewport and sidebar */
        max-width: calc(100vw - 280px); /* Account for sidebar and margins */
    }

    .table-responsive-horizontal .table {
        margin-bottom: 0;
        table-layout: fixed;
        white-space: nowrap;
        /* Dynamic width calculation will be handled by JavaScript */
    }

    .table-responsive-horizontal .table thead th {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        white-space: nowrap;
        padding: 8px 6px;
    }

    /* Fixed employee column */
    .table-responsive-horizontal .employee-name,
    .table-responsive-horizontal .employee-header {
        position: sticky;
        left: 0;
        z-index: 15;
        background-color: #ffffff;
        border-right: 3px solid #007bff;
        width: 180px;
        min-width: 180px;
        max-width: 180px;
        box-shadow: 3px 0 8px rgba(0,123,255,0.15);
        transition: box-shadow 0.3s ease;
    }

    .table-responsive-horizontal .employee-header {
        background-color: #f8f9fa !important;
        z-index: 20;
        font-weight: bold;
        background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%) !important;
    }

    .table-responsive-horizontal .employee-name {
        background-color: #ffffff !important;
        z-index: 15;
    }

    .table-responsive-horizontal .inactive-name {
        background-color: #ffcccc !important;
        border-right: 3px solid #dc3545 !important;
    }

    /* Date columns - responsive width */
    .table-responsive-horizontal .date-header {
        min-width: 40px;
        text-align: center;
        padding: 6px 4px !important;
    }

    .table-responsive-horizontal .attendance-cell {
        min-width: 40px;
        text-align: center;
        padding: 6px 4px !important;
    }

    /* Summary columns - responsive width */
    .table-responsive-horizontal .summary-header {
        min-width: 70px;
        text-align: center;
        padding: 6px 4px !important;
        font-size: 11px;
    }

    .table-responsive-horizontal .summary-cell {
        min-width: 70px;
        text-align: center;
        padding: 6px 4px !important;
        font-size: 12px;
    }
</style>
