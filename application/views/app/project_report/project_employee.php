<?php
$project_id = $this->input->get('project_id') ?? '';
$start_date = $this->input->get('start_date') ?? '';
$end_date = $this->input->get('end_date') ?? '';
?>

<div class="container-fluid p-0">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
    </div>
    
    <div class="card card-primary row mt-3 shadow-none bg-light-grey p-0" style="margin:14px;">
        <div class="card-header">
            <h3 class="card-title text-center">
                <?= strtoupper($page_title ?? '')?>
            </h3>
        </div>
        
        <div class="card-body p-1">
            <form method="get" action="">
                <div class="shadow_pro row p-1" style="max-width: 1200px;">
                    <div class="col-12 col-lg-4 p-2">
                        <select name="project_id" id="project_id" class="form-control select2" required>
                            <option value="">Choose Project</option>
                            <?php
                            if (isset($projects)){
                                foreach ($projects as $id => $title){
                                    $selected = $id == $project_id ? 'selected' : '';
                                    echo "<option value='{$id}' {$selected}>{$title}</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-6 col-lg-3 p-2">
                        <input type="date" name="start_date" id="start_date" value="<?= $start_date ?>"
                               class="form-control" placeholder="Start Date (Optional)">
                        <small class="text-muted">Leave empty for last 30 days</small>
                    </div>
                    <div class="col-6 col-lg-3 p-2">
                        <input type="date" name="end_date" id="end_date" value="<?= $end_date ?>"
                               class="form-control" placeholder="End Date (Optional)">
                        <small class="text-muted">Leave empty for today</small>
                    </div>
                    <div class="col-6 col-lg-2 p-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-chart-bar"></i> Generate Report
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php if (!empty($error_message)): ?>
<div class="p-3">
    <div class="shadow-pro pt-2 pb-2 bg-white p-3">
        <div class="text-center p-4">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Error</h5>
                <p><?= htmlspecialchars($error_message) ?></p>
            </div>
        </div>
    </div>
</div>
<?php elseif (!empty($report_data)): ?>
<div class="p-3">
    <div class="shadow-pro pt-2 pb-2 bg-white p-3">
        <div class="report-header mb-4">
            <div class="row">
                <div class="col-lg-4">
                    <h4 class="report-title mb-1">
                        <i class="fas fa-users text-primary"></i> Project Employee Work Report
                    </h4>
                    <?php if (!empty($projects[$project_id])): ?>
                        <p class="project-name mb-0">
                            <i class="fas fa-project-diagram text-muted"></i>
                            <strong><?= htmlspecialchars($projects[$project_id]) ?></strong>
                        </p>
                        <p class="project-rate mb-0">
                            <small class="text-muted">Project Rate: ₹<?= number_format($project_hourly_rate ?? 0, 2) ?>/hr</small>
                            <?php if (($project_hourly_rate ?? 0) == 0): ?>
                                <span class="text-danger">(Rate not found - check database)</span>
                            <?php endif; ?>
                        </p>
                    <?php endif; ?>
                    <div class="summary-item mt-2">
                        <span class="summary-label">Report Period:</span>
                        <span class="summary-value">
                            <?= date('d M Y', strtotime($actual_start_date ?? $start_date)) ?> to
                            <?= date('d M Y', strtotime($actual_end_date ?? $end_date)) ?>
                        </span>
                    </div>
                </div>
                <div class="col-lg-8">
                    <div class="report-summary">
                        <?php if (!empty($report_data)): ?>
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Selected Period Costs -->
                                <div class="summary-section">
                                    <div class="summary-section-title">Selected Period</div>
                                    <div class="summary-item">
                                        <span class="summary-label">Employee Cost:</span>
                                        <span class="summary-value text-primary font-weight-bold">₹<?= $emp_cost_totals['total_date_range_emp_cost'] ?? '0.00' ?></span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">Project Cost:</span>
                                        <span class="summary-value text-info font-weight-bold">₹<?= $proj_cost_totals['total_date_range_proj_cost'] ?? '0.00' ?></span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">Difference:</span>
                                        <span class="summary-value <?= ($cost_difference['total_date_range_diff'] ?? 0) >= 0 ? 'text-success' : 'text-danger' ?> font-weight-bold">
                                            ₹<?= number_format(abs($cost_difference['total_date_range_diff'] ?? 0), 2) ?>
                                            <?= ($cost_difference['total_date_range_diff'] ?? 0) >= 0 ? '(Profit)' : '(Loss)' ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- All Time Costs -->
                                <div class="summary-section">
                                    <div class="summary-section-title">All Time Total</div>
                                    <div class="summary-item">
                                        <span class="summary-label">Employee Cost:</span>
                                        <span class="summary-value text-primary font-weight-bold">₹<?= $emp_cost_totals['total_worked_emp_cost'] ?? '0.00' ?></span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">Project Cost:</span>
                                        <span class="summary-value text-info font-weight-bold">₹<?= $proj_cost_totals['total_worked_proj_cost'] ?? '0.00' ?></span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">Difference:</span>
                                        <span class="summary-value <?= ($cost_difference['total_worked_diff'] ?? 0) >= 0 ? 'text-success' : 'text-danger' ?> font-weight-bold">
                                            ₹<?= number_format(abs($cost_difference['total_worked_diff'] ?? 0), 2) ?>
                                            <?= ($cost_difference['total_worked_diff'] ?? 0) >= 0 ? '(Profit)' : '(Loss)' ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>



        <!-- Report Table -->
        <div class="report-table-container">
            <table class="table table-bordered table-hover report-table">
                <thead class="table-header">
                    <tr>
                        <th class="employee-name-col">
                            <div class="header-content">
                                <strong>Employee Name</strong>
                                <small class="d-block text-muted">Status & Rate</small>
                            </div>
                        </th>
                        <th class="text-center data-col">
                            <div class="header-content">
                                <strong>Selected Period</strong>
                                <small class="d-block text-muted">Hours | Emp Cost | Proj Cost</small>
                            </div>
                        </th>
                        <th class="text-center data-col">
                            <div class="header-content">
                                <strong>Last 7 Days</strong>
                                <small class="d-block text-muted">Hours | Emp Cost | Proj Cost</small>
                            </div>
                        </th>
                        <th class="text-center data-col">
                            <div class="header-content">
                                <strong>Last 15 Days</strong>
                                <small class="d-block text-muted">Hours | Emp Cost | Proj Cost</small>
                            </div>
                        </th>
                        <th class="text-center data-col">
                            <div class="header-content">
                                <strong>Last 30 Days</strong>
                                <small class="d-block text-muted">Hours | Emp Cost | Proj Cost</small>
                            </div>
                        </th>
                        <th class="text-center data-col">
                            <div class="header-content">
                                <strong>Last 60 Days</strong>
                                <small class="d-block text-muted">Hours | Emp Cost | Proj Cost</small>
                            </div>
                        </th>
                        <th class="text-center data-col">
                            <div class="header-content">
                                <strong>Last 90 Days</strong>
                                <small class="d-block text-muted">Hours | Emp Cost | Proj Cost</small>
                            </div>
                        </th>
                        <th class="text-center data-col">
                            <div class="header-content">
                                <strong>Last 180 Days</strong>
                                <small class="d-block text-muted">Hours | Emp Cost | Proj Cost</small>
                            </div>
                        </th>
                        <th class="text-center data-col total-col">
                            <div class="header-content">
                                <strong>Total Worked</strong>
                                <small class="d-block text-muted">Hours | Emp Cost | Proj Cost</small>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($report_data as $index => $employee): ?>
                    <tr class="employee-row <?= $employee['employee_status'] == 1 ? 'active-employee' : 'inactive-employee' ?>">
                        <td class="employee-name-col">
                            <div class="employee-info">
                                <div class="employee-name">
                                    <?= htmlspecialchars($employee['employee_name']) ?>
                                </div>
                                <div class="employee-details">
                                    <?php if ($employee['employee_status'] == 1): ?>
                                        <span class="status-badge active">Active</span>
                                    <?php else: ?>
                                        <span class="status-badge inactive">Inactive</span>
                                    <?php endif; ?>
                                    <?php if ($employee['hourly_rate'] > 0): ?>
                                        <span class="hourly-rate">₹<?= number_format($employee['hourly_rate'], 2) ?>/hr</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['total_date_range'] ?></div>
                            <div class="cost-display">₹<?= $employee['total_date_range_emp_cost'] ?> | ₹<?= $employee['total_date_range_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['last_7_days'] ?></div>
                            <div class="cost-display">₹<?= $employee['last_7_days_emp_cost'] ?> | ₹<?= $employee['last_7_days_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['last_15_days'] ?></div>
                            <div class="cost-display">₹<?= $employee['last_15_days_emp_cost'] ?> | ₹<?= $employee['last_15_days_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['last_30_days'] ?></div>
                            <div class="cost-display">₹<?= $employee['last_30_days_emp_cost'] ?> | ₹<?= $employee['last_30_days_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['last_60_days'] ?></div>
                            <div class="cost-display">₹<?= $employee['last_60_days_emp_cost'] ?> | ₹<?= $employee['last_60_days_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['last_90_days'] ?></div>
                            <div class="cost-display">₹<?= $employee['last_90_days_emp_cost'] ?> | ₹<?= $employee['last_90_days_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell">
                            <div class="time-display"><?= $employee['last_180_days'] ?></div>
                            <div class="cost-display">₹<?= $employee['last_180_days_emp_cost'] ?> | ₹<?= $employee['last_180_days_proj_cost'] ?></div>
                        </td>
                        <td class="text-center time-cost-cell total-worked-cell">
                            <div class="time-display font-weight-bold"><?= $employee['total_worked'] ?></div>
                            <div class="cost-display font-weight-bold">₹<?= $employee['total_worked_emp_cost'] ?> | ₹<?= $employee['total_worked_proj_cost'] ?></div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <th class="employee-name-col">
                            <div class="total-label">
                                <strong>TOTALS</strong>
                                <small class="d-block text-muted">All Employees</small>
                            </div>
                        </th>
                        <th class="text-center data-col">
                            <div class="total-time"><?= $column_totals['total_date_range'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['total_date_range_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['total_date_range_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center data-col">
                            <div class="total-time"><?= $column_totals['last_7_days'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['last_7_days_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['last_7_days_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center data-col">
                            <div class="total-time"><?= $column_totals['last_15_days'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['last_15_days_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['last_15_days_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center data-col">
                            <div class="total-time"><?= $column_totals['last_30_days'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['last_30_days_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['last_30_days_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center data-col">
                            <div class="total-time"><?= $column_totals['last_60_days'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['last_60_days_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['last_60_days_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center data-col">
                            <div class="total-time"><?= $column_totals['last_90_days'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['last_90_days_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['last_90_days_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center data-col">
                            <div class="total-time"><?= $column_totals['last_180_days'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost">₹<?= $emp_cost_totals['last_180_days_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['last_180_days_proj_cost'] ?? '0.00' ?></div>
                        </th>
                        <th class="text-center total-col">
                            <div class="total-time text-success font-weight-bold"><?= $column_totals['total_worked'] ?? '0 hrs 0 min' ?></div>
                            <div class="total-cost text-success font-weight-bold">₹<?= $emp_cost_totals['total_worked_emp_cost'] ?? '0.00' ?> | ₹<?= $proj_cost_totals['total_worked_proj_cost'] ?? '0.00' ?></div>
                        </th>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        <?php if (empty($report_data)): ?>
        <div class="text-center p-4">
            <div class="alert alert-info">
                <h5>No Data Found</h5>
                <p>No employees worked on this project during the selected date range.</p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php else: ?>
<div class="p-3">
    <div class="shadow-pro pt-2 pb-2 bg-white p-3">
        <div class="text-center p-4">
            <div class="alert alert-warning">
                <h5><i class="fas fa-info-circle"></i> Generate Report</h5>
                <p>Please select a project to generate the employee work report.</p>
                <p><small class="text-muted">Date range is optional - if not specified, the report will show data for the last 30 days.</small></p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
    /* Professional Report Styling */
    .report-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px;
        padding: 20px;
        border: 1px solid #dee2e6;
    }

    .report-title {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .project-name {
        color: #6c757d;
        font-size: 1rem;
    }

    .project-rate {
        color: #495057;
        font-size: 0.9rem;
        margin-top: 4px;
    }

    .report-summary {
        text-align: right;
    }

    .summary-item {
        display: block;
        margin-bottom: 8px;
    }

    .summary-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin-right: 8px;
    }

    .summary-value {
        font-weight: 600;
        color: #2c3e50;
    }

    .summary-section {
        margin-bottom: 15px;
        padding: 12px;
        background: rgba(248, 249, 250, 0.8);
        border-radius: 6px;
        border-left: 4px solid #007bff;
    }

    .summary-section-title {
        font-weight: 700;
        color: #495057;
        font-size: 0.9rem;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .summary-section .summary-item {
        margin-bottom: 6px;
    }

    .summary-section .summary-label {
        font-size: 0.85rem;
    }

    .summary-section .summary-value {
        font-size: 0.9rem;
    }

    /* Table Container */
    .report-table-container {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        background: white;
        overflow-x: auto;
        position: relative;
        width: 100%;
    }

    .report-table {
        margin-bottom: 0;
        font-size: 0.9rem;
        width: 100%;
        table-layout: fixed;
    }

    /* Table Header - Light Version */
    .table-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
        border-bottom: 2px solid #dee2e6;
    }

    .table-header th {
        border: 1px solid #dee2e6;
        padding: 15px 10px;
        font-weight: 600;
        vertical-align: middle;
        white-space: nowrap;
    }

    .header-content strong {
        display: block;
        font-size: 0.95rem;
        margin-bottom: 2px;
        color: #2c3e50;
    }

    .header-content small {
        font-size: 0.75rem;
        color: #6c757d;
        opacity: 0.9;
    }

    /* Employee Name Column - Sticky with Solid Background and High Z-Index */
    .employee-name-col {
        position: sticky !important;
        left: 0 !important;
        z-index: 100 !important;
        background: #f8f9fa !important;
        min-width: 200px;
        max-width: 200px;
        border-right: 2px solid #495057 !important;
        box-shadow: 2px 0 8px rgba(0,0,0,0.15) !important;
    }

    tbody .employee-name-col {
        position: sticky !important;
        left: 0 !important;
        background: #ffffff !important;
        border-right: 2px solid #495057 !important;
        padding: 15px 12px !important;
        box-shadow: 2px 0 8px rgba(0,0,0,0.15) !important;
        z-index: 99 !important;
    }

    tfoot .employee-name-col {
        position: sticky !important;
        left: 0 !important;
        z-index: 98 !important;
        box-shadow: 2px 0 8px rgba(0,0,0,0.15) !important;
    }

    /* Data Columns */
    .data-col {
        min-width: 140px;
        max-width: 140px;
    }

    .total-col {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
        color: #155724 !important;
        min-width: 140px;
        max-width: 140px;
    }

    /* Employee Info */
    .employee-info {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .employee-name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 6px;
        font-size: 0.95rem;
    }

    .employee-details {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 500;
        display: inline-block;
    }

    .status-badge.active {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-badge.inactive {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .hourly-rate {
        font-size: 0.8rem;
        color: #6c757d;
        font-weight: 500;
    }

    /* Employee Row Styling */
    .employee-row {
        border-left: 3px solid transparent;
        transition: all 0.2s ease;
    }

    .active-employee {
        background-color: rgba(40, 167, 69, 0.03) !important;
        border-left-color: #28a745;
    }

    .inactive-employee {
        background-color: rgba(220, 53, 69, 0.03) !important;
        border-left-color: #dc3545;
        opacity: 0.85;
    }

    .active-employee .employee-name-col {
        background: rgba(40, 167, 69, 0.08) !important;
        box-shadow: 2px 0 8px rgba(40, 167, 69, 0.25) !important;
        z-index: 99 !important;
        position: sticky !important;
        left: 0 !important;
    }

    .inactive-employee .employee-name-col {
        background: rgba(220, 53, 69, 0.08) !important;
        box-shadow: 2px 0 8px rgba(220, 53, 69, 0.25) !important;
        z-index: 99 !important;
        position: sticky !important;
        left: 0 !important;
    }

    /* Time and Cost Display */
    .time-cost-cell {
        padding: 12px 8px !important;
        text-align: center;
        vertical-align: middle;
    }

    .time-display {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 4px;
        font-size: 0.9rem;
        display: block;
    }

    .cost-display {
        font-size: 0.8rem;
        color: #6c757d;
        font-weight: 500;
        line-height: 1.3;
        word-break: break-word;
    }

    .total-worked-cell .time-display {
        color: #28a745;
        font-weight: bold;
    }

    .total-worked-cell .cost-display {
        color: #28a745;
        font-weight: bold;
    }

    /* Footer Totals */
    .table tfoot {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        font-weight: 600;
    }

    .table tfoot th {
        padding: 15px 8px;
        border-top: 2px solid #dee2e6;
    }

    .table tfoot .employee-name-col {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        z-index: 98 !important;
        position: sticky !important;
        left: 0 !important;
        box-shadow: 2px 0 8px rgba(0,0,0,0.15) !important;
    }

    .table tfoot .total-col {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
        color: #155724 !important;
    }

    .total-label strong {
        color: #2c3e50;
        font-size: 1rem;
    }

    .total-label small {
        color: #6c757d;
        font-size: 0.8rem;
    }

    .total-time {
        font-weight: bold;
        margin-bottom: 4px;
        font-size: 0.9rem;
        color: #2c3e50;
    }

    .total-cost {
        font-size: 0.8rem;
        font-weight: 600;
        color: #495057;
        line-height: 1.2;
    }

    /* Hover Effects */
    .employee-row:hover {
        background-color: rgba(0, 123, 255, 0.05) !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .active-employee:hover {
        background-color: rgba(40, 167, 69, 0.08) !important;
    }

    .inactive-employee:hover {
        background-color: rgba(220, 53, 69, 0.08) !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .report-header .row {
            text-align: center;
        }

        .report-summary {
            text-align: center;
            margin-top: 15px;
        }

        .data-col {
            min-width: 120px;
        }

        .employee-name-col {
            min-width: 180px;
        }
    }

    /* Shadow for cards */
    .shadow-pro {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
    }
</style>

<script>
$(document).ready(function() {
    $('.select2').select2({
        placeholder: "Choose Project",
        allowClear: true
    });
});
</script>
