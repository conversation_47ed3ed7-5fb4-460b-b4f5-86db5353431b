<div class="simple-tasks-wrapper">
    <!-- Simple Header -->
    <div class="tasks-header-simple">
        <div class="header-content">
            <h2 class="page-title">
                <i class="bi bi-list-task"></i>
                Tasks
            </h2>
            <div class="header-actions">
                <?php if (has_permission('tasks/add')): ?>
                <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add', 'Add New Task')" 
                        class="btn btn-primary btn-sm">
                    <i class="bi bi-plus"></i>
                    Add Task
                </button>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Simple Status Filter -->
    <div class="status-filter-simple">
        <div class="filter-buttons">
            <a href="?task_status=all" class="filter-btn <?=$_GET['task_status'] == 'all' || $_GET['task_status'] == '' ? 'active' : ''?>">
                All (<?=$status_count['all'] ?? 0?>)
            </a>
            <a href="?task_status=pending" class="filter-btn <?=$_GET['task_status'] == 'pending' ? 'active' : ''?>">
                Pending (<?=$status_count['pending'] ?? 0?>)
            </a>
            <a href="?task_status=assigned" class="filter-btn <?=$_GET['task_status'] == 'assigned' ? 'active' : ''?>">
                In Progress (<?=$status_count['assigned'] ?? 0?>)
            </a>
            <a href="?task_status=testing" class="filter-btn <?=$_GET['task_status'] == 'testing' ? 'active' : ''?>">
                Testing (<?=$status_count['testing'] ?? 0?>)
            </a>
            <a href="?task_status=on_hold" class="filter-btn <?=$_GET['task_status'] == 'on_hold' ? 'active' : ''?>">
                On Hold (<?=$status_count['on_hold'] ?? 0?>)
            </a>
            <a href="?task_status=completed" class="filter-btn <?=$_GET['task_status'] == 'completed' ? 'active' : ''?>">
                Completed (<?=$status_count['completed'] ?? 0?>)
            </a>
        </div>
    </div>

    <!-- Simple Task List -->
    <div class="task-list-simple">
        <?php
        if (isset($list_items) && isset($clients) && isset($projects) && isset($users)) {
            foreach ($list_items as $key => $item) {
                $project_title = isset($projects[$item['project_id']]) ? $projects[$item['project_id']] : 'N/A';
                $assigned_user = isset($users[$item['user_id']]) ? $users[$item['user_id']] : null;
                
                // Due date styling
                $due_date = $item['due_date'];
                $current_date = date('Y-m-d');
                $due_class = '';
                
                if ($due_date < $current_date) {
                    $due_class = 'overdue';
                } elseif ($due_date == $current_date) {
                    $due_class = 'due-today';
                }
                ?>
                <div class="task-item" data-task-id="<?= $item['id'] ?>">
                    <div class="task-main">
                        <div class="task-id">TRG-<?= str_pad($item['id'], 4, '0', STR_PAD_LEFT) ?></div>
                        <div class="task-title"><?= htmlspecialchars($item['title']) ?></div>
                        <div class="task-details">
                            <?php if ($project_title !== 'N/A'): ?>
                                <span class="project">📁 <?= htmlspecialchars($project_title) ?></span>
                            <?php endif; ?>
                            <span class="type"><?= get_task_type($item['task_type']) ?></span>
                            <span class="priority"><?= get_task_priority($item['task_priority']) ?></span>
                            <span class="status"><?= get_task_status($item['task_status']) ?></span>
                            <?php if (!empty($assigned_user)): ?>
                                <span class="assigned">👤 <?= htmlspecialchars($assigned_user) ?></span>
                            <?php else: ?>
                                <span class="unassigned">❌ Unassigned</span>
                            <?php endif; ?>
                            <span class="due-date <?= $due_class ?>">📅 <?= date('M j, Y', strtotime($due_date)) ?></span>
                        </div>
                    </div>
                    <div class="task-actions">
                        <a href="<?= site_url('app/tasks/view/' . $item['id']) ?>" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye"></i>
                        </a>
                        <?php if (has_permission('tasks/edit')): ?>
                        <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/edit&task_id=<?= $item['id'] ?>', 'Edit Task')" 
                                class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <?php endif; ?>
                        <?php if (has_permission('tasks/delete')): ?>
                        <button onclick="confirm_delete('<?= site_url('app/tasks/delete/' . $item['id']) ?>')" 
                                class="btn btn-sm btn-outline-danger">
                            <i class="bi bi-trash"></i>
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
                <?php
            }
        } else {
            ?>
            <div class="no-tasks">
                <i class="bi bi-inbox"></i>
                <p>No tasks found</p>
            </div>
            <?php
        }
        ?>
    </div>
</div>

<style>
.simple-tasks-wrapper {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.tasks-header-simple {
    margin-bottom: 20px;
}

.tasks-header-simple .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tasks-header-simple .page-title {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
}

.status-filter-simple {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.filter-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    text-decoration: none;
    color: #666;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.2s;
}

.filter-btn:hover {
    background: #e9ecef;
    text-decoration: none;
}

.filter-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.task-list-simple {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    transition: background 0.2s;
}

.task-item:hover {
    background: #f8f9fa;
}

.task-item:last-child {
    border-bottom: none;
}

.task-main {
    flex: 1;
}

.task-id {
    font-weight: bold;
    color: #007bff;
    font-size: 14px;
    margin-bottom: 5px;
}

.task-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.task-details {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    font-size: 13px;
    color: #666;
}

.task-details span {
    display: inline-flex;
    align-items: center;
    gap: 3px;
}

.task-actions {
    display: flex;
    gap: 5px;
}

.due-date.overdue {
    color: #dc3545;
    font-weight: bold;
}

.due-date.due-today {
    color: #fd7e14;
    font-weight: bold;
}

.no-tasks {
    text-align: center;
    padding: 40px;
    color: #666;
}

.no-tasks i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

@media (max-width: 768px) {
    .task-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .task-details {
        flex-direction: column;
        gap: 8px;
    }
    
    .task-actions {
        align-self: flex-end;
    }
    
    .filter-buttons {
        flex-direction: column;
    }
    
    .filter-btn {
        text-align: center;
    }
}
</style>