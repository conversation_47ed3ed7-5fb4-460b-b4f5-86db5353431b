<?php 
    $item_id = $param1;
    $task_details = $this->tasks_m->get(['id' => $item_id])->row_array();
    $task_history = $this->work_history_m->get_task_history($item_id);
    $project_title = $this->projects_m->get(['id' => $task_details['project_id']], ['id', 'title'])->row()->title;
    $users = array_column($this->users_m->get(null, ['id', 'name'])->result_array(), 'name', 'id');
?>

<style>
    .task_description p, .task_description li{
        font-size: 16px!important;
        line-height: 1.6em!important;
    }
</style>

<div class="task_description">
    <div class="p-1 pb-0">
        <span style="background-color:#efefef;font-size: 13px;border-left: 2px solid #999;color: blueviolet;padding-left: 5px">
            <?=strtoupper($project_title)?>
        </span>
        <h5 class="text-primary pt-2">
            <span style="color: #05639e;padding:3px">
                    [TRG-<b><?=$task_details['id']?></b>]
                </span>
                <b><?=$task_details['title']?></b>
        </h5>
        <hr>
    </div>
    <div class="shadow-pro1 p-1">
        <div style="background-color:#efefef5c;font-size:16px;color:#393939!important; line-height:1.6em;padding:10px;">
            <?=$task_details['description']?>
        </div>
        <div class="p-2">
            <div class="p-2 text-muted mb-2" style="background-color: #f3f2f2; font-size: 15px;font-weight: bold">
                TASK TIMELINE
            </div>
            <div class="timeline p-1">
                <?php
                if (count($task_history)){
                    foreach($task_history as $history){
                        if (!empty($history['remarks'])){
                            if($history['updated_by']!=get_user_id()){
                                $border = 'border:1px solid #ed6686';
                            }else{
                                $border = '';
                            }
                            ?>
                            <?php
                            $job_date = DateTime::createFromFormat('Y-m-d H:i:s', $history['start_time'])->format('d-m-Y');
                            if (!empty($history['start_time']) && !empty($history['end_time'])){
                                $start_time = DateTime::createFromFormat('H:i:s', $history['start_time'])->format('g:i A');
                                $end_time = DateTime::createFromFormat('H:i:s', $history['end_time'])->format('g:i A');
                            }
    
                            ?>
                            <div class="time-label">
                                <span class="" style="font-size: 13px; font-weight: normal; background-color: #e1f6eb;padding: 2px 3px">
                                    <span style="color: #062810">
                                        <b><?=$job_date?></b>: <?=$start_time.' to '.$end_time?>
                                    </span>
                                </span>
                            </div>
    
                            <div>
                                <i class="fas fa-comments bg-primary"></i>
                                <div class="timeline-item shadow-pro">
                                    <span class="time" style="font-size: 14px">
                                        <i class="fas fa-clock"></i> <?=$history['time_taken']?>
                                    </span>
                                    <h3 class="timeline-header text-muted" style="font-size: 14px">
                                        <a href="#"><?=strtoupper($users[$history['updated_by']]);?></a>
                                        updated on <small style="color: #0f91af"><?= DateTime::createFromFormat('Y-m-d H:i:s', $history['updated_on'])->format('d-m-Y g:i A')?></small>
                                    </h3>
                                    <div class="timeline-body p-2" style="font-size: 15px">
                                        <?=$history['remarks']?>
                                    </div>
                                    <div class="timeline-footer d-none">
                                        <button class="btn btn-outline-primary btn-sm" style="padding: 3px 10px;font-size: 12px; width: 90px">Edit</button>
                                        <button class="btn btn-outline-danger btn-sm" style="padding: 3px 10px;font-size: 12px; width: 90px">Delete</button>
                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                    }
                }
                ?>
                <div>
                    <i class="fas fa-clock bg-primary"></i>
                </div>
            </div>
        </div>
    </div>
</div>