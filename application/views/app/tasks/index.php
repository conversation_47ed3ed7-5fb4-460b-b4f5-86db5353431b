<div class="modern-tasks-wrapper">
    <!-- Modern Header -->
    <div class="tasks-header">
        <div class="header-content">
            <div class="header-left">
                <div class="breadcrumb-nav">
                    <a href="<?= base_url("app/dashboard/index"); ?>" class="breadcrumb-link">
                        <i class="bi bi-house"></i> Dashboard
                    </a>
                    <i class="bi bi-chevron-right"></i>
                    <span class="current-page">Tasks</span>
                </div>
                <h1 class="page-title">
                    <i class="bi bi-kanban"></i>
                    Task Management
                </h1>
                <p class="page-subtitle">Manage and track all your tasks efficiently</p>
            </div>
            <div class="header-actions">
                <?php if (has_permission('tasks/add')): ?>
                <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add', 'Add New Task')" 
                        class="btn-primary-modern">
                    <i class="bi bi-plus-circle"></i>
                    <span>Add New Task</span>
                </button>
                <?php endif; ?>
                <button onclick="window.location.reload()" class="btn-secondary-modern">
                    <i class="bi bi-arrow-clockwise"></i>
                    <span>Refresh</span>
                </button>
            </div>
        </div>
    </div>
    
    <div class="mt-3" style="margin:14px;">
    <!-- Modern Statistics Cards -->
    <div class="stats-section">
        <div class="stats-grid">
            <a href="?task_status=all" class="stat-card-modern <?=$_GET['task_status'] == 'all' || $_GET['task_status'] == '' ? 'active' : ''?>">
                <div class="stat-icon all-tasks">
                    <i class="bi bi-collection"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?=$status_count['all'] ?? 0?></div>
                    <div class="stat-label">Total Tasks</div>
                </div>
            </a>
            
            <a href="?task_status=pending" class="stat-card-modern <?=$_GET['task_status'] == 'pending' ? 'active' : ''?>">
                <div class="stat-icon pending-tasks">
                    <i class="bi bi-clock-history"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?=$status_count['pending'] ?? 0?></div>
                    <div class="stat-label">Pending</div>
                </div>
            </a>
            
            <a href="?task_status=assigned" class="stat-card-modern <?=$_GET['task_status'] == 'assigned' ? 'active' : ''?>">
                <div class="stat-icon assigned-tasks">
                    <i class="bi bi-person-check"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?=$status_count['assigned'] ?? 0?></div>
                    <div class="stat-label">In Progress</div>
                </div>
            </a>
            
            <a href="?task_status=testing" class="stat-card-modern <?=$_GET['task_status'] == 'testing' ? 'active' : ''?>">
                <div class="stat-icon testing-tasks">
                    <i class="bi bi-bug"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?=$status_count['testing'] ?? 0?></div>
                    <div class="stat-label">Testing</div>
                </div>
            </a>
            
            <a href="?task_status=on_hold" class="stat-card-modern <?=$_GET['task_status'] == 'on_hold' ? 'active' : ''?>">
                <div class="stat-icon hold-tasks">
                    <i class="bi bi-pause-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?=$status_count['on_hold'] ?? 0?></div>
                    <div class="stat-label">On Hold</div>
                </div>
            </a>
            
            <a href="?task_status=completed" class="stat-card-modern <?=$_GET['task_status'] == 'completed' ? 'active' : ''?>">
                <div class="stat-icon completed-tasks">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?=$status_count['completed'] ?? 0?></div>
                    <div class="stat-label">Completed</div>
                </div>
            </a>
        </div>
    </div>

    <!-- Modern Filters -->
    <?php if (isset($start_date) && isset($end_date) && isset($status_count) && isset($project_id)): ?>
    <div class="filters-section">
        <div class="filters-card">
            <div class="filters-header">
                <h5><i class="bi bi-funnel"></i> Filters & Search</h5>
                <button type="button" class="btn-filter-toggle" onclick="toggleFilters()">
                    <i class="bi bi-chevron-down"></i>
                </button>
            </div>
            
            <div class="filters-content" id="filtersContent">
                <form action="" method="get" class="modern-form">
                    <div class="form-grid">
                        <div class="form-group-modern">
                            <label><i class="bi bi-calendar-range"></i> Start Date</label>
                            <input type="date" value="<?=$start_date?>" class="form-control-modern" name="start_date" required>
                        </div>
                        
                        <div class="form-group-modern">
                            <label><i class="bi bi-calendar-check"></i> End Date</label>
                            <input type="date" value="<?=$end_date?>" class="form-control-modern" name="end_date" required>
                        </div>
                        
                        <div class="form-group-modern">
                            <label><i class="bi bi-person"></i> Assigned User</label>
                            <select class="form-control-modern select2" name="user_id">
                                <option value="" <?=$_GET['user_id'] == '' ? 'selected' : ''?>>All Users</option>
                                <?php
                                if (isset($users)){
                                    foreach ($users as $user_id => $user_name){
                                        $selected = $_GET['user_id'] == $user_id ? 'selected' : '';
                                        echo "<option value='{$user_id}' {$selected}>{$user_name}</option>";
                                    }
                                }
                                ?>
                            </select>
                        </div>
                        
                        <div class="form-group-modern">
                            <label><i class="bi bi-flag"></i> Status</label>
                            <select class="form-control-modern select2" name="task_status" required>
                                <option value="all" <?=$_GET['task_status'] == 'all' ? 'selected' : ''?>>All Statuses</option>
                                <option value="pending" <?=$_GET['task_status'] == 'pending' ? 'selected' : ''?>>Pending</option>
                                <option value="assigned" <?=$_GET['task_status'] == 'assigned' ? 'selected' : ''?>>Assigned</option>
                                <option value="testing" <?=$_GET['task_status'] == 'testing' ? 'selected' : ''?>>Testing</option>
                                <option value="on_hold" <?=$_GET['task_status'] == 'on_hold' ? 'selected' : ''?>>On Hold</option>
                                <option value="completed" <?=$_GET['task_status'] == 'completed' ? 'selected' : ''?>>Completed</option>
                            </select>
                        </div>
                        
                        <div class="form-group-modern form-group-wide">
                            <label><i class="bi bi-folder"></i> Project</label>
                            <select class="form-control-modern select2" name="project_id" required>
                                <option value="all" <?=$_GET['project_id'] == '0' ? 'selected' : ''?>>All Projects</option>
                                <?php
                                if (isset($projects_list) && isset($project_type)){
                                    foreach ($projects_list as $project){
                                        $selected = $project['id'] == $_GET['project_id'] ? 'selected' : '';
                                        echo "<option value='{$project['id']}' {$selected}>{$project['title']} - [{$project_type[$project['project_type']]}]</option>";
                                    }
                                }
                                ?>
                            </select>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn-filter-apply">
                                <i class="bi bi-search"></i>
                                Apply Filters
                            </button>
                            <button type="button" onclick="window.location.href='<?= current_url() ?>'" class="btn-filter-reset">
                                <i class="bi bi-x-circle"></i>
                                Clear
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Modern Tasks Table -->
    <div class="tasks-table-section">
        <div class="table-header">
            <div class="table-title">
                <h5><i class="bi bi-list-task"></i> Tasks List</h5>
                <span class="table-count"><?= isset($list_items) ? count($list_items) : 0 ?> tasks found</span>
            </div>
            <div class="table-actions">
                <div class="view-toggle">
                    <button class="view-btn active" data-view="table">
                        <i class="bi bi-table"></i>
                    </button>
                    <button class="view-btn" data-view="cards">
                        <i class="bi bi-grid-3x3"></i>
                    </button>
                </div>
                <button class="btn-export" onclick="exportTasks()">
                    <i class="bi bi-download"></i>
                    Export
                </button>
            </div>
        </div>
        
        <div class="modern-table-wrapper">
            <table class="modern-table" id="tasks_table">
                <thead class="table-header-modern">
                    <tr>
                        <th class="sortable" data-sort="id">
                            <div class="th-content">
                                <span>Task ID</span>
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </div>
                        </th>
                        <th class="sortable" data-sort="title">
                            <div class="th-content">
                                <span>Task Details</span>
                                <i class="bi bi-arrow-down-up sort-icon"></i>
                            </div>
                        </th>
                        <th class="actions-column">Actions</th>
                    </tr>
                </thead>
                <tbody class="table-body-modern">
                    <?php
                    if (isset($list_items) && isset($clients) && isset($projects) && isset($users)) {
                        foreach ($list_items as $key => $item) {
                            ?>
                            <tr class="table-row-modern ta_search_item" data-task-id="<?= $item['id'] ?>">
                                <td class="task-id-cell">
                                    <div class="task-id-badge">
                                        TRG-<?= str_pad($item['id'], 4, '0', STR_PAD_LEFT) ?>
                                    </div>
                                </td>
                                
                                <td class="task-main-cell">
                                    <div class="task-info">
                                        <?php $project_title = isset($projects[$item['project_id']]) ? $projects[$item['project_id']] : 'N/A'; ?>
                                        <div class="task-header">
                                            <?php if ($project_title !== 'N/A'): ?>
                                                <div class="project-tag">
                                                    <i class="bi bi-folder"></i>
                                                    <?= htmlspecialchars($project_title) ?>
                                                </div>
                                            <?php endif; ?>
                                            <div class="task-title">
                                                <?= htmlspecialchars($item['title']) ?>
                                            </div>
                                        </div>
                                        
                                        <div class="task-meta-compact">
                                            <div class="meta-item">
                                                <span class="meta-label">Type:</span>
                                                <?= get_task_type($item['task_type']) ?>
                                            </div>
                                            <div class="meta-item">
                                                <span class="meta-label">Priority:</span>
                                                <?= get_task_priority($item['task_priority']) ?>
                                            </div>
                                            <div class="meta-item">
                                                <span class="meta-label">Status:</span>
                                                <div class="status-badge-modern status-<?= $item['task_status'] ?>">
                                                    <i class="<?= get_task_status_icon($item['task_status']) ?>"></i>
                                                    <span><?= ucfirst($item['task_status']) ?></span>
                                                </div>
                                            </div>
                                            <div class="meta-item">
                                                <span class="meta-label">Assigned:</span>
                                                <?php $assigned_user = isset($users[$item['user_id']]) ? $users[$item['user_id']] : null; ?>
                                                <?php if (!empty($assigned_user)): ?>
                                                    <div class="user-badge">
                                                        <div class="user-avatar">
                                                            <?= strtoupper(substr($assigned_user, 0, 2)) ?>
                                                        </div>
                                                        <span class="user-name"><?= htmlspecialchars($assigned_user) ?></span>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="unassigned-badge">
                                                        <i class="bi bi-person-dash"></i>
                                                        <span>Unassigned</span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="meta-item">
                                                <span class="meta-label">Due:</span>
                                                <?php
                                                $due_date = $item['due_date'];
                                                $current_date = date('Y-m-d');
                                                $due_class = '';
                                                
                                                if ($due_date < $current_date) {
                                                    $due_class = 'overdue';
                                                } elseif ($due_date == $current_date) {
                                                    $due_class = 'due-today';
                                                }
                                                ?>
                                                <div class="due-date-badge <?= $due_class ?>">
                                                    <i class="bi bi-calendar-event"></i>
                                                    <span><?= DateTime::createFromFormat('Y-m-d', $due_date)->format('M d, Y') ?></span>
                                                </div>
                                            </div>
                                            <div class="meta-item">
                                                <span class="meta-label">Created by:</span>
                                                <div class="creator-info">
                                                    <?= isset($users[$item['created_by']]) ? htmlspecialchars($users[$item['created_by']]) : 'N/A' ?>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <?php if (!empty($item['description'])): ?>
                                            <div class="task-description">
                                                <?= htmlspecialchars(substr($item['description'], 0, 100)) ?><?= strlen($item['description']) > 100 ? '...' : '' ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                
                                <td class="task-actions-cell">
                                    <div class="action-buttons">
                                        <div class="dropdown">
                                            <button class="action-btn-modern dropdown-toggle" type="button" data-toggle="dropdown">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <div class="dropdown-menu dropdown-menu-right">
                                                <a class="dropdown-item" href="<?= base_url('app/tasks/view/'.$item['id']); ?>">
                                                    <i class="bi bi-eye"></i> View Details
                                                </a>
                                                
                                                <?php if (empty($item['user_id']) || $item['user_id'] == 0): ?>
                                                    <a class="dropdown-item" href="#" onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/assign'); ?>', 'Assign Task')">
                                                        <i class="bi bi-person-plus"></i> Assign Task
                                                    </a>
                                                <?php endif; ?>
                                                
                                                <?php if (has_permission('tasks/edit')): ?>
                                                    <a class="dropdown-item" href="#" onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=tasks/edit'); ?>', 'Edit Task')">
                                                        <i class="bi bi-pencil"></i> Edit Task
                                                    </a>
                                                <?php endif; ?>
                                                
                                                <div class="dropdown-divider"></div>
                                                
                                                <?php if (has_permission('tasks/delete')): ?>
                                                    <a class="dropdown-item text-danger" href="#" onclick="confirm_modal('<?=base_url("app/tasks/delete/{$item['id']}/");?>')">
                                                        <i class="bi bi-trash"></i> Delete Task
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php
                        }
                    }
                    ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Modern No Results -->
        <div id="ta_no_result" class="no-results-modern" style="display: none">
            <div class="no-results-content">
                <div class="no-results-icon">
                    <i class="bi bi-inbox"></i>
                </div>
                <h4>No Tasks Found</h4>
                <p>Try adjusting your filters or create a new task</p>
                <?php if (has_permission('tasks/add')): ?>
                <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add', 'Add New Task')" 
                        class="btn-primary-modern">
                    <i class="bi bi-plus-circle"></i>
                    Create New Task
                </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Floating Quick Add Button -->
<?php if (has_permission('tasks/add')): ?>
<div class="floating-add-btn">
    <button id="floating_quick_add" class="btn btn-primary btn-lg rounded-circle shadow-lg" 
            title="Add New Task"
            onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add', 'Add New Task')">
        <i class="fas fa-plus fa-lg"></i>
    </button>
</div>
<?php endif; ?>

<style>
/* Modern Tasks Design System */
.modern-tasks-wrapper {
    background: #f8fafc;
    min-height: 100vh;
    padding: 0;
}

/* Header Styles */
.tasks-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 20px;
    margin-bottom: 30px;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.breadcrumb-nav {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 14px;
    opacity: 0.9;
}

.breadcrumb-link {
    color: white;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.breadcrumb-link:hover {
    opacity: 0.8;
    color: white;
}

.current-page {
    font-weight: 500;
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
}

.header-actions {
    display: flex;
    gap: 15px;
}

.btn-primary-modern, .btn-secondary-modern {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 14px;
}

.btn-primary-modern {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.btn-primary-modern:hover {
    background: white;
    color: #667eea;
    transform: translateY(-2px);
}

.btn-secondary-modern {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.btn-secondary-modern:hover {
    background: rgba(255,255,255,0.1);
    transform: translateY(-2px);
}

/* Statistics Cards */
.stats-section {
    max-width: 1400px;
    margin: 0 auto 30px auto;
    padding: 0 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card-modern {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 20px;
    text-decoration: none;
    color: inherit;
    border: 2px solid transparent;
}

.stat-card-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    text-decoration: none;
    color: inherit;
}

.stat-card-modern.active {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea10 0%, #764ba210 100%);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stat-icon.all-tasks { background: linear-gradient(135deg, #667eea, #764ba2); }
.stat-icon.pending-tasks { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
.stat-icon.assigned-tasks { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
.stat-icon.testing-tasks { background: linear-gradient(135deg, #a8a8a8, #8e8e8e); }
.stat-icon.hold-tasks { background: linear-gradient(135deg, #ffd93d, #ffb74d); }
.stat-icon.completed-tasks { background: linear-gradient(135deg, #45b7d1, #2196f3); }

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
    color: #2c3e50;
}

.stat-label {
    font-size: 14px;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Filters Section */
.filters-section {
    max-width: 1400px;
    margin: 0 auto 30px auto;
    padding: 0 20px;
}

.filters-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    overflow: hidden;
}

.filters-header {
    background: #f8f9fa;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.filters-header h5 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn-filter-toggle {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 16px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.btn-filter-toggle:hover {
    color: #495057;
}

.filters-content {
    padding: 25px;
    display: block;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: end;
}

.form-group-modern {
    display: flex;
    flex-direction: column;
}

.form-group-wide {
    grid-column: span 2;
}

.form-group-modern label {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-control-modern {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-control-modern:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
    display: flex;
    gap: 15px;
}

.btn-filter-apply, .btn-filter-reset {
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-filter-apply {
    background: #667eea;
    color: white;
}

.btn-filter-apply:hover {
    background: #5a67d8;
    transform: translateY(-2px);
}

.btn-filter-reset {
    background: #e9ecef;
    color: #495057;
}

.btn-filter-reset:hover {
    background: #dee2e6;
    transform: translateY(-2px);
}

/* Table Section */
.tasks-table-section {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.table-header {
    background: white;
    padding: 20px 25px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.table-title h5 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.table-count {
    font-size: 13px;
    color: #6c757d;
    background: #f8f9fa;
    padding: 4px 10px;
    border-radius: 20px;
    margin-left: 15px;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
}

.view-btn {
    padding: 8px 12px;
    background: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6c757d;
}

.view-btn.active, .view-btn:hover {
    background: white;
    color: #667eea;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-export {
    padding: 8px 16px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-export:hover {
    background: #218838;
    transform: translateY(-2px);
}

.modern-table-wrapper {
    background: white;
    border-radius: 0 0 15px 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.table-header-modern th {
    background: #f8f9fa;
    padding: 15px 20px;
    text-align: left;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
}

.th-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-icon {
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.sortable:hover .sort-icon {
    opacity: 1;
}

.table-body-modern .table-row-modern {
    transition: all 0.3s ease;
    cursor: pointer;
}

.table-body-modern .table-row-modern:hover {
    background: #f8f9fa;
}

.table-body-modern .table-row-modern:nth-child(even) {
    background: #fdfdfd;
}

.table-body-modern .table-row-modern:nth-child(even):hover {
    background: #f8f9fa;
}

.table-body-modern td {
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

/* Table Cell Specific Styles */
.task-id-badge {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    letter-spacing: 0.5px;
    min-width: 60px;
    text-align: center;
}

.task-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.project-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: #e3f2fd;
    color: #1565c0;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    width: fit-content;
}

.task-title {
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.4;
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    margin: 4px 0;
    display: inline-block;
}

.task-description {
    font-size: 11px;
    color: #6c757d;
    line-height: 1.3;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    border-left: 3px solid #dee2e6;
    margin-top: 4px;
}

.status-badge-modern {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
    min-width: 60px;
    justify-content: center;
}

.status-badge-modern.status-pending {
    background: #fff5f5;
    color: #c53030;
}

.status-badge-modern.status-assigned {
    background: #ebf8ff;
    color: #2b6cb0;
}

.status-badge-modern.status-testing {
    background: #f7fafc;
    color: #4a5568;
}

.status-badge-modern.status-completed {
    background: #f0fff4;
    color: #22543d;
}

.status-badge-modern.status-on_hold {
    background: #fffbeb;
    color: #d69e2e;
}

.user-badge {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 9px;
    font-weight: 600;
}

.user-name {
    font-size: 11px;
    font-weight: 500;
    color: #2c3e50;
}

.unassigned-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #6c757d;
    font-size: 13px;
    font-style: italic;
}

.due-date-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    background: #f8f9fa;
    color: #495057;
    min-width: 80px;
    justify-content: center;
}

.due-date-badge.overdue {
    background: #fed7d7;
    color: #c53030;
}

.due-date-badge.due-today {
    background: #feebc8;
    color: #dd6b20;
}

.creator-info {
    font-size: 13px;
    color: #6c757d;
}

.action-buttons {
    position: relative;
}

.action-btn-modern {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 6px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #495057;
}

.action-btn-modern:hover {
    background: #e9ecef;
    color: #212529;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    border-radius: 10px;
    padding: 8px 0;
    margin-top: 5px;
}

.dropdown-item {
    padding: 8px 20px;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: background-color 0.3s ease;
}

.dropdown-item:hover {
    background: #f8f9fa;
}

.dropdown-divider {
    margin: 5px 0;
}

/* No Results */
.no-results-modern {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    margin: 30px auto;
    max-width: 500px;
}

.no-results-icon {
    font-size: 64px;
    color: #e9ecef;
    margin-bottom: 20px;
}

.no-results-content h4 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.no-results-content p {
    color: #6c757d;
    margin-bottom: 20px;
}

/* Floating Button */
.floating-add-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.floating-add-btn .btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    border: none;
}

.floating-add-btn .btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

/* Compact Layout Improvements */
.task-main-cell {
    width: auto;
    min-width: 400px;
}

.task-actions-cell {
    width: 60px;
    text-align: center;
}

.task-id-cell {
    width: 80px;
    text-align: center;
}

.task-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.task-meta-compact {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 8px;
    margin-bottom: 8px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
}

.meta-label {
    font-weight: 600;
    color: #6c757d;
    min-width: 50px;
    font-size: 10px;
    text-transform: uppercase;
}

.meta-item .user-badge,
.meta-item .unassigned-badge,
.meta-item .due-date-badge,
.meta-item .status-badge-modern,
.meta-item .creator-info {
    margin: 0;
}

.compact-details {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;
    margin-top: 4px;
}

.compact-details > * {
    flex-shrink: 0;
}

.unassigned-badge {
    font-size: 10px;
    padding: 2px 6px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.creator-info {
    font-size: 10px;
    color: #6c757d;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 20px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-group-wide {
        grid-column: span 1;
    }
    
    .table-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .modern-table-wrapper {
        overflow-x: auto;
    }
    
    .modern-table {
        min-width: 800px;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .tasks-header {
        padding: 20px 15px;
    }
    
    .page-title {
        font-size: 24px;
    }
}
</style>

<script>
// Filter toggle functionality
function toggleFilters() {
    const content = document.getElementById('filtersContent');
    const toggle = document.querySelector('.btn-filter-toggle i');
    
    if (content.style.display === 'none') {
        content.style.display = 'block';
        toggle.style.transform = 'rotate(180deg)';
    } else {
        content.style.display = 'none';
        toggle.style.transform = 'rotate(0deg)';
    }
}

// Export functionality
function exportTasks() {
    // Add export functionality here
    alert('Export functionality to be implemented');
}

// View toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const viewButtons = document.querySelectorAll('.view-btn');
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            viewButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Add view switching logic here
            const view = this.dataset.view;
            if (view === 'cards') {
                // Switch to card view
                alert('Card view to be implemented');
            } else {
                // Switch to table view (default)
            }
        });
    });
    
    // Table sorting
    const sortableHeaders = document.querySelectorAll('.sortable');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const sortBy = this.dataset.sort;
            // Add sorting logic here
            console.log('Sort by:', sortBy);
        });
    });
});

/* Responsive adjustments */
@media (max-width: 768px) {
    .floating-add-btn {
        bottom: 20px;
        right: 20px;
    }
    
    .floating-add-btn .btn {
        width: 50px;
        height: 50px;
    }
    
    #tasks_table {
        font-size: 11px;
    }
    
    .btn_assigned_user {
        width: 100px !important;
        font-size: 10px;
    }
}

/* Animation for the floating button */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.floating-add-btn .btn {
    animation: float 3s ease-in-out infinite;
}

.floating-add-btn .btn:hover {
    animation: none;
}

/* Overview Cards Styling */
.overview-card {
    padding: 15px 10px;
    border-radius: 8px;
    background-color: #f8f9fa;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: default;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.overview-card.active {
    background-color: #ffffff;
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0,123,255,0.2);
}

.overview-card .text-muted {
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
</style>

<script>
$(document).ready(function() {
    // Initialize floating button tooltip
    $('#floating_quick_add').tooltip();
    
    // Initialize DataTable
    $('#tasks_table').DataTable({
        "responsive": true,
        "autoWidth": false,
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        "order": [[0, "desc"]], // Sort by Task ID descending
        "language": {
            "search": "Search tasks:",
            "lengthMenu": "Show _MENU_ tasks per page",
            "info": "Showing _START_ to _END_ of _TOTAL_ tasks",
            "infoEmpty": "Showing 0 to 0 of 0 tasks",
            "infoFiltered": "(filtered from _MAX_ total tasks)",
            "emptyTable": "No tasks found",
            "zeroRecords": "No matching tasks found"
        },
        "columnDefs": [
            {
                "targets": [8], // Actions column
                "orderable": false,
                "searchable": false
            }
        ],
        "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
               '<"row"<"col-sm-12"tr>>' +
               '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        "initComplete": function () {
            // Add custom search functionality
            this.api().columns().every(function () {
                var column = this;
                var title = column.header().textContent;
                
                // Skip actions column
                if (title === 'Actions') return;
                
                // Create search input for each column
                var input = $('<input class="form-control form-control-sm" type="text" placeholder="Search ' + title + '" />')
                    .appendTo($(column.header()))
                    .on('keyup change', function () {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });
    
    // Enhanced search functionality for original view
    $('#ta_search_list').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        var hasResults = false;

        $('.ta_search_item').each(function() {
            var text = $(this).text().toLowerCase();
            if (text.indexOf(value) > -1) {
                $(this).show();
                hasResults = true;
            } else {
                $(this).hide();
            }
        });

        if (hasResults) {
            $('#ta_no_result').hide();
        } else {
            $('#ta_no_result').show();
        }
    });

    // Clear search functionality
    $('#clear_search_original').on('click', function() {
        $('#ta_search_list').val('');
        $('.ta_search_item').show();
        $('#ta_no_result').hide();
    });
    
    // Show floating button always
    $('.floating-add-btn').show();
});

// AJAX modal function for quick actions
function show_ajax_modal(url, title) {
    $.ajax({
        url: url,
        type: 'GET',
        success: function(response) {
            $('#ajax_modal_content').html(response);
            $('#ajax_modal_title').text(title);
            $('#ajax_modal').modal('show');
        },
        error: function() {
            alert('Error loading content');
        }
    });
}
</script>

