<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Go Back
        </a>
        <?php
        if (has_permission('tasks/add')){
            ?>
            <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add', '<?= get_phrase('add_task'); ?>')"
                    class="btn btn-primary btn-mini float-right">
                <small><i class="fas fa-plus"></i></small> Add <?= $page_title ?? '' ?>
            </button>
            <?php
        }
        ?>
    </div>
    
    <div class="mt-2" style="margin:14px;margin-top:5px!important;">
        <!-- Task Overview Cards -->
        <div class="p-2">
            <!-- Task Overview Summary -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="bg-white rounded shadow-sm py-3 px-4 border-left-primary">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <h6 class="text-primary mb-0 font-weight-bold">Task Overview</h6>
                                <small class="text-muted">Current Period</small>
                            </div>
                            <div class="col-md-10">
                                <div class="row text-center">
                                    <div class="col-2">
                                        <div class="overview-card <?=$_GET['task_status'] == 'all' || $_GET['task_status'] == '' ? 'active' : ''?>">
                                            <div style="font-size: 24px;font-weight: bold; color: #007bff;">
                                                <?=$status_count['all']?>
                                            </div>
                                            <small class="text-muted">ALL</small>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="overview-card <?=$_GET['task_status'] == 'pending' ? 'active' : ''?>">
                                            <div style="font-size: 24px;font-weight: bold; color: #dc3545;">
                                                <?=$status_count['pending']?>
                                            </div>
                                            <small class="text-muted">PENDING</small>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="overview-card <?=$_GET['task_status'] == 'assigned' ? 'active' : ''?>">
                                            <div style="font-size: 24px;font-weight: bold; color: #17a2b8;">
                                                <?=$status_count['assigned']?>
                                            </div>
                                            <small class="text-muted">ASSIGNED</small>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="overview-card <?=$_GET['task_status'] == 'testing' ? 'active' : ''?>">
                                            <div style="font-size: 24px;font-weight: bold; color: #343a40;">
                                                <?=$status_count['testing']?>
                                            </div>
                                            <small class="text-muted">TESTING</small>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="overview-card <?=$_GET['task_status'] == 'on_hold' ? 'active' : ''?>">
                                            <div style="font-size: 24px;font-weight: bold; color: #ffc107;">
                                                <?=$status_count['on_hold']?>
                                            </div>
                                            <small class="text-muted">ON HOLD</small>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="overview-card <?=$_GET['task_status'] == 'completed' ? 'active' : ''?>">
                                            <div style="font-size: 24px;font-weight: bold; color: #28a745;">
                                                <?=$status_count['completed']?>
                                            </div>
                                            <small class="text-muted">COMPLETED</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Search and Filters -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="bg-white rounded shadow-sm p-3">
                        <div class="row">
                            <div class="col-md-9">
                                <div class="input-group input-group-lg">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text bg-primary text-white">
                                            <i class="fas fa-search"></i>
                                        </span>
                                    </div>
                                    <input type="text" class="form-control" id="enhanced_search" 
                                           placeholder="Search tasks by title, ID, project, or assigned user...">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="button" id="clear_search">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex">
                                    <button class="btn btn-success btn-lg btn-add-actions mr-2" id="refresh_table" title="Refresh">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                    <button class="btn btn-info btn-lg btn-add-actions" id="apply_filters" title="Apply Filters">
                                        <i class="fas fa-filter"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Advanced Filters -->
                        <div class="row mt-3" id="advanced_filters">
                            <div class="col-md-2">
                                <label class="small text-muted">Status</label>
                                <select class="form-control form-control-sm" id="filter_status">
                                    <option value="">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="assigned">Assigned</option>
                                    <option value="testing">Testing</option>
                                    <option value="completed">Completed</option>
                                    <option value="on_hold">On Hold</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="small text-muted">Priority</label>
                                <select class="form-control form-control-sm" id="filter_priority">
                                    <option value="">All Priority</option>
                                    <option value="critical">Critical</option>
                                    <option value="high">High</option>
                                    <option value="medium">Medium</option>
                                    <option value="low">Low</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="small text-muted">Project</label>
                                <select class="form-control form-control-sm" id="filter_project">
                                    <option value="">All Projects</option>
                                    <?php
                                    if (isset($projects_list) && isset($project_type)){
                                        foreach ($projects_list as $project){
                                            echo "<option value='{$project['id']}'>{$project['title']} - [{$project_type[$project['project_type']]}]</option>";
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="small text-muted">Assigned To</label>
                                <select class="form-control form-control-sm" id="filter_assigned">
                                    <option value="">All Users</option>
                                    <?php
                                    if (isset($users)){
                                        foreach ($users as $user_id => $user_name){
                                            echo "<option value='{$user_id}'>{$user_name}</option>";
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="small text-muted">Due Date From</label>
                                <input type="date" class="form-control form-control-sm" id="filter_date_from">
                            </div>
                            <div class="col-md-2">
                                <label class="small text-muted">Due Date To</label>
                                <input type="date" class="form-control form-control-sm" id="filter_date_to">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Optimized Tasks Table with DataTables -->
            <div class="card card-primary shadow-pro">
                <div class="card-header">
                    <h3 class="card-title"><?= strtoupper($page_title ?? 'Tasks')?> (Optimized)</h3>
                </div>
                <div class="card-body">
                    <table id="tasks_datatable" class="table table-bordered table-striped" style="width:100%">
                        <thead class="table-light">
                        <tr>
                            <th>Task ID</th>
                            <th>Title</th>
                            <th>Type & Priority</th>
                            <th>Status</th>
                            <th>Assigned & Due</th>
                            <th>Created By</th>
                            <th style="width: 50px;">Actions</th>
                        </tr>
                        </thead>
                        <tbody>
                        <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floating Quick Add Button -->
<?php if (has_permission('tasks/add')): ?>
<div class="floating-add-btn">
    <button id="floating_quick_add" class="btn btn-primary btn-lg rounded-circle shadow-lg" 
            title="Add New Task"
            onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add', 'Add New Task')">
        <i class="fas fa-plus fa-lg"></i>
    </button>
</div>
<?php endif; ?>

<style>
/* Optimized Design Styles */
.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.rounded-lg {
    border-radius: 0.5rem !important;
}

/* Font weight optimization */
.font-weight-bold {
    font-weight: 600 !important;
}

/* Card hover effects */
.bg-white:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

/* Badge styling */
.badge {
    font-size: 0.9em;
    padding: 0.4em 0.8em;
}

/* Enhanced search section styling */
.search-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Enhanced search input styling */
.input-group-lg .form-control {
    height: 48px !important;
    font-size: 16px !important;
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.input-group-lg .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: translateY(-1px);
}

.input-group-lg .input-group-text {
    height: 48px !important;
    font-size: 16px !important;
    background-color: #e9ecef;
    border-color: #ced4da;
}

.input-group-lg .btn {
    height: 48px !important;
    font-size: 16px !important;
}

/* Add button styling */
.btn-add-actions {
    min-width: 120px;
    height: 48px;
}

/* Floating button styling */
.floating-add-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    display: block !important;
}

.floating-add-btn .btn {
    width: 60px;
    height: 60px;
    border-radius: 50% !important;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.floating-add-btn .btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.floating-add-btn .btn:active {
    transform: scale(0.95);
}

/* Job ID Dashboard styling */
.job_id_dashboard {
    font-size: 14px;
    color: #007bff;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.job_id_dashboard b {
    font-weight: 700;
    color: #0056b3;
}

/* Due date styling */
.due_date {
    border-radius: 10px;
    padding: 2px 10px;
    font-size: 12px!important;
    font-weight: bold;
    background-color: rgba(224, 223, 223, 0.16);
}

/* DataTables performance optimizations */
.dataTables_wrapper .dataTables_processing {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Subtle Table Improvements */
.table-light th {
    font-weight: 500;
    font-size: 13px;
    padding: 12px 10px;
}

.table-light th i {
    opacity: 0.7;
}

/* Subtle row hover effect */
#tasks_datatable tbody tr:hover {
    background-color: #f8f9fa;
}

/* Improved spacing for table cells */
#tasks_datatable td {
    padding: 10px;
    vertical-align: middle;
}

/* Better badge styling */
.badge {
    font-size: 11px;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .floating-add-btn {
        bottom: 20px;
        right: 20px;
    }
    
    .floating-add-btn .btn {
        width: 50px;
        height: 50px;
    }
    
    .btn-add-actions {
        min-width: 80px;
        height: 40px;
    }
    
    .input-group-lg .form-control {
        font-size: 16px !important; /* Prevent zoom on iOS */
    }
}

/* Animation for the floating button */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.floating-add-btn .btn {
    animation: float 3s ease-in-out infinite;
}

.floating-add-btn .btn:hover {
    animation: none;
}

/* Overview Cards Styling */
.overview-card {
    padding: 15px 10px;
    border-radius: 8px;
    background-color: #f8f9fa;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: default;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.overview-card.active {
    background-color: #ffffff;
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0,123,255,0.2);
}

.overview-card .text-muted {
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
</style>

<script>
$(document).ready(function() {
    // Initialize optimized DataTable for large datasets
    var table = $('#tasks_datatable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?= site_url('app/tasks/ajax_list') ?>",
            "type": "POST",
            "data": function(d) {
                // Add filter parameters
                d.filter_status = $('#filter_status').val();
                d.filter_priority = $('#filter_priority').val();
                d.filter_project = $('#filter_project').val();
                d.filter_assigned = $('#filter_assigned').val();
                d.filter_date_from = $('#filter_date_from').val();
                d.filter_date_to = $('#filter_date_to').val();
            }
        },
        "columns": [
            { "data": 0, "orderable": true, "searchable": true, "width": "12%" }, // Task ID
            { "data": 1, "width": "30%" }, // Title (with Project)
            { "data": 2, "width": "15%" }, // Type & Priority
            { "data": 3, "width": "12%" }, // Status
            { "data": 4, "width": "18%" }, // Assigned & Due
            { "data": 5, "width": "13%" }, // Created By
            { "data": 6, "orderable": false, "searchable": false, "width": "5%" } // Actions
        ],
        "order": [[ 0, "desc" ]], // Order by Task ID descending (latest first)
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        "language": {
            "processing": "Loading tasks...",
            "emptyTable": "No tasks found",
            "info": "Showing _START_ to _END_ of _TOTAL_ tasks",
            "infoEmpty": "Showing 0 to 0 of 0 tasks",
            "infoFiltered": "(filtered from _MAX_ total tasks)",
            "search": "",
            "paginate": {
                "first": "First",
                "last": "Last",
                "next": "Next",
                "previous": "Previous"
            }
        },
        "dom": '<"row"<"col-sm-6"l><"col-sm-6">>rtip', // Remove default search box
        "responsive": true,
        "deferRender": true,
        "stateSave": true,
        "searching": true,
        "drawCallback": function(settings) {
            // Debug information
            console.log('DataTables Info:', settings.json);
            console.log('Total Records:', settings.json ? settings.json.recordsTotal : 'N/A');
            console.log('Filtered Records:', settings.json ? settings.json.recordsFiltered : 'N/A');
        }
    });

    // Enhanced search functionality
    $('#enhanced_search').on('keyup', function() {
        var searchValue = $(this).val();
        table.search(searchValue).draw();
    });

    // Clear search button
    $('#clear_search').on('click', function() {
        $('#enhanced_search').val('');
        table.search('').draw();
    });

    // Filter functionality
    $('#apply_filters').on('click', function() {
        table.ajax.reload();
    });

    // Clear filters
    $('#clear_filters').on('click', function() {
        $('#filter_status').val('');
        $('#filter_priority').val('');
        $('#filter_project').val('');
        $('#filter_assigned').val('');
        $('#filter_date_from').val('');
        $('#filter_date_to').val('');
        table.ajax.reload();
    });

    // Refresh table
    $('#refresh_table').on('click', function() {
        table.ajax.reload();
    });

    // Auto-apply filters when changed
    $('#filter_status, #filter_priority, #filter_project, #filter_assigned').on('change', function() {
        table.ajax.reload();
    });

    // Date filters
    $('#filter_date_from, #filter_date_to').on('change', function() {
        table.ajax.reload();
    });

    // Initialize floating button tooltip
    $('#floating_quick_add').tooltip();
    
    // Show floating button always
    $('.floating-add-btn').show();
});

// AJAX modal function for quick actions
function show_ajax_modal(url, title) {
    $.ajax({
        url: url,
        type: 'GET',
        success: function(response) {
            $('#ajax_modal_content').html(response);
            $('#ajax_modal_title').text(title);
            $('#ajax_modal').modal('show');
        },
        error: function() {
            alert('Error loading content');
        }
    });
}
</script> 