<!-- Modern Mobile-First Task View -->
<div class="task-view-container">
    <!-- Mobile Header -->
    <div class="mobile-header">
        <div class="header-actions">
            <a href="<?= base_url("app/tasks/index"); ?>" class="btn-back">
                <i class="fas fa-arrow-left"></i>
                <span class="btn-text">Back</span>
            </a>

            <?php if (has_permission('tasks/edit')): ?>
                <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/edit'); ?>', 'Edit Task')"
                        class="btn-edit">
                    <i class="fas fa-edit"></i>
                    <span class="btn-text">Edit</span>
                </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- Task Title Section -->
    <div class="task-title-section">
        <h1 class="task-title"><?= $task['title'] ?></h1>
        <div class="task-id">#<?= str_pad($task['id'], 6, '0', STR_PAD_LEFT) ?></div>
    </div>

    <!-- Task Status Cards -->
    <div class="status-cards-grid">
        <div class="status-card status-primary">
            <div class="status-icon">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="status-content">
                <div class="status-label">Status</div>
                <div class="status-value status-<?= $task['task_status'] ?>"><?= ucfirst($task['task_status']) ?></div>
            </div>
        </div>

        <div class="status-card priority-card">
            <div class="status-icon">
                <i class="fas fa-bolt"></i>
            </div>
            <div class="status-content">
                <div class="status-label">Priority</div>
                <div class="status-value priority-<?= $task['task_priority'] ?>"><?= ucfirst(get_task_priority($task['task_priority'])) ?></div>
            </div>
        </div>

        <div class="status-card time-card">
            <div class="status-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="status-content">
                <div class="status-label">Time Logged</div>
                <div class="status-value">
                    <?php
                    $total_seconds = 0;
                    if (!empty($task_history)) {
                        foreach ($task_history as $history) {
                            if (!empty($history['duration'])) {
                                $total_seconds += $history['duration'] * 60;
                            }
                        }
                    }
                    echo gmdate('H:i', $total_seconds);
                    ?>
                </div>
            </div>
        </div>

        <div class="status-card due-card">
            <div class="status-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="status-content">
                <div class="status-label">Due Date</div>
                <div class="status-value">
                    <?= (!empty($task['due_date']) && $task['due_date'] !== '0000-00-00 00:00:00') ? date('M d, Y', strtotime($task['due_date'])) : 'Not set' ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Task Details Section -->
    <div class="task-details-section">
        <div class="detail-item">
            <div class="detail-label">
                <i class="fas fa-project-diagram"></i>
                Project
            </div>
            <div class="detail-value"><?= $task['project_title'] ?? 'No project assigned' ?></div>
        </div>

        <div class="detail-item">
            <div class="detail-label">
                <i class="fas fa-user"></i>
                Assigned To
            </div>
            <div class="detail-value"><?= $task['assigned_user_name'] ?? 'Unassigned' ?></div>
        </div>

        <div class="detail-item">
            <div class="detail-label">
                <i class="fas fa-building"></i>
                Client
            </div>
            <div class="detail-value"><?= $task['client_name'] ?? 'No client' ?></div>
        </div>

        <div class="detail-item">
            <div class="detail-label">
                <i class="fas fa-tag"></i>
                Type
            </div>
            <div class="detail-value"><?= get_task_type($task['task_type']) ?></div>
        </div>
    </div>

    <!-- Task Description -->
    <?php if (!empty($task['description'])): ?>
    <div class="task-description-section">
        <div class="section-header">
            <i class="fas fa-align-left"></i>
            <span>Description</span>
        </div>
        <div class="description-content">
            <?= strip_tags($task['description'], '<p><br><strong><em><ul><ol><li>') ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Quick Actions Section -->
    <?php if ($task['task_status'] !== 'completed' && has_permission('tasks/edit')): ?>
    <div class="quick-actions-section">
        <div class="section-header">
            <i class="fas fa-bolt"></i>
            <span>Quick Actions</span>
        </div>

        <form action="<?= base_url('app/tasks/task_status/'.$task['id']) ?>" method="post" class="quick-status-form">
            <div class="form-group">
                <label for="status">Change Status:</label>
                <select name="task_status" id="status" class="form-control" required>
                    <option value="">Select Status</option>
                    <option value="pending" <?= $task['task_status'] == 'pending' ? 'selected' : '' ?>>Pending</option>
                    <option value="assigned" <?= $task['task_status'] == 'assigned' ? 'selected' : '' ?>>Assigned</option>
                    <option value="testing" <?= $task['task_status'] == 'testing' ? 'selected' : '' ?>>Testing</option>
                    <option value="completed" <?= $task['task_status'] == 'completed' ? 'selected' : '' ?>>Completed</option>
                    <option value="on_hold" <?= $task['task_status'] == 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                </select>
            </div>

            <!-- Assigned To dropdown - shown only when status is "assigned" -->
            <div class="form-group" id="assigned_to_group" style="display: <?= $task['task_status'] == 'assigned' ? 'block' : 'none' ?>;">
                <label for="assigned_to">Assign To:</label>
                <select name="user_id" id="assigned_to" class="form-control select2" style="width: 100%;">
                    <option value="">Select User</option>
                    <?php if (isset($users)): ?>
                        <?php foreach ($users as $user_id => $user_name): ?>
                            <option value="<?= $user_id ?>" <?= $task['user_id'] == $user_id ? 'selected' : '' ?>>
                                <?= htmlspecialchars($user_name) ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <button type="submit" class="btn-primary-action">
                <i class="fas fa-sync"></i> Update Status
            </button>
        </form>
    </div>
    <?php endif; ?>

    <!-- Comments Section -->
    <div class="comments-section">
        <div class="section-header">
            <i class="fas fa-comments"></i>
            <span>Comments & History</span>
            <?php if (!empty($task_history)): ?>
                <span class="comment-count"><?= count($task_history) ?></span>
            <?php endif; ?>
        </div>

        <!-- Add Comment Form -->
        <?php if (has_permission('tasks/edit')): ?>
        <div class="add-comment-form">
            <form id="add_comment_form">
                <div class="form-group">
                    <textarea name="remarks" id="comment_text" class="form-control" rows="3"
                            placeholder="Add a comment..." required></textarea>
                </div>

                <!-- Status Update Option -->
                <div class="status-update-toggle">
                    <label class="toggle-switch">
                        <input type="checkbox" id="update_status_checkbox">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">Also update status</span>
                    </label>
                </div>

                <div class="status-update-options" id="status_update_options" style="display: none;">
                    <div class="form-group">
                        <select name="new_status" id="new_status" class="form-control">
                            <option value="">Select New Status</option>
                            <option value="pending" <?= $task['task_status'] == 'pending' ? 'selected' : '' ?>>Pending</option>
                            <option value="assigned" <?= $task['task_status'] == 'assigned' ? 'selected' : '' ?>>Assigned</option>
                            <option value="testing" <?= $task['task_status'] == 'testing' ? 'selected' : '' ?>>Testing</option>
                            <option value="completed" <?= $task['task_status'] == 'completed' ? 'selected' : '' ?>>Completed</option>
                            <option value="on_hold" <?= $task['task_status'] == 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                        </select>
                    </div>

                    <!-- Assigned To dropdown for comments form -->
                    <div class="form-group" id="comment_assigned_to_group" style="display: none;">
                        <select name="comment_user_id" id="comment_assigned_to" class="form-control">
                            <option value="">Select User to Assign</option>
                            <?php if (isset($users)): ?>
                                <?php foreach ($users as $user_id => $user_name): ?>
                                    <option value="<?= $user_id ?>" <?= $task['user_id'] == $user_id ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($user_name) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" id="submit_comment_btn" class="btn-primary-action">
                        <i class="fas fa-paper-plane"></i>
                        <span id="submit_btn_text">Add Comment</span>
                    </button>
                </div>
            </form>
        </div>
        <?php endif; ?>

        <!-- Comments Timeline -->
        <div class="comments-timeline" id="comments_timeline">
            <?php if (!empty($task_history)): ?>
                <?php foreach ($task_history as $index => $history): ?>
                    <div class="comment-item">
                        <div class="comment-avatar">
                            <?php if (!empty($history['user_photo']) && file_exists(FCPATH . 'uploads/users/' . $history['user_photo'])): ?>
                                <img src="<?= base_url('uploads/users/'.$history['user_photo']) ?>"
                                    alt="<?= htmlspecialchars($history['user_name']) ?>"
                                    class="avatar-img"
                                    onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="avatar-fallback" style="display: none;">
                                    <?= strtoupper(substr($history['user_name'], 0, 1)) ?>
                                </div>
                            <?php else: ?>
                                <div class="avatar-fallback">
                                    <?= strtoupper(substr($history['user_name'], 0, 1)) ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="comment-content">
                            <div class="comment-header">
                                <div class="comment-author"><?= htmlspecialchars($history['user_name']) ?></div>
                                <div class="comment-time">
                                    <?php
                                    $time_diff = time() - strtotime($history['created_at']);
                                    if ($time_diff < 60) {
                                        echo 'Just now';
                                    } elseif ($time_diff < 3600) {
                                        echo floor($time_diff / 60) . 'm ago';
                                    } elseif ($time_diff < 86400) {
                                        echo floor($time_diff / 3600) . 'h ago';
                                    } elseif ($time_diff < 604800) {
                                        echo floor($time_diff / 86400) . 'd ago';
                                    } else {
                                        echo date('M d', strtotime($history['created_at']));
                                    }
                                    ?>
                                </div>
                                <?php if (!empty($history['duration'])): ?>
                                    <div class="comment-duration">
                                        <i class="fas fa-clock"></i> <?= gmdate('H:i', $history['duration'] * 60) ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="comment-text">
                                <?= nl2br(htmlspecialchars($history['remarks'])) ?>
                            </div>

                            <?php if (!empty($history['start_time']) && !empty($history['end_time'])): ?>
                                <div class="comment-time-period">
                                    <i class="fas fa-clock"></i>
                                    <?= date('H:i', strtotime($history['start_time'])) ?> - <?= date('H:i', strtotime($history['end_time'])) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="no-comments">
                    <div class="no-comments-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="no-comments-text">
                        <h3>No comments yet</h3>
                        <p>Be the first to add a comment to this task</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Task Timeline Section -->
    <div class="task-timeline-section">
        <div class="section-header">
            <i class="fas fa-history"></i>
            <span>Timeline</span>
        </div>

        <div class="timeline-items">
            <div class="timeline-item">
                <div class="timeline-icon created">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-title">Task Created</div>
                    <div class="timeline-date"><?= date('M d, Y \a\t g:i A', strtotime($task['created_on'])) ?></div>
                    <div class="timeline-user">
                        by <?= isset($users[$task['created_by']]) ? $users[$task['created_by']] : 'System' ?>
                    </div>
                </div>
            </div>

            <?php if ($task['updated_on'] && $task['updated_on'] != $task['created_on']): ?>
            <div class="timeline-item">
                <div class="timeline-icon updated">
                    <i class="fas fa-edit"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-title">Last Updated</div>
                    <div class="timeline-date"><?= date('M d, Y \a\t g:i A', strtotime($task['updated_on'])) ?></div>
                    <div class="timeline-user">
                        by <?= isset($users[$task['updated_by']]) ? $users[$task['updated_by']] : 'System' ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* Mobile-First Task View Styles */
* {
    box-sizing: border-box;
}

.task-view-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
    background: #f8f9fa;
    min-height: 100vh;
}

/* Mobile Header */
.mobile-header {
    background: #fff;
    padding: 12px 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    margin-bottom: 16px;
}

.header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

.btn-back, .btn-edit {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-back {
    background: #6c757d;
    color: white;
}

.btn-edit {
    background: #007bff;
    color: white;
}

.btn-back:hover, .btn-edit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    color: white;
    text-decoration: none;
}

/* Task Title Section */
.task-title-section {
    background: #fff;
    padding: 20px 16px;
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.task-title {
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 8px 0;
    line-height: 1.3;
}

.task-id {
    font-size: 14px;
    color: #6c757d;
    font-weight: 600;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

/* Status Cards Grid */
.status-cards-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
}

.status-card {
    background: #fff;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    gap: 12px;
    transition: transform 0.2s ease;
}

.status-card:hover {
    transform: translateY(-2px);
}

.status-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
}

.status-primary .status-icon { background: #007bff; }
.priority-card .status-icon { background: #dc3545; }
.time-card .status-icon { background: #28a745; }
.due-card .status-icon { background: #ffc107; color: #212529; }

.status-content {
    flex: 1;
}

.status-label {
    font-size: 11px;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.status-value {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

/* Status-specific colors */
.status-pending { color: #dc3545; }
.status-assigned { color: #007bff; }
.status-testing { color: #6c757d; }
.status-completed { color: #28a745; }
.status-on_hold { color: #ffc107; }

.priority-critical { color: #dc3545; }
.priority-high { color: #fd7e14; }
.priority-medium { color: #ffc107; }
.priority-low { color: #28a745; }

/* Task Details Section */
.task-details-section {
    background: #fff;
    padding: 20px 16px;
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.detail-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 100px;
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.detail-value {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
    margin-left: 16px;
}

/* Task Description */
.task-description-section {
    background: #fff;
    padding: 20px 16px;
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.description-content {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    line-height: 1.6;
    color: #495057;
    font-size: 14px;
}

/* Quick Actions Section */
.quick-actions-section {
    background: #fff;
    padding: 20px 16px;
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.quick-status-form .form-group {
    margin-bottom: 16px;
}

.quick-status-form label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    display: block;
}

.quick-status-form .form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.quick-status-form .form-control:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.btn-primary-action {
    width: 100%;
    padding: 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary-action:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

/* Comments Section */
.comments-section {
    background: #fff;
    padding: 20px 16px;
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.comment-count {
    background: #007bff;
    color: white;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
}

/* Add Comment Form */
.add-comment-form {
    margin-bottom: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid #f1f3f4;
}

.add-comment-form .form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    resize: vertical;
    min-height: 80px;
    transition: border-color 0.2s ease;
}

.add-comment-form .form-control:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

/* Status Update Toggle */
.status-update-toggle {
    margin: 16px 0;
}

.toggle-switch {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    user-select: none;
}

.toggle-switch input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    width: 44px;
    height: 24px;
    background: #ccc;
    border-radius: 12px;
    position: relative;
    transition: background 0.2s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    transition: transform 0.2s ease;
}

.toggle-switch input:checked + .toggle-slider {
    background: #007bff;
}

.toggle-switch input:checked + .toggle-slider::before {
    transform: translateX(20px);
}

.toggle-label {
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
}

.status-update-options {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.form-actions {
    margin-top: 16px;
}

/* Comments Timeline */
.comments-timeline {
    max-height: 500px;
    overflow-y: auto;
}

.comment-item {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f1f3f4;
}

.comment-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.comment-avatar {
    flex-shrink: 0;
}

.avatar-img, .avatar-fallback {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.avatar-img {
    object-fit: cover;
    border: 2px solid #e9ecef;
}

.avatar-fallback {
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 16px;
}

.comment-content {
    flex: 1;
    min-width: 0;
}

.comment-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.comment-author {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.comment-time {
    font-size: 12px;
    color: #6c757d;
}

.comment-duration {
    font-size: 11px;
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 6px;
    border-radius: 4px;
}

.comment-text {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.5;
    color: #495057;
    margin-bottom: 8px;
}

.comment-time-period {
    font-size: 11px;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* No Comments State */
.no-comments {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-comments-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.3;
}

.no-comments-text h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #495057;
}

.no-comments-text p {
    font-size: 14px;
    margin: 0;
}

/* Task Timeline Section */
.task-timeline-section {
    background: #fff;
    padding: 20px 16px;
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.timeline-items {
    margin-top: 16px;
}

.timeline-item {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    flex-shrink: 0;
}

.timeline-icon.created {
    background: #28a745;
}

.timeline-icon.updated {
    background: #007bff;
}

.timeline-content {
    flex: 1;
}

.timeline-title {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    margin-bottom: 4px;
}

.timeline-date {
    font-size: 13px;
    color: #495057;
    margin-bottom: 2px;
}

.timeline-user {
    font-size: 12px;
    color: #6c757d;
}

/* Responsive Design */
@media (min-width: 768px) {
    .task-view-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .mobile-header {
        border-radius: 12px;
        margin-bottom: 24px;
    }

    .status-cards-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
    }

    .task-title {
        font-size: 24px;
    }

    .btn-text {
        display: inline;
    }

    .comments-timeline {
        max-height: none;
    }
}

@media (min-width: 1024px) {
    .task-view-container {
        max-width: 1000px;
    }

    .status-cards-grid {
        gap: 20px;
    }

    .task-title {
        font-size: 28px;
    }
}

/* Hide text on very small screens */
@media (max-width: 480px) {
    .btn-text {
        display: none;
    }

    .status-cards-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .task-title {
        font-size: 18px;
    }

    .detail-label {
        min-width: 80px;
        font-size: 11px;
    }

    .detail-value {
        font-size: 13px;
        margin-left: 12px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .task-view-container {
        background: #1a1a1a;
        color: #e0e0e0;
    }

    .mobile-header,
    .task-title-section,
    .status-card,
    .task-details-section,
    .task-description-section,
    .quick-actions-section,
    .comments-section,
    .task-timeline-section {
        background: #2d2d2d;
        color: #e0e0e0;
    }

    .task-title,
    .status-value,
    .detail-value,
    .comment-author,
    .timeline-title {
        color: #ffffff;
    }

    .status-label,
    .detail-label,
    .comment-time,
    .timeline-user {
        color: #b0b0b0;
    }

    .comment-text {
        background: #3a3a3a;
        color: #e0e0e0;
    }

    .form-control {
        background: #3a3a3a;
        border-color: #555;
        color: #e0e0e0;
    }

    .form-control:focus {
        border-color: #007bff;
        background: #3a3a3a;
    }
}

/* Print styles */
@media print {
    .mobile-header,
    .quick-actions-section,
    .add-comment-form {
        display: none;
    }

    .task-view-container {
        background: white;
        box-shadow: none;
    }

    .status-card,
    .task-details-section,
    .task-description-section,
    .comments-section,
    .task-timeline-section {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .status-card,
    .task-details-section,
    .task-description-section,
    .quick-actions-section,
    .comments-section,
    .task-timeline-section {
        border: 2px solid #000;
    }

    .btn-back,
    .btn-edit,
    .btn-primary-action {
        border: 2px solid #000;
    }
}
</style>

<script>
$(document).ready(function() {
    // Initialize assigned to dropdown visibility based on current status
    var currentStatus = $('#status').val();
    if (currentStatus === 'assigned') {
        $('#assigned_to_group').show();
        $('#assigned_to').prop('required', true);
    }

    // Handle status change in Quick Actions form
    $('#status').on('change', function() {
        var selectedStatus = $(this).val();
        if (selectedStatus === 'assigned') {
            $('#assigned_to_group').show();
            $('#assigned_to').prop('required', true);
        } else {
            $('#assigned_to_group').hide();
            $('#assigned_to').prop('required', false);
        }
    });

    // Handle status update toggle
    $('#update_status_checkbox').on('change', function() {
        var isChecked = $(this).is(':checked');
        var statusOptions = $('#status_update_options');

        if (isChecked) {
            statusOptions.show();
            $('#submit_btn_text').text('Add Comment & Update Status');
            $('#new_status').focus();
        } else {
            statusOptions.hide();
            $('#submit_btn_text').text('Add Comment');
            $('#new_status').val('<?= $task['task_status'] ?>');
            $('#comment_assigned_to_group').hide();
            $('#comment_assigned_to').prop('required', false);
        }
    });

    // Handle status change in Comments form
    $('#new_status').on('change', function() {
        var selectedStatus = $(this).val();
        if (selectedStatus === 'assigned') {
            $('#comment_assigned_to_group').show();
            $('#comment_assigned_to').prop('required', true);
        } else {
            $('#comment_assigned_to_group').hide();
            $('#comment_assigned_to').prop('required', false);
        }
    });

    // Handle comment form submission
    $('#add_comment_form').on('submit', function(e) {
        e.preventDefault();

        var formData = {
            remarks: $('#comment_text').val().trim()
        };

        // Check if status update is requested
        var updateStatus = $('#update_status_checkbox').is(':checked');
        if (updateStatus) {
            var newStatus = $('#new_status').val();
            if (!newStatus) {
                showAlert('Please select a new status or uncheck the status update option.', 'warning');
                return;
            }

            // Check if status is actually changing
            if (newStatus === '<?= $task['task_status'] ?>') {
                // If status is not changing but it's "assigned", check for user reassignment
                if (newStatus === 'assigned') {
                    var assignedUserId = $('#comment_assigned_to').val();
                    if (!assignedUserId) {
                        showAlert('Please select a user to assign the task to.', 'warning');
                        return;
                    }
                    // Check if user is actually changing
                    if (assignedUserId === '<?= $task['user_id'] ?>') {
                        showAlert('The selected user is the same as the current assigned user. Please select a different user or uncheck the status update option.', 'warning');
                        return;
                    }
                    formData.user_id = assignedUserId;
                } else {
                    showAlert('The selected status is the same as the current status. Please select a different status or uncheck the status update option.', 'warning');
                    return;
                }
            } else {
                // If status is "assigned", check if user is selected
                if (newStatus === 'assigned') {
                    var assignedUserId = $('#comment_assigned_to').val();
                    if (!assignedUserId) {
                        showAlert('Please select a user to assign the task to when changing status to "Assigned".', 'warning');
                        return;
                    }
                    formData.user_id = assignedUserId;
                }

                formData.update_status = true;
                formData.new_status = newStatus;
            }
        }

        if (!formData.remarks) {
            showAlert('Please enter a comment before submitting.', 'warning');
            return;
        }

        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();

        // Update button text based on action
        var loadingText = updateStatus ?
            '<i class="fas fa-spinner fa-spin"></i> Adding Comment & Updating Status...' :
            '<i class="fas fa-spinner fa-spin"></i> Adding Comment...';

        // Disable submit button and show loading
        submitBtn.prop('disabled', true).html(loadingText);

        $.ajax({
            url: '<?= base_url("app/tasks/add_comment/".$task['id']) ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // Clear the form
                    $('#comment_text').val('');
                    $('#update_status_checkbox').prop('checked', false);
                    $('#status_update_options').hide();
                    $('#comment_assigned_to_group').hide();
                    $('#comment_assigned_to').prop('required', false);
                    $('#submit_btn_text').text('Add Comment');

                    // Reload the page to show the new comment and updated status
                    location.reload();
                } else {
                    showAlert('Error: ' + response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                showAlert('An error occurred while processing your request. Please try again.', 'error');
                console.error('AJAX Error:', error);
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Auto-resize textarea
    $('#comment_text').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Simple alert function for better mobile UX
    function showAlert(message, type) {
        // You can replace this with a more sophisticated toast/alert system
        alert(message);
    }

    // Initialize Select2 for better mobile experience
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2({
            width: '100%',
            placeholder: 'Select an option',
            allowClear: true
        });
    }
});
</script>