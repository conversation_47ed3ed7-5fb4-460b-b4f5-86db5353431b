<!-- Modern Task View -->
<div class="container-fluid" style="margin-top: -10px;padding:10px;">
    <div class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/tasks/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left shadow-pro" style="width: 49%;max-width: 200px;">
            <i class="fas fa-arrow-circle-left"></i> Back
        </a>
        
        <?php if (has_permission('tasks/edit')): ?>
            <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/edit'); ?>', 'Edit Task')"
                    class="btn btn-info btn-mini float-right" style="width: 49%;max-width: 200px;">
                <small><i class="fas fa-pencil-alt"></i></small> Edit
            </button>
        <?php endif; ?>
    </div>
    
    <div class="row">
        <!-- Task Details Card -->
        <div class="col-md-8">
            <div class="card card-primary shadow-pro">
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <h4 class="text-primary mb-4 task-title-bg"><?= $task['title'] ?></h4>
                        </div>
                        
                        <!-- Compact Task Info Section -->
                        <div class="task-info-compact mb-4">
                            <div class="info-row">
                                <div>
                                    <span class="info-label"><i class="fas fa-tasks text-info"></i> Status</span>
                                    <span class="badge badge-status"><?= strtoupper($task['task_status']) ?></span>
                                </div>
                                <div>
                                    <span class="info-label"><i class="fas fa-bolt text-danger"></i> Priority</span>
                                    <span class="badge badge-priority"><?= strtoupper(get_task_priority($task['task_priority'])) ?></span>
                                </div>
                            </div>
                            <div class="info-row">
                                <div>
                                    <span class="info-label"><i class="fas fa-project-diagram text-primary"></i> Project</span>
                                    <span class="info-value"><?= $task['project_title'] ?? 'N/A' ?></span>
                                </div>
                                <div>
                                    <span class="info-label"><i class="fas fa-tag text-info"></i> Type</span>
                                    <span class="info-value"><?= get_task_type($task['task_type']) ?></span>
                                </div>
                            </div>
                            <div class="info-row">
                                <div>
                                    <span class="info-label"><i class="fas fa-user text-success"></i> Assigned To</span>
                                    <span class="info-value"><?= $task['assigned_user_name'] ?? 'Unassigned' ?></span>
                                </div>
                                <div>
                                    <span class="info-label"><i class="fas fa-building text-warning"></i> Client</span>
                                    <span class="info-value"><?= $task['client_name'] ?? 'N/A' ?></span>
                                </div>
                            </div>
                            <div class="info-row">
                                <div>
                                    <span class="info-label"><i class="fas fa-calendar-alt text-danger"></i> Due Date</span>
                                    <span class="info-value"><?= (!empty($task['due_date']) && $task['due_date'] !== '0000-00-00 00:00:00') ? date('d-m-Y H:i', strtotime($task['due_date'])) : 'N/A' ?></span>
                                </div>
                                <div>
                                    <span class="info-label"><i class="fas fa-clock text-secondary"></i> Time Logged</span>
                                    <span class="info-value">
                                        <?php
                                        $total_seconds = 0;
                                        if (!empty($task_history)) {
                                            foreach ($task_history as $history) {
                                                if (!empty($history['duration'])) {
                                                    $total_seconds += $history['duration'] * 60;
                                                }
                                            }
                                        }
                                        echo gmdate('H:i', $total_seconds);
                                        ?>
                                    </span>
                                </div>
                            </div>
                            <div class="info-row">
                                <div>
                                    <span class="info-label"><i class="fas fa-calendar-plus text-info"></i> Created</span>
                                    <span class="info-value"><?= date('d-m-Y H:i', strtotime($task['created_on'])) ?></span>
                                </div>
                                <div>
                                    <span class="info-label"><i class="fas fa-edit text-warning"></i> Last Updated</span>
                                    <span class="info-value"><?= $task['updated_on'] && $task['updated_on'] != $task['created_on'] ? date('d-m-Y H:i', strtotime($task['updated_on'])) : 'Not updated' ?></span>
                                </div>
                            </div>
                        </div>
                        
                        <?php if (!empty($task['description'])): ?>
                        <div class="col-12">
                            <div class="detail-group">
                                <label class="detail-label">
                                    <i class="fas fa-align-left text-primary"></i> Description
                                </label>
                                <div class="description-content">
                                    <?= strip_tags($task['description'], '<p><br><strong><em><ul><ol><li>') ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Comments/History Section -->
            <div class="col-12 mt-4">
                <div class="card card-success shadow-pro">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-comments"></i> Comments & History
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- Add Comment Form -->
                        <?php if (has_permission('tasks/edit')): ?>
                        <div class="mb-4">
                            <form id="add_comment_form" class="border rounded p-3 bg-light">
                                <div class="form-group">
                                    <label for="comment_text"><strong>Add Comment:</strong></label>
                                    <textarea name="remarks" id="comment_text" class="form-control" rows="3"
                                            placeholder="Enter your comment here..." required></textarea>
                                </div>

                                <!-- Status Update Option -->
                                <div class="form-group status-update-section">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="update_status_checkbox">
                                                <label class="custom-control-label" for="update_status_checkbox">
                                                    <i class="fas fa-sync-alt"></i> <strong>Also update task status</strong>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <select name="new_status" id="new_status" class="form-control" disabled>
                                                <option value="">Select New Status</option>
                                                <option value="pending" <?= $task['task_status'] == 'pending' ? 'selected' : '' ?>>Pending</option>
                                                <option value="assigned" <?= $task['task_status'] == 'assigned' ? 'selected' : '' ?>>Assigned</option>
                                                <option value="testing" <?= $task['task_status'] == 'testing' ? 'selected' : '' ?>>Testing</option>
                                                <option value="completed" <?= $task['task_status'] == 'completed' ? 'selected' : '' ?>>Completed</option>
                                                <option value="on_hold" <?= $task['task_status'] == 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                                            </select>
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle"></i> Current:
                                                <span class="badge badge-secondary">
                                                    <?php
                                                    $status_labels = [
                                                        'pending' => 'Pending',
                                                        'assigned' => 'Assigned',
                                                        'testing' => 'Testing',
                                                        'completed' => 'Completed',
                                                        'on_hold' => 'On Hold'
                                                    ];
                                                    echo $status_labels[$task['task_status']] ?? $task['task_status'];
                                                    ?>
                                                </span>
                                            </small>
                                        </div>
                                    </div>
                                    
                                    <!-- Assigned To dropdown for comments form - shown only when status is "assigned" -->
                                    <div class="form-group mt-3" id="comment_assigned_to_group" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <label for="comment_assigned_to"><strong>Assign To:</strong></label>
                                                <select name="comment_user_id" id="comment_assigned_to" class="form-control select2" style="width: 100%;">
                                                    <option value="">Select User</option>
                                                    <?php if (isset($users)): ?>
                                                        <?php foreach ($users as $user_id => $user_name): ?>
                                                                                                        <option value="<?= $user_id ?>" <?= $task['user_id'] == $user_id ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($user_name) ?>
                                            </option>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </select>
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle"></i> 
                                                    When changing status to "Assigned", you must select a user to assign the task to.
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-0">
                                    <button type="submit" id="submit_comment_btn" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> <span id="submit_btn_text">Add Comment</span>
                                    </button>
                                    <button type="reset" id="reset_form_btn" class="btn btn-outline-secondary ml-2">
                                        <i class="fas fa-times"></i> Clear
                                    </button>
                                </div>
                            </form>
                        </div>
                        <?php endif; ?>

                        <!-- Comments Timeline -->
                        <div id="comments_timeline">
                            <?php if (!empty($task_history)): ?>
                                <div class="timeline-header mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-history"></i>
                                        <strong><?= count($task_history) ?></strong> entries found
                                        <span class="ml-2">
                                            <i class="fas fa-sort-amount-down"></i> Latest first
                                        </span>
                                    </small>
                                </div>
                                <?php foreach ($task_history as $index => $history): ?>
                                    <div class="comment-item border-bottom pb-3 mb-3 position-relative">
                                        <?php if ($index < count($task_history) - 1): ?>
                                            <div class="timeline-connector"></div>
                                        <?php endif; ?>
                                        <div class="d-flex">
                                            <div class="comment-avatar mr-3">
                                                <?php if (!empty($history['user_photo']) && file_exists(FCPATH . 'uploads/users/' . $history['user_photo'])): ?>
                                                    <img src="<?= base_url('uploads/users/'.$history['user_photo']) ?>"
                                                        alt="<?= htmlspecialchars($history['user_name']) ?>"
                                                        class="user-avatar rounded-circle shadow-sm"
                                                        style="width: 45px; height: 45px; object-fit: cover; border: 2px solid #e9ecef;"
                                                        onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                    <div class="user-avatar-fallback bg-primary text-white rounded-circle d-flex align-items-center justify-content-center shadow-sm"
                                                        style="width: 45px; height: 45px; font-size: 16px; font-weight: bold; border: 2px solid #e9ecef; display: none;">
                                                        <?= strtoupper(substr($history['user_name'], 0, 1)) ?>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="user-avatar-fallback bg-primary text-white rounded-circle d-flex align-items-center justify-content-center shadow-sm"
                                                        style="width: 45px; height: 45px; font-size: 16px; font-weight: bold; border: 2px solid #e9ecef;">
                                                        <?= strtoupper(substr($history['user_name'], 0, 1)) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="comment-content flex-grow-1">
                                                <div class="comment-header mb-2">
                                                    <strong class="text-primary"><?= htmlspecialchars($history['user_name']) ?></strong>
                                                    <small class="text-muted ml-2">
                                                        <i class="fas fa-clock"></i>
                                                        <?= date('d-m-Y g:i A', strtotime($history['created_at'])) ?>
                                                        <span class="ml-1 text-info">
                                                            <?php
                                                            $time_diff = time() - strtotime($history['created_at']);
                                                            if ($time_diff < 60) {
                                                                echo '(Just now)';
                                                            } elseif ($time_diff < 3600) {
                                                                echo '(' . floor($time_diff / 60) . ' min ago)';
                                                            } elseif ($time_diff < 86400) {
                                                                echo '(' . floor($time_diff / 3600) . ' hrs ago)';
                                                            } elseif ($time_diff < 604800) {
                                                                echo '(' . floor($time_diff / 86400) . ' days ago)';
                                                            }
                                                            ?>
                                                        </span>
                                                    </small>
                                                    <?php if (!empty($history['duration'])): ?>
                                                        <span class="ml-2">
                                                            <span class="badge badge-info badge-sm">
                                                                <i class="fas fa-clock"></i> <?= gmdate('H:i:s', $history['duration']) ?>
                                                            </span>
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="comment-text">
                                                    <?= nl2br(htmlspecialchars($history['remarks'])) ?>
                                                </div>
                                                <?php if (!empty($history['start_time']) && !empty($history['end_time'])): ?>
                                                    <div class="comment-time-info mt-2">
                                                        <small class="text-muted">
                                                            <i class="fas fa-clock"></i> Time Period:
                                                            <span class="badge badge-light ml-1">
                                                                <?= date('H:i', strtotime($history['start_time'])) ?> - <?= date('H:i', strtotime($history['end_time'])) ?>
                                                            </span>
                                                        </small>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center text-muted py-5">
                                    <div class="mb-3">
                                        <i class="fas fa-comments fa-4x" style="opacity: 0.3;"></i>
                                    </div>
                                    <h5 class="text-muted">No History Yet</h5>
                                    <p class="mb-0">This task doesn't have any comments or history entries yet.<br>
                                    Be the first to add a comment!</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions Card -->
        <div class="col-md-4">
            <div class="card card-secondary shadow-pro">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div class="card-body">
                    <?php if ($task['task_status'] !== 'completed'): ?>
                        <?php if (has_permission('tasks/edit')): ?>
                            <form action="<?= base_url('app/tasks/task_status/'.$task['id']) ?>" method="post" class="mb-3">
                                <div class="form-group">
                                    <label for="status">Change Status:</label>
                                    <select name="task_status" id="status" class="form-control" required>
                                        <option value="">Select Status</option>
                                        <option value="pending" <?= $task['task_status'] == 'pending' ? 'selected' : '' ?>>Pending</option>
                                        <option value="assigned" <?= $task['task_status'] == 'assigned' ? 'selected' : '' ?>>Assigned</option>
                                        <option value="testing" <?= $task['task_status'] == 'testing' ? 'selected' : '' ?>>Testing</option>
                                        <option value="completed" <?= $task['task_status'] == 'completed' ? 'selected' : '' ?>>Completed</option>
                                        <option value="on_hold" <?= $task['task_status'] == 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                                    </select>
                                </div>
                                
                                <!-- Assigned To dropdown - shown only when status is "assigned" -->
                                <div class="form-group" id="assigned_to_group" style="display: <?= $task['task_status'] == 'assigned' ? 'block' : 'none' ?>;">
                                    <label for="assigned_to">Assign To:</label>
                                    <select name="user_id" id="assigned_to" class="form-control select2" style="width: 100%;">
                                        <option value="">Select User</option>
                                        <?php if (isset($users)): ?>
                                            <?php foreach ($users as $user_id => $user_name): ?>
                                                <option value="<?= $user_id ?>" <?= $task['user_id'] == $user_id ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($user_name) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> 
                                        When changing status to "Assigned", you must select a user to assign the task to.
                                    </small>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-sm btn-block">
                                    <i class="fas fa-sync"></i> Update Status
                                </button>
                            </form>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <div class="task-timestamps-modern">
                        <div class="timestamp-card">
                            <div class="timestamp-header">
                                <i class="bi bi-plus-circle text-success"></i>
                                <span class="timestamp-label">Created</span>
                            </div>
                            <div class="timestamp-content">
                                <div class="timestamp-date"><?= date('M d, Y', strtotime($task['created_on'])) ?></div>
                                <div class="timestamp-time"><?= date('g:i A', strtotime($task['created_on'])) ?></div>
                                <div class="timestamp-user">
                                    <i class="bi bi-person-circle"></i>
                                    <?= isset($users[$task['created_by']]) ? $users[$task['created_by']] : 'System' ?>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($task['updated_on'] && $task['updated_on'] != $task['created_on']): ?>
                            <div class="timestamp-card">
                                <div class="timestamp-header">
                                    <i class="bi bi-arrow-clockwise text-info"></i>
                                    <span class="timestamp-label">Updated</span>
                                </div>
                                <div class="timestamp-content">
                                    <div class="timestamp-date"><?= date('M d, Y', strtotime($task['updated_on'])) ?></div>
                                    <div class="timestamp-time"><?= date('g:i A', strtotime($task['updated_on'])) ?></div>
                                    <div class="timestamp-user">
                                        <i class="bi bi-person-circle"></i>
                                        <?= isset($users[$task['updated_by']]) ? $users[$task['updated_by']] : 'System' ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Task Info Card -->
            <div class="card card-info shadow-pro">
                <div class="card-header">
                    <h3 class="card-title">Task Information</h3>
                </div>
                <div class="card-body">
                    <div class="info-box">
                        <span class="info-box-icon bg-info"><i class="fas fa-tasks"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Task ID</span>
                            <span class="info-box-number">#<?= str_pad($task['id'], 6, '0', STR_PAD_LEFT) ?></span>
                        </div>
                    </div>
                    
                    <div class="info-box">
                        <span class="info-box-icon bg-success"><i class="fas fa-clock"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Total Time Logged</span>
                            <span class="info-box-number">
                                <?php
                                $total_seconds = 0;
                                if (!empty($task_history)) {
                                    foreach ($task_history as $history) {
                                        if (!empty($history['duration'])) {
                                            $total_seconds += $history['duration'] * 60;
                                        }
                                    }
                                }
                                echo gmdate('H:i', $total_seconds);
                                ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge-lg {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.comment-item:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

.comment-avatar {
    flex-shrink: 0;
}

.user-avatar {
    transition: transform 0.2s ease;
}

.user-avatar:hover {
    transform: scale(1.05);
}

.user-avatar-fallback {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    transition: transform 0.2s ease;
}

.user-avatar-fallback:hover {
    transform: scale(1.05);
}

.comment-content {
    min-height: 40px;
}

.comment-text {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-radius: 10px;
    border-left: 3px solid #007bff;
}

#add_comment_form {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

#new_status:disabled {
    background-color: #e9ecef;
    opacity: 0.6;
}

.custom-control-label {
    cursor: pointer;
}

.status-update-section {
    background-color: rgba(0, 123, 255, 0.05);
    border: 1px dashed #007bff;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
}

#update_status_checkbox:checked + .custom-control-label {
    color: #007bff;
    font-weight: bold;
}

.badge-sm {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
}

.comment-header .badge {
    vertical-align: middle;
}

.comment-header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}

.timeline-connector {
    position: absolute;
    left: 22px;
    top: 55px;
    bottom: -15px;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #e9ecef);
    z-index: 1;
}

.timeline-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 20px !important;
}

.comment-item:last-child .timeline-connector {
    display: none;
}

/* Assigned To Dropdown Styling */
#assigned_to_group, #comment_assigned_to_group {
    background-color: rgba(255, 193, 7, 0.05);
    border: 1px dashed #ffc107;
    border-radius: 5px;
    padding: 15px;
    margin-top: 10px;
    transition: all 0.3s ease;
}

#assigned_to_group.show, #comment_assigned_to_group.show {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: #ffc107;
}

#assigned_to_group label, #comment_assigned_to_group label {
    color: #856404;
    font-weight: 600;
}

#assigned_to_group small, #comment_assigned_to_group small {
    color: #856404;
}

#assigned_to_group select, #comment_assigned_to_group select {
    border-color: #ffc107;
}

#assigned_to_group select:focus, #comment_assigned_to_group select:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* Task Title Background */
.task-title-bg {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px 20px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 20px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* Professional Task Details Styling */
.detail-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    transition: all 0.2s ease;
}

.detail-group:hover {
    background: #ffffff;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

/* Simple Detail Group - No card design */
.simple-detail-group {
    margin-bottom: 16px;
    transition: all 0.2s ease;
}

.simple-detail-group:hover {
    transform: translateX(2px);
}

.detail-label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.detail-label i {
    margin-right: 6px;
    width: 16px;
}

.detail-value {
    font-size: 14px;
    font-weight: 500;
    color: #212529;
    line-height: 1.4;
}

.description-content {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-top: 8px;
    line-height: 1.6;
    color: #495057;
}

.description-content p {
    margin-bottom: 12px;
}

.description-content p:last-child {
    margin-bottom: 0;
}

/* Badge styling improvements */
.badge-lg {
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.badge-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.badge-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.badge-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.badge-secondary {
    background: linear-gradient(135deg, #6c757d, #545b62);
    color: white;
}

.badge-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

/* Modern Timestamp Cards */
.task-timestamps-modern {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.timestamp-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.timestamp-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

.timestamp-card:last-child::before {
    background: linear-gradient(90deg, #17a2b8 0%, #6f42c1 100%);
}

.timestamp-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.timestamp-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.timestamp-header i {
    font-size: 16px;
}

.timestamp-label {
    font-weight: 600;
    color: #495057;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.timestamp-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.timestamp-date {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.timestamp-time {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.timestamp-user {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: #495057;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    margin-top: 4px;
    border: 1px solid #e9ecef;
}

.timestamp-user i {
    color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .timestamp-card {
        padding: 10px;
    }
    
    .timestamp-date {
        font-size: 13px;
    }
    
    .timestamp-time {
        font-size: 11px;
    }
    
    .timestamp-user {
        font-size: 10px;
    }
}

/* New CSS block for .task-info-compact and related classes */
.task-info-compact {
    background: #f8fafc;
    border-radius: 10px;
    padding: 18px 20px;
    margin-bottom: 24px;
    font-size: 15px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.03);
}
.info-row {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    margin-bottom: 8px;
}
.info-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
    font-weight: 600;
}
.info-value, .badge {
    font-weight: 500;
    color: #222;
    font-size: 15px;
}
.badge-status {
    background: #17a2b8;
    color: #fff;
    border-radius: 4px;
    padding: 2px 10px;
    font-size: 12px;
    margin-left: 4px;
}
.badge-priority {
    background: #dc3545;
    color: #fff;
    border-radius: 4px;
    padding: 2px 10px;
    font-size: 12px;
    margin-left: 4px;
}
@media (max-width: 600px) {
    .info-row {
        flex-direction: column;
        gap: 2px;
    }
}
</style>

<script>
$(document).ready(function() {
    // Initialize assigned to dropdown visibility based on current status
    var currentStatus = $('#status').val();
    if (currentStatus === 'assigned') {
        $('#assigned_to_group').show().addClass('show');
        $('#assigned_to').prop('required', true);
    }

    // Handle status change in Quick Actions form
    $('#status').on('change', function() {
        var selectedStatus = $(this).val();
        if (selectedStatus === 'assigned') {
            $('#assigned_to_group').show().addClass('show');
            $('#assigned_to').prop('required', true);
        } else {
            $('#assigned_to_group').hide().removeClass('show');
            $('#assigned_to').prop('required', false);
        }
    });

    // Handle status update checkbox
    $('#update_status_checkbox').on('change', function() {
        var isChecked = $(this).is(':checked');
        $('#new_status').prop('disabled', !isChecked);

        // Update button text based on checkbox state
        if (isChecked) {
            $('#submit_btn_text').text('Add Comment & Update Status');
            $('#submit_comment_btn').removeClass('btn-primary').addClass('btn-success');
            $('#new_status').focus();
            
            // Check if current status is already "assigned" and show dropdown
            var currentCommentStatus = $('#new_status').val();
            if (currentCommentStatus === 'assigned') {
                $('#comment_assigned_to_group').show().addClass('show');
                $('#comment_assigned_to').prop('required', true);
            }
        } else {
            $('#submit_btn_text').text('Add Comment');
            $('#submit_comment_btn').removeClass('btn-success').addClass('btn-primary');
            $('#new_status').val('<?= $task['task_status'] ?>');
            $('#comment_assigned_to_group').hide().removeClass('show');
            $('#comment_assigned_to').prop('required', false);
        }
    });

    // Handle status change in Comments form
    $('#new_status').on('change', function() {
        var selectedStatus = $(this).val();
        if (selectedStatus === 'assigned') {
            $('#comment_assigned_to_group').show().addClass('show');
            $('#comment_assigned_to').prop('required', true);
        } else {
            $('#comment_assigned_to_group').hide().removeClass('show');
            $('#comment_assigned_to').prop('required', false);
        }
    });

    // Handle comment form submission
    $('#add_comment_form').on('submit', function(e) {
        e.preventDefault();

        var formData = {
            remarks: $('#comment_text').val().trim()
        };

        // Check if status update is requested
        var updateStatus = $('#update_status_checkbox').is(':checked');
        if (updateStatus) {
            var newStatus = $('#new_status').val();
            if (!newStatus) {
                alert('Please select a new status or uncheck the status update option.');
                return;
            }

            // Check if status is actually changing
            if (newStatus === '<?= $task['task_status'] ?>') {
                // If status is not changing but it's "assigned", check for user reassignment
                if (newStatus === 'assigned') {
                    var assignedUserId = $('#comment_assigned_to').val();
                    if (!assignedUserId) {
                        alert('Please select a user to assign the task to.');
                        return;
                    }
                    // Check if user is actually changing
                    if (assignedUserId === '<?= $task['user_id'] ?>') {
                        alert('The selected user is the same as the current assigned user. Please select a different user or uncheck the status update option.');
                        return;
                    }
                    formData.user_id = assignedUserId;
                } else {
                    alert('The selected status is the same as the current status. Please select a different status or uncheck the status update option.');
                    return;
                }
            } else {
                // If status is "assigned", check if user is selected
                if (newStatus === 'assigned') {
                    var assignedUserId = $('#comment_assigned_to').val();
                    if (!assignedUserId) {
                        alert('Please select a user to assign the task to when changing status to "Assigned".');
                        return;
                    }
                    formData.user_id = assignedUserId;
                }

                formData.update_status = true;
                formData.new_status = newStatus;
            }
        }

        if (!formData.remarks) {
            alert('Please enter a comment before submitting.');
            return;
        }

        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();

        // Update button text based on action
        var loadingText = updateStatus ?
            '<i class="fas fa-spinner fa-spin"></i> Adding Comment & Updating Status...' :
            '<i class="fas fa-spinner fa-spin"></i> Adding Comment...';

        // Disable submit button and show loading
        submitBtn.prop('disabled', true).html(loadingText);

        $.ajax({
            url: '<?= base_url("app/tasks/add_comment/".$task['id']) ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // Clear the form
                    $('#comment_text').val('');
                    $('#update_status_checkbox').prop('checked', false);
                    $('#new_status').prop('disabled', true).val('<?= $task['task_status'] ?>');
                    $('#comment_assigned_to_group').hide().removeClass('show');
                    $('#comment_assigned_to').prop('required', false);

                    // Reload the page to show the new comment and updated status
                    location.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('An error occurred while processing your request. Please try again.');
                console.error('AJAX Error:', error);
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Handle form reset
    $('#reset_form_btn').on('click', function() {
        $('#comment_text').val('');
        $('#update_status_checkbox').prop('checked', false);
        $('#new_status').prop('disabled', true).val('<?= $task['task_status'] ?>');
        $('#comment_assigned_to_group').hide().removeClass('show');
        $('#comment_assigned_to').prop('required', false);
        $('#submit_btn_text').text('Add Comment');
        $('#submit_comment_btn').removeClass('btn-success').addClass('btn-primary');

        // Reset textarea height
        $('#comment_text').css('height', 'auto');
    });

    // Auto-resize textarea
    $('#comment_text').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>