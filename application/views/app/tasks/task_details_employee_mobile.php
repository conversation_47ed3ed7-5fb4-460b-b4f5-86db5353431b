<?php
$item_id = $param1;
$task_details = $this->tasks_m->get(['id' => $item_id])->row_array();
$task_history = $this->work_history_m->get_task_history($item_id);
$project_title = $this->projects_m->get(['id' => $task_details['project_id']], ['id', 'title'])->row()->title;
$users = array_column($this->users_m->get(null, ['id', 'name'])->result_array(), 'name', 'id');
?>
<div class="p-1" style="margin-top: -35px!important;">
    <div class="p-1 bg-white">
        <div class="p-2 pb-0 pt-1 row">
            <div class="col-4">
                <button onclick="window.history.back();" class="btn btn-secondary w-100">
                    <i class="bi bi-arrow-left-circle"></i> Go Back
                </button>
            </div>
            <div class="col-4">
                <a href="#form_update_task" class="btn btn-outline-success btn-sm float-right mr-2">Update Task</a>
            </div>
            <div class="col-4">
                <div class="mb-1 job_id_dashboard" style="font-size: 17px!important;width: 100px;margin-top: 3px">
                    TRG-<b><?=$item_id?></b>
                </div>
            </div>
            
            <div class="col-4 d-none">
                <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$task_details['id'].'/?page_name=tasks/edit'); ?>', '<?php echo get_phrase('update_task'); ?>')"
                        class="btn btn-outline-info w-100">Edit Task</button>
                        <a href="#form_update_task" class="btn btn-outline-success btn-sm float-right mr-2">Update Task</a>
            </div>
            <div class="col-3 d-none">
                <?php
                if (get_user_id() == $task_details['created_by'] && get_user_id() == $task_details['user_id']){
                    ?>
                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$task_details['id'].'/?page_name=tasks/edit'); ?>', '<?php echo get_phrase('update_task'); ?>')"
                            class="btn btn-outline-danger d-none btn-sm float-right">Delete</button>
                    <?php
                }
                ?>
            </div>
        </div>
        <div class="shadow-pro">
            <div class="row p-2">
                <div class="col-5">
                    <?=get_project_title($project_title)?>
                </div>
                <div class="col-3 text-center">
                    <?= get_task_priority($task_details['task_priority'])?>
                </div>
                <div class="col-4" style="text-align: right">
                    <?= get_due_date($task_details['due_date'])?>
                </div>
            </div>
            <div class="p-2">
                <h5 style="background-color: #f4f4fa; font-size: 18px!important;font-weight: 600" class="p-2 m-0">
                    <?=$task_details['title']?>
                </h5>
                <div style="background-color:#efefef5c;font-size:16px;color:#393939!important; line-height:1.6em;padding:10px;margin-top:10px;">
                    <?=$task_details['description']?>
                </div>
            </div>
            <div class="pb-1">
                <div class="p-2 text-muted mb-2" style="background-color: #f3f2f2; font-size: 15px;font-weight: bold">
                    TASK TIMELINE
                </div>
                <div class="timeline p-1">
                    <?php
                    if (count($task_history)){
                        foreach($task_history as $history){
                            if (!empty($history['remarks'])){
                                if($history['user_id']!=get_user_id()){
                                    $border = 'border:1px solid #ed6686';
                                }else{
                                    $border = '';
                                }
                                ?>
                                <?php
                                $job_date = DateTime::createFromFormat('Y-m-d H:i:s', $history['created_at'])->format('d-m-Y');
                                $start_time = '';
                                $end_time = '';
                                $duration_text = '';
                                
                                if (!empty($history['start_time']) && !empty($history['end_time'])){
                                    $start_time = DateTime::createFromFormat('Y-m-d H:i:s', $history['start_time'])->format('g:i A');
                                    $end_time = DateTime::createFromFormat('Y-m-d H:i:s', $history['end_time'])->format('g:i A');
                                }
                                
                                if (!empty($history['duration'])) {
                                    $duration_text = get_time_from_seconds($history['duration'] * 60); // Convert minutes to seconds
                                }
                                ?>
                                <div class="time-label">
                                    <span class="" style="font-size: 13px; font-weight: normal; background-color: #e1f6eb;padding: 2px 3px">
                                        <span style="color: #062810">
                                            <b><?=$job_date?></b>: <?=$start_time.' to '.$end_time?>
                                        </span>
                                    </span>
                                </div>

                                <div>
                                    <i class="fas fa-comments bg-primary"></i>
                                    <div class="timeline-item shadow-pro">
                                        <span class="time" style="font-size: 14px"><i class="fas fa-clock"></i> <?=$duration_text?></span>
                                        <h3 class="timeline-header text-muted" style="font-size: 14px">
                                            <a href="#"><?=strtoupper($history['user_name']);?></a><br>
                                            updated on <small style="color: #0f91af"><?= DateTime::createFromFormat('Y-m-d H:i:s', $history['created_at'])->format('d-m-Y g:i A')?></small>

                                        </h3>
                                        <div class="timeline-body p-2" style="font-size: 16px">
                                            <?=$history['remarks']?>
                                        </div>
                                        <div class="timeline-footer d-none">
                                            <button class="btn btn-outline-primary btn-sm" style="padding: 3px 10px;font-size: 12px; width: 90px">Edit</button>
                                            <button class="btn btn-outline-danger btn-sm" style="padding: 3px 10px;font-size: 12px; width: 90px">Delete</button>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                        }
                    }
                    ?>
                    <div>
                        <i class="fas fa-clock bg-primary"></i>
                    </div>
                </div>
            </div>

        </div>

        <?php
        if ($task_details['task_status'] == 'assigned' && $task_details['user_id'] == get_user_id()){
            ?>
            <div class="p-0 pt-4">

                <form class="form-horizontal" id="form_update_task" action="<?=base_url('app/tasks/employee_update_status/'.$task_details['id'].'/')?>" method="post" enctype="multipart/form-data"
                      style="background-color: rgba(248,246,246,0.51); border-radius: 5px;border: 2px solid rgba(72,190,108,0.2)">
                    <div class="p-1" style="background-color: rgba(72,190,108,0.2); color: #0f6629; font-size: 15px!important;">
                        <b>UPDATE TASK STATUS</b>
                        <div class="mb-1 job_id_dashboard" style="font-size: 15px!important;width: 95px;margin-right: -5px;margin-top: -5px;">
                            TRG-<b><?=$item_id?></b>
                        </div>
                    </div>
                    <div class="row pt-1" style="margin: 0!important;">
                        <div class="p-2 pb-3 col-12">
                            <div class="bg-danger-lighten p-2 text-center text-danger" style="font-size: 16px">
                                Job remarks should clearly detail the <b>tasks accomplished today</b>, providing a comprehensive understanding of the work completed.
                            </div>
                        </div>
                        <div class="form-group col-12 p-0">
                            <label for="task_status" class="col-sm-12 col-form-label text-muted"><b>Task Status</b> <span class="text-danger">*</span></label>
                            <div class="col-sm-12">
                                <select class="form-control" id="task_status" name="task_status" required>
                                    <option value="">Choose Status</option>
                                    <option value="assigned" <?=$task_details['task_status'] == 'assigned' ? 'assigned' : ''?>>Pending/ In Progress</option>
                                    <option value="testing"  <?=$task_details['task_status'] == 'testing' ? 'testing' : ''?>>Completed</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group col-12 p-0">
                            <label for="job_date" class="col-sm-12 col-form-label text-muted"><b>Job Date</b> <span class="text-danger">*</span></label>
                            <div class="col-sm-12">
                                <input type="date" class="form-control " id="job_date" name="job_date" value="<?=date('Y-m-d')?>" required readonly>
                            </div>
                        </div>
                        <div class="form-group col-4">
                            <label for="start_time" class="col-sm-12 col-form-label text-muted"><b>Start Time</b> <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" name="start_time" id="start_time" onchange="get_time_taken()" required>
                        </div>
                        <div class="form-group col-4">
                            <label for="end_time" class="col-sm-12 col-form-label text-muted"><b>End Time</b> <span class="text-danger">*</span></label>
                            <input type="time" class="form-control" name="end_time" id="end_time" onchange="get_time_taken()" required>
                        </div>
                        <div class="form-group col-4">
                            <label for="time_taken" class="col-sm-12 col-form-label text-muted"><b>Time Taken</b> <span class="text-danger">*</span></label>
                            <div class="col-sm-12">
                                <input type="text" class="form-control " id="time_taken" name="time_taken" onchange="duration_input(this)" pattern="^(0[0-9]|1[0-9]|2[0-3]):([0-5][0-9])$" value="00:00" placeholder="00:00" required readonly>
                            </div>
                        </div>
                        <div class="form-group col-12 p-0">
                            <label for="remarks" class="col-sm-12 col-form-label text-muted"><strong>Describe Your Work</strong> <span class="text-danger">*</span></label>
                            <div class="p-2 pt-0">
                                <div class="d-none text-center text-danger p-1" style="border-radius: 5px;background-color: #f5f5f2">
                                    <!--Please provide detailed task information, including what you have done. Detailed updations helps better project flow and completion.-->
                                    Kindly ensure that work statuses are updated regularly. 
                                    Provide clear remarks on both completed and pending tasks, and make sure the timesheets are fully updated—not partially.<br>
                                    This is essential for tracking our project progress and ensuring smooth, on-time project delivery.
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <textarea class="form-control ck_editor" id="remarks" name="remarks" placeholder="Enter Remarks"></textarea>
                            </div>
                        </div>

                    </div>
                    <div class="col-12 pb-2" >
                        <button type="submit" name="add" value="Save" class="btn btn-success btn-mini w-100">
                            <small><i class="fa fa-check"></i></small> Update
                        </button>
                    </div>


                </form>
            </div>
            <?php
        }
        ?>


        <script type="text/javascript">
            

            $('.select2').select2();
        </script>

        <script type="text/javascript">
            document.querySelector('.form-horizontal').addEventListener('submit', function(e) {
                var timeTaken = document.getElementById('time_taken').value;
                // var remarks = CKEDITOR.instances.remarks.getData();
                var remarks = tinymce.get('remarks').getContent();

                // Check if Time Taken is "00:00"
                if (timeTaken === "00:00") {
                    message_error("Time Taken cannot be 00:00");
                    e.preventDefault(); // Prevent form submission
                }

                // Check if Remarks is at least 20 characters
                if (remarks.trim().length < 25 || !check_content_words(remarks)) {
                    message_error("Please provide detailed task information!");
                    e.preventDefault(); // Prevent form submission
                }

            });
            
            // check content words
            function check_content_words(str) {
                let tempDiv = document.createElement('div');
                tempDiv.innerHTML = str;
                let text = tempDiv.textContent || tempDiv.innerText || "";
            
                const words = text.match(/[a-zA-Z]+/g);
                return words && words.length >= 4;
            }

            function get_time_taken(){
                var start_time = $('#start_time').val();
                var end_time = $('#end_time').val();

                if (start_time === '' || end_time === '') {
                    $('#time_taken').val('00:00');
                    return;
                }

                // Convert time to minutes
                var startTimeMinutes = convertTimeToMinutes(start_time);
                var endTimeMinutes = convertTimeToMinutes(end_time);

                // Ensure start time is less than end time
                if (startTimeMinutes >= endTimeMinutes) {
                    $('#time_taken').val('00:00');
                    return;
                }

                // Calculate duration
                var diffMinutes = endTimeMinutes - startTimeMinutes;

                if (!(diffMinutes > 0)){
                    $('#time_taken').val('00:00');
                    return;
                }

                // Format duration
                var duration = formatDuration(diffMinutes);

                $('#time_taken').val(duration);
            }
            function convertTimeToMinutes(time) {
                var [hours, minutes] = time.split(':').map(num => parseInt(num, 10));
                return hours * 60 + minutes;
            }

            function formatDuration(minutes) {
                var hours = Math.floor(minutes / 60);
                var mins = minutes % 60;
                // Leading zero for minutes
                hours = hours < 10 ? '0' + hours : hours;
                mins = mins < 10 ? '0' + mins : mins;
                return `${hours}:${mins}`;
            }

            function setupDateInput() {
                const dateInput = document.getElementById('job_date');
                dateInput.min = '<?=date('Y-m-d')?>';
                dateInput.max = '<?=date('Y-m-d')?>';
                dateInput.value = '<?=date('Y-m-d')?>';  // Default to today
            }
            window.onload = setupDateInput;
        </script>
    </div>
</div>

<style>
    label {
        font-size: 12px!important;
        color: #5555 !important;
        text-transform: uppercase!important;
        margin-bottom: -5px!important;
    }
    input, textarea, select{
        font-size: 15px!important;
        font-weight: 600!important;
    }
</style>