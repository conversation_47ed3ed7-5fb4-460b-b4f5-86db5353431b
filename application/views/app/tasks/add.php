<?php
    $projects = $this->projects_m->get(null, ['id', 'title', 'project_type'])->result_array();
    $project_types = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
    $assign_users = $this->users_m->get_task_assign_users();
    
    // Get project_id from URL parameter if available
    $selected_project_id = $this->input->get('project_id');
?>
<style>
    .task-title-section {
        background: #ffffff;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 1.75rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        position: relative;
        transition: all 0.3s ease;
    }
    
    .task-title-section:hover {
        border-color: #007bff;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    }
    
    .task-title-section label {
        font-size: 0.85rem !important;
        font-weight: 500 !important;
        color: #6c757d !important;
        text-transform: none !important;
        margin-bottom: 0.375rem !important;
        position: relative;
        z-index: 2;
    }
    
    .task-title-section textarea {
        font-size: 1.3rem !important;
        font-weight: 500 !important;
        padding: 1rem !important;
        border: 1px solid #ced4da !important;
        border-radius: 8px !important;
        transition: all 0.3s ease !important;
        background: #ffffff !important;
        color: #2c3e50 !important;
        resize: vertical !important;
        min-height: 70px !important;
        position: relative;
        z-index: 2;
        line-height: 1.4 !important;
    }
    
    .task-title-section textarea::placeholder {
        color: #6c757d !important;
        font-weight: 400 !important;
        font-size: 1.1rem !important;
    }
    
    .task-description-section {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 0.75rem;
        margin-bottom: 1rem;
        opacity: 0.85;
        transition: all 0.2s ease;
    }
    
    .task-description-section:hover {
        opacity: 1;
        border-color: #dee2e6;
    }
    
    .task-description-section label {
        font-size: 0.85rem !important;
        font-weight: 500 !important;
        color: #6c757d !important;
        text-transform: none !important;
        margin-bottom: 0.375rem !important;
    }
    
    .task-description-section textarea {
        font-size: 0.9rem !important;
        font-weight: 400 !important;
        padding: 0.5rem !important;
        border: 1px solid #ced4da !important;
        border-radius: 4px !important;
        resize: vertical !important;
        min-height: 70px !important;
        transition: border-color 0.2s ease !important;
        background: white !important;
    }
    
    .task-description-section textarea:focus {
        border-color: #adb5bd !important;
        box-shadow: 0 0 0 0.1rem rgba(108, 117, 125, 0.1) !important;
    }
    
    .task-description-section textarea::placeholder {
        color: #adb5bd !important;
        font-style: italic !important;
    }
</style>

<form class="form-horizontal" action="<?=base_url('app/tasks/add/')?>" method="post" enctype="multipart/form-data">
	<div class="row" style="margin: 0!important;">
		<!-- Enhanced Multi-line Title Section -->
		<div class="form-group col-12 p-0 task-title-section">
			<label for="title" class="col-sm-12 col-form-label">
				<i class="fas fa-edit mr-1"></i>Task Title <span class="text-danger">*</span>
			</label>
			<div class="col-sm-12">
				<textarea class="form-control" id="title" name="title" placeholder="Enter a comprehensive task title that clearly describes the work to be done..." required></textarea>
			</div>
		</div>
        
        <!-- Minimal Description Section -->
        <div class="form-group col-12 p-0 task-description-section">
            <label for="description" class="col-sm-12 col-form-label">
                <i class="fas fa-info-circle mr-1"></i>Additional Notes (Optional)
            </label>
            <div class="col-sm-12">
                <textarea class="form-control" id="description" name="description" placeholder="Any additional details, context, or notes about this task..."></textarea>
            </div>
        </div>
        
        <div class="form-group col-6 p-0">
			<label for="project_id" class="col-sm-12 col-form-label text-muted">Project <span class="text-danger">*</span></label>
			<div class="col-sm-12">
                <select class="form-control select2" id="project_id" name="project_id" required>
                    <option value="">Choose Project</option>
                    <?php
                        foreach ($projects as $key => $project) {
                            $selected = ($selected_project_id == $project['id']) ? 'selected' : '';
                            echo "<option value='{$project['id']}' {$selected}>{$project['title']} - [{$project_types[$project['project_type']]}]</option>";
                        }
                    ?>
                </select>
			</div>
		</div>
        <div class="form-group col-6 p-0">
            <label for="assign_user_id" class="col-sm-12 col-form-label text-muted">Choose User <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control form-control-sm select2" id="assign_user_id" name="assign_user_id" required>
                    <option value="">Choose User</option>
                    <?php
                    if (has_permission('tasks/index')){
                        echo "<option value=\"0\" selected>UN ASSIGNED</option>";
                    }
                    ?>
                    <?php
                    foreach ($assign_users as $assign_user) {
                        if (!has_permission('tasks/index')){
                            $selected = get_user_id() == $assign_user['id'] ? 'selected' : '';
                        }

                        echo "<option value='{$assign_user['id']}' {$selected}>{$assign_user['name']}</option>";
                    }
                    ?>
                </select>
            </div>
        </div>
        <div class="form-group col-6 p-0">
            <label for="task_type" class="col-sm-12 col-form-label text-muted">Task Type <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select class="form-control select2" id="task_type" name="task_type" required>
                    <option value="">Choose Type</option>
                    <option value="new">New/ Requirement</option>
                    <option value="bug">Bug</option>
                    <option value="critical_bug">Critical Bug</option>
                    <option value="queries">Queries</option>
                    <option value="meeting">Meeting</option>
                </select>
            </div>
        </div>
        
        <div class="form-group col-6 p-0">
			<label for="task_priority" class="col-sm-12 col-form-label text-muted">Task Priority <span class="text-danger">*</span></label>
			<div class="col-sm-12">
                <select class="form-control select2" id="task_priority" name="task_priority" required>
                    <option value="">Choose Priority</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                </select>
			</div>
		</div>

        <div class="col-12">
            <hr>
        </div>
        <div class="form-group col-6 p-0">
            <label for="due_date" class="col-sm-12 col-form-label text-muted">Due Date <span class="text-danger">*</span> </label>
            <div class="col-sm-12">
                <input type="date" class="form-control" id="due_date" name="due_date" value="<?=date('Y-m-d')?>" placeholder="Due Date" required>
            </div>
        </div>
        <div class="form-group col-6 p-0">
            <label for="required_time" class="col-sm-12 col-form-label text-muted">Required Time</label>
            <div class="col-sm-12">
                <div class="input-group">
                    <input type="number" class="form-control" id="required_hours" name="required_hours" min="0" max="999" placeholder="Hours">
                    <input type="number" class="form-control" id="required_minutes" name="required_minutes" min="0" max="59" placeholder="Minutes">
                    <div class="input-group-append">
                        <span class="input-group-text">H:M</span>
                    </div>
                </div>
                <small class="form-text text-muted">Enter expected time to complete this task</small>
                <input type="hidden" id="required_time" name="required_time" value="">
            </div>
        </div>
        
        <?php
            if(is_tester()){
                ?>
                <div class="form-group col-6 p-0">
                    <div class="col-sm-12">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="mandatory_check" name="mandatory_check" style="width:20px;height:20px" required>
                            <label class="form-check-label text-muted ps-2" for="mandatory_check">
                                <span class="text-danger">*</span> Is this task updated in testers WhatsApp group?
                            </label>
                        </div>
                    </div>
                </div>
                <?php
            }
        ?>
	</div>
    <?php
    if (!has_permission('tasks/index')){
        ?>
        <div class="col-12">
            <div class="alert bg-danger-lighten">
                Once task added, only admin can delete or edit the task.
            </div>
        </div>
        <?php
    }
    ?>

	<div class="col-12 " >
		<button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
			<small><i class="fa fa-check"></i></small> Save
		</button>
	</div>
</form>


<script type="text/javascript">
	$('.select2').select2();
</script>
<script type="text/javascript">
    // Time conversion functionality
    function updateRequiredTime() {
        const hours = parseInt($('#required_hours').val()) || 0;
        const minutes = parseInt($('#required_minutes').val()) || 0;
        const totalMinutes = (hours * 60) + minutes;
        $('#required_time').val(totalMinutes > 0 ? totalMinutes : '');
    }

    // Update required time when hours or minutes change
    $('#required_hours, #required_minutes').on('input change', updateRequiredTime);

    document.querySelector('.form-horizontal').addEventListener('submit', function(e) {
        var description = $('#description').val();
        var title = $('#title').val();

        if (title.trim().length < 20) {
            message_error("Task Title must be at least 20 characters long");
            e.preventDefault(); // Prevent form submission
        }

        // Description is now optional, so no minimum length requirement
    });
</script>