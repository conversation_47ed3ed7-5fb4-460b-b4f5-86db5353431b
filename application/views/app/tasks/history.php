<?php
$item_id = $param1;
$task_details = $this->tasks_m->get(['id' => $item_id])->row_array();
$task_history = $this->work_history_m->get_task_history($item_id);
$users = array_column($this->users_m->get(['role_id!=' => 1], ['id', 'name'])->result_array(), 'name', 'id');
?>

<div class="p-1">
    <div class="shadow-pro">
        <table class="table table-bordered table-striped">
            <thead>
                <th>Name</th>
                <th>Remarks</th>
                <th>Duration</th>
                <th>Date</th>
            </thead>
            <tbody>
                <?php
                    foreach ($task_history as $history) {
                        $duration_text = '';
                        if (!empty($history['duration'])) {
                            $duration_text = get_time_from_seconds($history['duration'] * 60); // Convert minutes to seconds
                        }
                        ?>
                        <tr>
                            <td>
                                <?=$history['user_name']?><br>
                                <small>
                                    <?= DateTime::createFromFormat('Y-m-d H:i:s', $history['created_at'])->format('d-m-Y, g:i A')?>
                                </small>
                            </td>
                            <td><?=$history['remarks']?></td>
                            <td><?=$duration_text?></td>
                            <td><?= DateTime::createFromFormat('Y-m-d H:i:s', $history['created_at'])->format('d-m-Y')?></td>
                        </tr>
                        <?php
                    }
                    $total_duration = array_sum(array_column($task_history, 'duration')) * 60; // Convert to seconds
                ?>
            <tr>
                <th colspan="2">Total</th>
                <th><?=get_time_from_seconds($total_duration)?></th>
                <th>-</th>
            </tr>
            </tbody>
        </table>
    </div>
</div>
