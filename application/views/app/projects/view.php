<style>
    /* Modern Header Styles */
    .project-header-section {
        position: relative;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        margin-bottom: 2rem;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .header-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    }

    .header-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.1);
    }

    .header-content {
        position: relative;
        z-index: 2;
        padding: 1.5rem 2.5rem;
        color: white;
    }

    /* Breadcrumb Styles */
    .breadcrumb-nav {
        margin-bottom: 2rem;
    }

    .breadcrumb {
        background: transparent;
        padding: 0;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .breadcrumb-item {
        display: flex;
        align-items: center;
    }

    .breadcrumb-item:not(:last-child)::after {
        content: '/';
        margin-left: 0.5rem;
        opacity: 0.6;
        font-weight: 300;
    }

    .breadcrumb-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
    }

    .breadcrumb-link:hover {
        color: white;
        background: rgba(255,255,255,0.2);
        transform: translateY(-1px);
    }

    .breadcrumb-current {
        color: white;
        font-weight: 600;
        opacity: 0.9;
    }

    /* Header Top Row */
    .header-top-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .breadcrumb-nav {
        margin-bottom: 0;
    }

    /* Action Buttons */
    .project-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        flex-shrink: 0;
    }

    .btn-action {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        border: 2px solid transparent;
    }

    .btn-outline-light {
        background: rgba(255,255,255,0.1);
        border-color: rgba(255,255,255,0.3);
        color: white;
    }

    .btn-outline-light:hover {
        background: rgba(255,255,255,0.2);
        border-color: rgba(255,255,255,0.5);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .btn-primary {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        box-shadow: 0 4px 15px rgba(0,123,255,0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,123,255,0.4);
    }

    /* Project Main Info */
    .project-main-info {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1.5rem;
        gap: 2rem;
    }

    .project-title-section {
        flex: 1;
    }

    .project-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 1rem 0;
        line-height: 1.2;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .project-meta {
        display: flex;
        gap: 2rem;
        flex-wrap: wrap;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.95rem;
        opacity: 0.9;
    }

    .meta-item i {
        font-size: 1.1rem;
        opacity: 0.8;
    }

    .project-status-section {
        flex-shrink: 0;
    }

    .status-badge {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.95rem;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .status-delivered {
        background: rgba(40, 167, 69, 0.9);
        color: white;
    }

    .status-not-delivered {
        background: rgba(220, 53, 69, 0.9);
        color: white;
    }

    /* Project Stats Section */
    .project-stats-section {
        margin-bottom: 2rem;
    }

    .stats-card {
        background: white;
        border-radius: 12px;
        padding: 1.25rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        border: 1px solid rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    }

    .stats-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .stats-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.1rem;
        color: white;
        flex-shrink: 0;
    }

    .tasks-card .stats-icon {
        background: linear-gradient(45deg, #28a745, #20c997);
    }

    .tickets-card .stats-icon {
        background: linear-gradient(45deg, #17a2b8, #6f42c1);
    }

    .team-card .stats-icon {
        background: linear-gradient(45deg, #fd7e14, #e83e8c);
    }

    .stats-title {
        flex: 1;
    }

    .stats-title h3 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
    }

    .stats-action {
        flex-shrink: 0;
    }

    .stats-action .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-weight: 500;
    }

    /* Stats Content */
    .stats-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    /* Wide Progress Bar */
    .progress-bar-wide {
        width: 100%;
        height: 12px;
        background-color: #e9ecef;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(45deg, #28a745, #20c997);
        border-radius: 6px;
        transition: width 0.6s ease;
        box-shadow: 0 1px 3px rgba(40, 167, 69, 0.3);
    }

    .progress-fill.tickets {
        background: linear-gradient(45deg, #17a2b8, #6f42c1);
        box-shadow: 0 1px 3px rgba(23, 162, 184, 0.3);
    }

    /* Stats Numbers */
    .stats-numbers {
        display: flex;
        justify-content: space-between;
        gap: 0.5rem;
    }

    .stats-numbers .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        flex: 1;
    }

    .stats-numbers .stat-number {
        font-size: 1.25rem;
        font-weight: 800;
        color: white;
        line-height: 1;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        min-width: 2rem;
        text-align: center;
        display: inline-block;
    }

    /* Background colors for stat numbers */
    .stat-number.completed-bg {
        background: linear-gradient(45deg, #28a745, #20c997);
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }

    .stat-number.resolved-bg {
        background: linear-gradient(45deg, #28a745, #20c997);
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }

    .stat-number.in-progress-bg {
        background: linear-gradient(45deg, #ffc107, #fd7e14);
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
    }

    .stat-number.pending-bg {
        background: linear-gradient(45deg, #dc3545, #e83e8c);
        box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    }

    .stats-numbers .stat-label {
        font-size: 0.75rem;
        color: #6c757d;
        font-weight: 500;
        margin-top: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    /* Team Members */
    .team-members {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .team-member {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .team-member.compact {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .member-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background: linear-gradient(45deg, #007bff, #0056b3);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
        flex-shrink: 0;
    }

    .team-member.compact .member-avatar {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .member-avatar.project-lead {
        background: linear-gradient(45deg, #fd7e14, #e83e8c);
    }

    .member-info {
        flex: 1;
        min-width: 0;
    }

    .member-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
        margin-bottom: 0.125rem;
    }

    .team-member.compact .member-name {
        font-size: 0.8rem;
        margin-bottom: 0.0625rem;
    }

    .member-role {
        font-size: 0.8rem;
        color: #6c757d;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .team-member.compact .member-role {
        font-size: 0.7rem;
    }

    /* Professional Task Cards */
    .tasks-list {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        padding: 1rem;
    }

    .task-card-row {
        display: flex;
        align-items: stretch;
        gap: 1rem;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        transition: all 0.3s ease;
        cursor: pointer;
        min-height: 120px;
    }

    .task-card-row:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-color: #007bff;
    }

    .task-status-indicator {
        flex-shrink: 0;
        width: 100px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 0.5rem;
        border-radius: 8px;
        justify-content: center;
    }

    .task-status-indicator.pending {
        background: rgba(215, 72, 86, 0.15);
        border: 1px solid #dc3545;
    }

    .task-status-indicator.assigned {
        background: rgba(220, 53, 69, 0.1);
    }

    .task-status-indicator.completed {
        background: rgba(40, 167, 69, 0.1);
    }

    .task-status-indicator i {
        font-size: 1.4rem;
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .task-status-indicator.assigned i {
        color: #dc3545;
    }

    .task-status-indicator.pending i {
        color: #dc3545;
    }

    .task-status-indicator.completed i {
        color: #28a745;
    }

    .status-label {
        font-size: 0.7rem;
        font-weight: 600;
        text-align: center;
        color: #495057;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        line-height: 1.2;
    }

    .task-id-below {
        display: flex;
        justify-content: center;
    }

    .task-id-below .task-id-badge {
        background: linear-gradient(45deg, #e3f2fd, #bbdefb);
        color: #1976d2;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-size: 0.85rem;
        font-weight: 700;
        white-space: nowrap;
        box-shadow: 0 1px 3px rgba(25, 118, 210, 0.1);
        border: 1px solid #90caf9;
    }

    .task-content {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .task-header {
        margin-bottom: 0.5rem;
    }

    .task-title h6 {
        font-size: 1rem;
        font-weight: 600;
        color: #2c3e50;
        line-height: 1.4;
        margin: 0;
        flex: 1;
    }

    .task-details {
        margin-top: auto;
        background: #f8f9fa;
        padding: 0.5rem;
        border-radius: 8px;
        border-left: 3px solid #dee2e6;
    }

    .task-details-row {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        width: 100%;
    }

    .task-assigned {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.85rem;
        font-weight: 500;
        flex-shrink: 0;
        width: 32%;
    }

    .task-assigned i {
        color: #007bff;
        width: 14px;
        text-align: center;
        font-size: 0.8rem;
    }

    .assigned-person {
        color:rgb(59, 80, 102);
        font-weight: 600;
        font-size: 0.85rem;
    }

    .task-assigned-simple {
        display: flex;
        align-items: center;
        font-size: 0.85rem;
        font-weight: 500;
        flex-shrink: 0;
        padding: 0.25rem 0;
    }

    .task-assigned-simple .assigned-person {
        color: #6c757d;
        font-weight: 500;
        font-size: 0.85rem;
    }

    .priority-and-id {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        align-items: center;
        justify-content: center;
    }

    .task-id-inline {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .task-id-inline .task-id-badge {
        background: #f8f9fa;
        color: #6c757d;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        border: 1px solid #e9ecef;
    }

    .task-meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.75rem;
        color: #6c757d;
        text-align: right;
        width: 68%;
        justify-content: flex-start;
    }

    .task-meta-item i {
        width: 16px;
        text-align: center;
        font-size: 0.7rem;
    }

    .detail-label {
        font-weight: 500;
        color: #6c757d;
    }

    .detail-value {
        color: #2c3e50;
        font-weight: 500;
    }

    .task-actions {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
    }

    .priority-badge {
        margin-bottom: 0.25rem;
    }

    .priority-badge-inner {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.375rem;
        font-weight: 600;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .priority-badge-inner i {
        font-size: 0.7rem;
    }

    .priority-label {
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.375rem;
        align-items: center;
    }

    .action-buttons .btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 6px;
        white-space: nowrap;
        min-width: 65px;
        font-weight: 500;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .stats-card {
            padding: 1rem;
        }
        
        .stats-header {
            margin-bottom: 0.75rem;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        .stats-icon {
            width: 35px;
            height: 35px;
            font-size: 1rem;
        }
        
        .stats-title h3 {
            font-size: 1rem;
        }
        
        .stats-action {
            width: 100%;
            text-align: center;
        }
        
        .stats-action .btn {
            width: 100%;
            margin-top: 0.5rem;
        }
        
        .stats-numbers {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .stats-numbers .stat-item {
            flex-direction: row;
            justify-content: space-between;
            text-align: left;
        }
        
        .team-members {
            gap: 0.5rem;
        }
        
        .team-member {
            padding: 0.5rem;
        }
        
        .member-avatar {
            width: 30px;
            height: 30px;
            font-size: 0.8rem;
        }
        
        .progress-bar-wide {
            height: 10px;
        }
        
        .tasks-list {
            padding: 0.75rem;
            gap: 0.5rem;
        }
        
        .task-card-row {
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.75rem;
        }
        
        .task-status-indicator {
            flex-direction: row;
            width: auto;
            gap: 0.75rem;
            align-items: center;
        }
        
        .task-status-indicator i {
            width: 35px;
            height: 35px;
            font-size: 1.1rem;
        }
        
        .status-label {
            font-size: 0.65rem;
        }
        
        .task-id-below .task-id-badge {
            font-size: 0.75rem;
            padding: 0.2rem 0.4rem;
        }
        
        .task-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
        
        .task-details-row {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .task-actions {
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            gap: 0.75rem;
        }
        
        .priority-badge {
            margin-bottom: 0;
        }
        
        .priority-badge-inner {
            padding: 0.375rem 0.5rem;
            gap: 0.25rem;
        }
        
        .priority-label {
            font-size: 0.65rem;
        }
        
        .action-buttons {
            gap: 0.375rem;
        }
        
        .action-buttons .btn {
            padding: 0.25rem 0.5rem;
            min-width: 55px;
            font-size: 0.7rem;
        }
    }

    @media (max-width: 576px) {
        .stats-card {
            padding: 0.875rem;
        }
        
        .stats-header {
            flex-direction: column;
            text-align: center;
            gap: 0.5rem;
        }
        
        .stats-icon {
            width: 30px;
            height: 30px;
            font-size: 0.9rem;
        }
        
        .stats-title h3 {
            font-size: 0.95rem;
        }
        
        .progress-bar-wide {
            height: 8px;
        }
        
        .tasks-list {
            padding: 0.5rem;
            gap: 0.5rem;
        }
        
        .task-card-row {
            padding: 0.5rem;
            border-radius: 8px;
        }
        
        .task-status-indicator {
            width: 32px;
            height: 32px;
        }
        
        .task-status-indicator i {
            font-size: 1rem;
        }
        
        .task-title h6 {
            font-size: 0.9rem;
        }
        
        .task-assigned {
            font-size: 0.85rem;
        }
        
        .task-meta-item {
            font-size: 0.75rem;
        }
    }

    /* Enhanced Search and Filter Styles */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
    }

    .search-container {
        position: relative;
        min-width: 400px;
    }

    .search-container .input-group {
        border-radius: 25px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        align-items: stretch;
    }

    .search-container .input-group-text {
        border: none;
        background: white;
        padding: 0.75rem 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        height: auto;
    }

    .search-container .form-control {
        border: none;
        padding: 0.75rem 1.25rem;
        background: white;
        font-size: 1rem;
        height: auto;
        line-height: 1.5;
    }

    .search-container .form-control:focus {
        box-shadow: none;
        background: white;
    }

    /* Task Tabs Styles */
    .task-tabs-container {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .task-tabs {
        border: none;
        margin: 0;
        padding: 0 1rem;
    }

    .task-tabs .nav-item {
        margin: 0;
    }

    .task-tabs .nav-link {
        border: none;
        background: transparent;
        color: #6c757d;
        font-weight: 500;
        padding: 1rem 1.5rem;
        border-radius: 0;
        transition: all 0.3s ease;
        position: relative;
        margin: 0;
    }

    .task-tabs .nav-link:hover {
        color: #495057;
        background: rgba(0,0,0,0.05);
    }

    .task-tabs .nav-link.active {
        color: #007bff;
        background: white;
        border-bottom: 3px solid #007bff;
        font-weight: 600;
    }

    .task-tabs .nav-link i {
        margin-right: 0.5rem;
        font-size: 0.9rem;
    }

    .task-tabs .nav-link .task-count {
        background: #e9ecef;
        color: #6c757d;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-left: 0.5rem;
        min-width: 1.5rem;
        text-align: center;
        display: inline-block;
    }

    .task-tabs .nav-link.active .task-count {
        background: #007bff;
        color: white;
    }

    .tab-content {
        background: white;
    }

    .tab-pane {
        padding: 0;
    }

    .tab-pane .tasks-list {
        padding: 1rem;
    }

    /* Filter Buttons (Legacy - keeping for reference) */
    .filter-buttons .btn-group {
        border-radius: 25px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .filter-buttons .btn {
        border: none;
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .filter-buttons .btn:hover {
        transform: translateY(-1px);
    }

    .filter-buttons .btn.active {
        background: #007bff;
        color: white;
    }

    /* Employee Workload Card Styles */
    .workload-stats {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .workload-stats .stat-item {
        padding: 0.5rem;
    }

    .workload-stats .stat-item h4 {
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .employee-cards {
        max-height: 400px;
        overflow-y: auto;
    }

    .employee-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .employee-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-color: #007bff;
    }

    .employee-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .employee-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(45deg, #007bff, #0056b3);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
        flex-shrink: 0;
    }

    .employee-info {
        flex: 1;
        min-width: 0;
    }

    .employee-info h6 {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 0.125rem;
    }

    .employee-info small {
        font-size: 0.8rem;
    }

    .employee-progress {
        flex-shrink: 0;
    }

    .progress-percentage {
        background: #e9ecef;
        color: #495057;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .employee-stats {
        border-top: 1px solid #e9ecef;
        padding-top: 0.75rem;
    }

    .employee-stats small {
        font-size: 0.75rem;
    }

    /* Essential styles for existing functionality */
    .gap-2 > * + * {
        margin-left: 0.5rem;
    }

    .nav-pills .nav-link {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }

    .task-row {
        transition: background-color 0.2s ease;
    }

    .task-row:hover {
        background-color: #f8f9fa;
    }

    .progress {
        background-color: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        height: 8px;
    }

    .progress-bar {
        border-radius: 10px;
        transition: width 0.6s ease;
    }

    .bg-success {
        background: linear-gradient(45deg, #28a745, #20c997) !important;
    }

    .bg-info {
        background: linear-gradient(45deg, #17a2b8, #6f42c1) !important;
    }

    .bg-warning {
        background: linear-gradient(45deg, #ffc107, #fd7e14) !important;
    }

    .bg-danger {
        background: linear-gradient(45deg, #dc3545, #e83e8c) !important;
    }

    /* Card Enhancements */
    .card {
        border-radius: 12px;
        overflow: hidden;
    }

    .card-header {
        border-bottom: 1px solid #e9ecef;
        padding: 1rem 1.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    /* Badge Outline Style */
    .badge-outline {
        background-color: transparent;
        border: 1px solid currentColor;
    }

    .badge-outline.badge-success {
        color: #28a745;
    }

    .badge-outline.badge-warning {
        color: #ffc107;
    }

    .badge-outline.badge-danger {
        color: #dc3545;
    }

    /* Search Input Styles */
    .input-group-text {
        border-color: #ced4da;
    }

    .input-group .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Responsive adjustments for new elements */
    @media (max-width: 768px) {
        .search-container {
            min-width: 250px;
        }
        
        .task-tabs .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.9rem;
        }
        
        .task-tabs .nav-link i {
            margin-right: 0.25rem;
            font-size: 0.8rem;
        }
        
        .task-tabs .nav-link .task-count {
            margin-left: 0.25rem;
            padding: 0.2rem 0.4rem;
            font-size: 0.7rem;
            min-width: 1.2rem;
        }
        
        .workload-overview {
            padding: 1rem;
        }
        
        .stat-circle {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }
        
        .stat-info h4 {
            font-size: 1.1rem;
        }
        
        .team-member-card {
            padding: 0.75rem;
        }
        
        .member-header {
            gap: 0.5rem;
        }
        
        .member-avatar {
            width: 35px;
            height: 35px;
            font-size: 0.9rem;
        }
        
        .member-name {
            font-size: 0.85rem;
        }
        
        .employee-header {
            flex-direction: column;
            text-align: center;
            gap: 0.5rem;
        }
        
        .employee-avatar {
            width: 35px;
            height: 35px;
            font-size: 0.9rem;
        }
        
        .employee-progress {
            align-self: center;
        }
    }

    @media (max-width: 576px) {
        .search-container {
            min-width: 200px;
        }
        
        .search-container .form-control {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
        
        .search-container .input-group-text {
            padding: 0.5rem 1rem;
        }
        
        .task-tabs {
            padding: 0 0.5rem;
        }
        
        .task-tabs .nav-link {
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
        }
        
        .task-tabs .nav-link i {
            margin-right: 0.25rem;
            font-size: 0.75rem;
        }
        
        .task-tabs .nav-link .task-count {
            margin-left: 0.25rem;
            padding: 0.15rem 0.3rem;
            font-size: 0.65rem;
            min-width: 1rem;
        }
        
        .workload-overview {
            padding: 0.75rem;
        }
        
        .stat-circle {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
        
        .stat-info h4 {
            font-size: 1rem;
        }
        
        .stat-info small {
            font-size: 0.7rem;
        }
        
        .team-members-container {
            padding: 0.75rem;
        }
        
        .team-member-card {
            padding: 0.5rem;
        }
        
        .member-header {
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .member-avatar {
            width: 30px;
            height: 30px;
            font-size: 0.8rem;
        }
        
        .member-name {
            font-size: 0.8rem;
        }
        
        .completion-badge {
            padding: 0.2rem 0.4rem;
            font-size: 0.7rem;
            min-width: 1.5rem;
        }
        
        .task-breakdown {
            gap: 0.2rem;
        }
        
        .breakdown-item {
            font-size: 0.65rem;
            gap: 0.3rem;
        }
        
        .breakdown-dot {
            width: 6px;
            height: 6px;
        }
        
        .employee-card {
            padding: 0.75rem;
        }
        
        .workload-stats {
            padding: 0.75rem;
        }
        
        .workload-stats .stat-item h4 {
            font-size: 1.1rem;
        }
    }

    /* Redesigned Team Workload Distribution Styles */
    .workload-overview {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .workload-stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.5rem;
        font-size: 1.5rem;
        color: white;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .completed-stat {
        background: linear-gradient(45deg, #28a745, #20c997);
    }

    .progress-stat {
        background: linear-gradient(45deg, #ffc107, #fd7e14);
    }

    .pending-stat {
        background: linear-gradient(45deg, #dc3545, #e83e8c);
    }

    .stat-info {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .team-members-container {
        margin-top: 1rem;
    }

    .team-members-header {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }

    .team-members-list {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .team-member-card {
        flex: 1;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .team-member-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border-color: #007bff;
    }

    .member-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .member-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(45deg, #007bff, #0056b3);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
        flex-shrink: 0;
    }

    .member-info {
        flex: 1;
        min-width: 0;
    }

    .member-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
        margin-bottom: 0.125rem;
    }

    .member-role {
        font-size: 0.8rem;
        color: #6c757d;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .member-completion {
        flex-shrink: 0;
    }

    .completion-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-align: center;
        min-width: 2rem;
    }

    .completion-badge.excellent {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }

    .completion-badge.good {
        background: linear-gradient(45deg, #17a2b8, #6f42c1);
        color: white;
    }

    .completion-badge.fair {
        background: linear-gradient(45deg, #ffc107, #fd7e14);
        color: white;
    }

    .completion-badge.poor {
        background: linear-gradient(45deg, #dc3545, #e83e8c);
        color: white;
    }

    .member-progress-section {
        margin-top: 0.75rem;
    }

    .progress-container {
        height: 8px;
        background-color: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
        margin-bottom: 0.5rem;
    }

    .progress-bar-stacked {
        display: flex;
        height: 100%;
        width: 100%;
    }

    .progress-segment {
        height: 100%;
        transition: width 0.6s ease;
    }

    .progress-segment.completed {
        background: linear-gradient(45deg, #28a745, #20c997);
    }

    .progress-segment.in-progress {
        background: linear-gradient(45deg, #ffc107, #fd7e14);
    }

    .progress-segment.pending {
        background: linear-gradient(45deg, #dc3545, #e83e8c);
    }

    .task-breakdown {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .breakdown-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.7rem;
        color: #6c757d;
    }

    .breakdown-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        flex-shrink: 0;
    }

    .breakdown-dot.completed {
        background: #28a745;
    }

    .breakdown-dot.in-progress {
        background: #ffc107;
    }

    .breakdown-dot.pending {
        background: #dc3545;
    }

    .breakdown-label {
        font-size: 0.7rem;
        color: #6c757d;
    }

    .empty-workload {
        text-align: center;
        padding: 2rem;
        border: 1px dashed #ced4da;
        border-radius: 8px;
        margin: 1rem;
    }

    .empty-icon {
        font-size: 3rem;
        color: #ced4da;
        margin-bottom: 1rem;
    }

    .workload-overview {
        padding: 1.5rem;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    .workload-summary .badge {
        background: rgba(255,255,255,0.2);
        color: white;
        border: 1px solid rgba(255,255,255,0.3);
    }

    .team-members-container {
        padding: 1rem;
    }

    .team-members-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .team-member-card {
        flex: none;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    /* Simplified Project Tasks Section Styles */
    .tasks-section {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        margin-bottom: 2rem;
    }

    .tasks-header {
        background: #f8f9fa;
        padding: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        border-radius: 12px 12px 0 0;
    }

    .tasks-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .tasks-title-section {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .tasks-icon {
        width: 40px;
        height: 40px;
        background: #007bff;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: white;
    }

    .tasks-title h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #2c3e50;
    }

    .tasks-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .search-container-simple {
        position: relative;
        min-width: 250px;
    }

    .search-input-simple {
        border: 1px solid #ced4da;
        border-radius: 6px;
        padding: 0.5rem 1rem 0.5rem 2.5rem;
        font-size: 0.9rem;
        width: 100%;
        transition: border-color 0.3s ease;
    }

    .search-input-simple:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    }

    .search-icon-simple {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-size: 0.9rem;
    }

    .add-task-btn-simple {
        background: #007bff;
        border: 1px solid #007bff;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-weight: 500;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .add-task-btn-simple:hover {
        background: #0056b3;
        border-color: #0056b3;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    /* Professional Filter Tabs */
    .tasks-filter-section {
        background: white;
        border-bottom: 1px solid #e9ecef;
        padding: 0;
    }

    .filter-tabs-simple {
        display: flex;
        margin: 0;
        padding: 0;
        list-style: none;
        overflow-x: auto;
        background: #f8f9fa;
    }

    .filter-tab-simple {
        flex-shrink: 0;
        margin: 0;
    }

    .filter-tab-btn-simple {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem 1.5rem;
        background: transparent;
        border: none;
        color: #6c757d;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        position: relative;
        white-space: nowrap;
        text-decoration: none;
        border-bottom: 3px solid transparent;
    }

    .filter-tab-btn-simple:hover {
        color: #495057;
        background: rgba(0,0,0,0.03);
        text-decoration: none;
    }

    .filter-tab-btn-simple.active {
        color: #007bff;
        background: white;
        font-weight: 600;
        border-bottom-color: #007bff;
        box-shadow: 0 -2px 4px rgba(0,0,0,0.05);
    }

    .task-count-simple {
        background: #e9ecef;
        color: #6c757d;
        padding: 0.25rem 0.6rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        min-width: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .filter-tab-btn-simple.active .task-count-simple {
        background: #007bff;
        color: white;
        box-shadow: 0 1px 3px rgba(0,123,255,0.3);
    }

    /* Simple Task Cards */
    .tasks-content {
        padding: 0;
    }

    .tasks-list-simple {
        padding: 1rem;
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .task-card-simple {
        background: transparent;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 0;
        transition: all 0.3s ease;
        cursor: pointer;
        display: flex;
        align-items: stretch;
        overflow: hidden;
    }

    .task-card-simple:hover {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transform: translateY(-1px);
    }

    /* Task Status Indicator */
    .task-status-indicator {
        flex-shrink: 0;
        width: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.3rem;
        color: #2c3e50;
    }

    .task-status-indicator.pending {
        background: rgba(220, 53, 69, 0.15);
        color: #721c24;
    }

    .task-status-indicator.assigned {
        background: rgba(255, 193, 7, 0.15);
        color: #856404;
    }

    .task-status-indicator.completed {
        background: rgba(40, 167, 69, 0.15);
        color: #155724;
    }

    /* Task Content */
    .task-content-simple {
        flex: 1;
        min-width: 0;
        padding: 1rem;
        background: white;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .task-title-simple {
        font-size: 1rem;
        font-weight: 600;
        color: #2c3e50;
        line-height: 1.3;
        flex: 1;
    }

    .task-meta-simple {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .task-id-simple {
        background: #f8f9fa;
        color: #6c757d;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
        font-family: 'Courier New', monospace;
    }

    .task-priority-simple {
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .task-priority-simple.low {
        background: rgba(40, 167, 69, 0.1);
        color: #155724;
    }

    .task-priority-simple.medium {
        background: rgba(255, 193, 7, 0.1);
        color: #856404;
    }

    .task-priority-simple.high {
        background: rgba(220, 53, 69, 0.1);
        color: #721c24;
    }

    .task-assignee-simple {
        font-size: 0.85rem;
        color: #6c757d;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .task-assignee-simple i {
        color: #007bff;
        font-size: 0.8rem;
    }

    /* Task Actions */
    .task-actions-simple {
        flex-shrink: 0;
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .task-action-btn-simple {
        width: 32px;
        height: 32px;
        border: 1px solid #dee2e6;
        background: white;
        color: #6c757d;
        border-radius: 4px;
        font-size: 0.8rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .task-action-btn-simple:hover {
        background: #007bff;
        color: white;
        border-color: #007bff;
        transform: translateY(-1px);
    }

    .task-action-btn-simple.delete {
        color: #dc3545;
        border-color: #dc3545;
    }

    .task-action-btn-simple.delete:hover {
        background: #dc3545;
        color: white;
    }

    /* Empty State */
    .empty-tasks-simple {
        text-align: center;
        padding: 3rem 2rem;
        color: #6c757d;
    }

    .empty-tasks-icon-simple {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .empty-tasks-title-simple {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #495057;
    }

    .empty-tasks-description-simple {
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .tasks-header {
            padding: 1rem;
        }

        .tasks-header-content {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .tasks-title-section {
            justify-content: center;
        }

        .tasks-actions {
            justify-content: center;
        }

        .search-container-simple {
            min-width: auto;
            width: 100%;
        }

        .filter-tabs-simple {
            padding: 0 0.5rem;
        }

        .filter-tab-btn-simple {
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
        }

        .tasks-list-simple {
            padding: 0.75rem;
            gap: 0.5rem;
        }

        .task-card-simple {
            flex-direction: column;
            align-items: stretch;
        }

        .task-status-indicator {
            width: 100%;
            height: 40px;
            font-size: 1rem;
        }

        .task-content-simple {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .task-meta-simple {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .task-actions-simple {
            align-self: flex-end;
            flex-direction: row;
            gap: 0.5rem;
        }
    }

    @media (max-width: 576px) {
        .tasks-header {
            padding: 0.75rem;
        }

        .tasks-icon {
            width: 35px;
            height: 35px;
            font-size: 1rem;
        }

        .tasks-title h3 {
            font-size: 1.1rem;
        }

        .search-input-simple {
            padding: 0.4rem 0.75rem 0.4rem 2rem;
            font-size: 0.85rem;
        }

        .search-icon-simple {
            left: 0.5rem;
        }

        .add-task-btn-simple {
            padding: 0.4rem 0.75rem;
            font-size: 0.85rem;
        }

        .filter-tab-btn-simple {
            padding: 0.4rem 0.5rem;
            font-size: 0.75rem;
        }

        .task-card-simple {
            padding: 0.5rem;
        }

        .task-title-simple {
            font-size: 0.9rem;
        }

        .task-meta-simple {
            font-size: 0.8rem;
        }

        .task-action-btn-simple {
            width: 28px;
            height: 28px;
            font-size: 0.7rem;
        }
    }
</style>
<div class="container-fluid" style="padding:16px!important">
    <!-- Modern Header Section -->
    <div class="project-header-section">
        <div class="header-background">
            <div class="header-overlay"></div>
        </div>
        
        <div class="header-content">
            <!-- Header Top Row -->
            <div class="header-top-row">
                <!-- Breadcrumb Navigation -->
                <nav aria-label="breadcrumb" class="breadcrumb-nav">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('app/projects') ?>" class="breadcrumb-link">
                                <i class="fas fa-project-diagram"></i>
                                <span>Projects</span>
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <span class="breadcrumb-current"><?= htmlspecialchars($project['title']) ?></span>
                        </li>
                    </ol>
                </nav>
                
                <!-- Action Buttons -->
                <div class="project-actions">
                    <?php if (has_permission('projects/edit')): ?>
                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$project['id'].'/?page_name=projects/edit'); ?>', 'Edit Project')"
                            class="btn btn-outline-light btn-action">
                        <i class="fas fa-pencil-alt"></i>
                        <span>Edit Project</span>
                    </button>
                    <?php endif; ?>
                    <?php if (has_permission('tasks/add')): ?>
                    <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add&project_id=<?= $project['id'] ?>', 'Add Task')"
                            class="btn btn-outline-light btn-action">
                        <i class="fas fa-plus"></i>
                        <span>Add Task</span>
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Main Project Information -->
            <div class="project-main-info">
                <div class="project-title-section">
                    <h1 class="project-title"><?= htmlspecialchars($project['title']) ?></h1>
                    <div class="project-meta">
                        <div class="meta-item">
                            <i class="fas fa-user-circle"></i>
                            <span>Client: <strong><?= $client_list[$project['client_id']] ?></strong></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-tag"></i>
                            <span>Type: <strong><?= $project_type[$project['project_type']] ?></strong></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Created: <strong><?= DateTime::createFromFormat('Y-m-d H:i:s', $project['created_on'])->format('M d, Y') ?></strong></span>
                        </div>
                    </div>
                </div>
                
                <div class="project-status-section">
                    <!-- Project Status -->
                    <?php if($project['is_delivered'] == 1): ?>
                        <div class="status-badge status-delivered">
                            <i class="fas fa-check-circle"></i>
                            <span>Delivered</span>
                        </div>
                    <?php else: ?>
                        <div class="status-badge status-not-delivered">
                            <i class="fas fa-times-circle"></i>
                            <span>Not Delivered</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Project Stats Cards -->
    <div class="project-stats-section">
        <div class="row">
            <!-- Tasks Progress Card -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="stats-card tasks-card">
                    <div class="stats-header">
                        <div class="stats-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stats-title">
                            <h3>Tasks Progress</h3>
                        </div>
                        <div class="stats-action">
                            <a href="<?= base_url('app/tasks?project_id=' . $project['id']) ?>" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> View Tasks
                            </a>
                        </div>
                    </div>
                    <div class="stats-content">
                        <div class="progress-bar-wide">
                            <div class="progress-fill" style="width: <?= $project['tasks']['progress'] ?>%"></div>
                        </div>
                        <div class="stats-numbers">
                            <div class="stat-item">
                                <span class="stat-number completed-bg"><?= $project['tasks']['completed'] ?></span>
                                <span class="stat-label">Completed</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number in-progress-bg"><?= $project['tasks']['assigned'] ?></span>
                                <span class="stat-label">In Progress</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number pending-bg"><?= $project['tasks']['total'] - $project['tasks']['completed'] - $project['tasks']['assigned'] ?></span>
                                <span class="stat-label">Pending</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tickets Summary Card -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="stats-card tickets-card">
                    <div class="stats-header">
                        <div class="stats-icon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="stats-title">
                            <h3>Tickets Summary</h3>
                        </div>
                        <div class="stats-action">
                            <a href="<?= base_url('app/tickets?project_id=' . $project['id']) ?>" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-eye"></i> View Tickets
                            </a>
                        </div>
                    </div>
                    <div class="stats-content">
                        <div class="progress-bar-wide">
                            <div class="progress-fill tickets" style="width: <?= $project['tickets']['total'] > 0 ? round((($project['tickets']['total'] - $project['tickets']['pending']) / $project['tickets']['total']) * 100) : 0 ?>%"></div>
                        </div>
                        <div class="stats-numbers">
                            <div class="stat-item">
                                <span class="stat-number resolved-bg"><?= $project['tickets']['total'] - $project['tickets']['pending'] ?></span>
                                <span class="stat-label">Resolved</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number in-progress-bg"><?= $project['tickets']['assigned'] ?></span>
                                <span class="stat-label">In Progress</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number pending-bg"><?= $project['tickets']['pending'] ?></span>
                                <span class="stat-label">Pending</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Project Team Card -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="stats-card team-card">
                    <div class="stats-header">
                        <div class="stats-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stats-title">
                            <h3>Project Team</h3>
                        </div>
                    </div>
                    <div class="team-members">
                        <div class="team-member compact">
                            <div class="member-avatar">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="member-info">
                                <div class="member-name">Tester</div>
                                <div class="member-role"><?= $users[$project['tester_id']] ?? 'Not assigned' ?></div>
                            </div>
                        </div>
                        <div class="team-member compact">
                            <div class="member-avatar project-lead">
                                <i class="fas fa-user-tie"></i>
                            </div>
                            <div class="member-info">
                                <div class="member-name">Project Lead</div>
                                <div class="member-role"><?= $users[$project['project_lead_id']] ?? 'Not assigned' ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-8">
            <!-- Simplified Project Tasks Section -->
            <div class="tasks-section">
                <!-- Simple Header -->
                <div class="tasks-header">
                    <div class="tasks-header-content">
                        <div class="tasks-title-section">
                            <div class="tasks-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="tasks-title">
                                <h3>Project Tasks</h3>
                            </div>
                        </div>
                        
                        <div class="tasks-actions">
                            <div class="search-container-simple">
                                <i class="fas fa-search search-icon-simple"></i>
                                <input type="text" class="search-input-simple" id="taskSearchInput" placeholder="Search tasks...">
                            </div>
                            
                            <?php if (has_permission('tasks/add')): ?>
                            <a href="#" onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add&project_id=<?= $project['id'] ?>', 'Add Task')" class="add-task-btn-simple">
                                <i class="fas fa-plus"></i>
                                <span>Add Task</span>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Simple Filter Tabs -->
                <div class="tasks-filter-section">
                    <ul class="filter-tabs-simple" id="taskFilterTabs">
                        <li class="filter-tab-simple">
                            <button class="filter-tab-btn-simple active" data-filter="all">
                                <i class="fas fa-list"></i>
                                <span>All</span>
                                <span class="task-count-simple" id="all-count"><?= count($project_tasks) ?></span>
                            </button>
                        </li>
                        <li class="filter-tab-simple">
                            <button class="filter-tab-btn-simple" data-filter="pending">
                                <i class="fas fa-clock"></i>
                                <span>Pending</span>
                                <span class="task-count-simple" id="pending-count"><?= count(array_filter($project_tasks, function($task) { return $task['task_status'] == 'pending'; })) ?></span>
                            </button>
                        </li>
                        <li class="filter-tab-simple">
                            <button class="filter-tab-btn-simple" data-filter="assigned">
                                <i class="fas fa-play"></i>
                                <span>In Progress</span>
                                <span class="task-count-simple" id="assigned-count"><?= count(array_filter($project_tasks, function($task) { return $task['task_status'] == 'assigned'; })) ?></span>
                            </button>
                        </li>
                        <li class="filter-tab-simple">
                            <button class="filter-tab-btn-simple" data-filter="completed">
                                <i class="fas fa-check"></i>
                                <span>Completed</span>
                                <span class="task-count-simple" id="completed-count"><?= count(array_filter($project_tasks, function($task) { return $task['task_status'] == 'completed'; })) ?></span>
                            </button>
                        </li>
                    </ul>
                </div>
                
                <!-- Tasks Content -->
                <div class="tasks-content">
                    <div class="tasks-list-simple" id="tasksList">
                        <?php if (empty($project_tasks)): ?>
                            <!-- Empty State -->
                            <div class="empty-tasks-simple">
                                <div class="empty-tasks-icon-simple">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <div class="empty-tasks-title-simple">No tasks yet</div>
                                <div class="empty-tasks-description-simple">Get started by creating your first task for this project.</div>
                                <?php if (has_permission('tasks/add')): ?>
                                <a href="#" onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add&project_id=<?= $project['id'] ?>', 'Add Task')" class="add-task-btn-simple">
                                    <i class="fas fa-plus"></i>
                                    <span>Create First Task</span>
                                </a>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <?php foreach ($project_tasks as $task): ?>
                            <div class="task-card-simple" 
                                 data-status="<?= $task['task_status'] ?>" 
                                 data-search="<?= strtolower(htmlspecialchars($task['title'] . ' ' . ($task['user_name'] ?? '') . ' ' . ($task['creator_name'] ?? ''))) ?>" 
                                 onclick="handleTaskCardClick(event, <?= $task['id'] ?>)">
                                
                                <!-- Task Status Indicator (Left Side) -->
                                <div class="task-status-indicator <?= $task['task_status'] ?>">
                                    <?php if($task['task_status'] == 'completed'): ?>
                                        <i class="fas fa-check"></i>
                                    <?php elseif($task['task_status'] == 'assigned'): ?>
                                        <i class="fas fa-clock"></i>
                                    <?php else: ?>
                                        <i class="fas fa-exclamation"></i>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Task Content -->
                                <div class="task-content-simple">
                                    <div class="task-title-simple"><?= htmlspecialchars($task['title']) ?></div>
                                    
                                    <div class="task-meta-simple">
                                        <span class="task-id-simple">#<?= $task['id'] ?></span>
                                        <div class="task-assignee-simple">
                                            <i class="fas fa-user"></i>
                                            <span><?= $task['user_name'] ? htmlspecialchars($task['user_name']) : 'Unassigned' ?></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Priority and Actions -->
                                <div class="task-actions-simple">
                                    <span class="task-priority-simple <?= $task['task_priority'] ?>"><?= ucfirst($task['task_priority']) ?></span>
                                    
                                    <?php if (has_permission('tasks/edit')): ?>
                                    <button class="task-action-btn-simple" onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$task['id'].'/?page_name=tasks/edit'); ?>', 'Edit Task')" title="Edit Task">
                                        <i class="fas fa-pencil-alt"></i>
                                    </button>
                                    <?php endif; ?>
                                    
                                    <?php if (has_permission('tasks/delete')): ?>
                                    <button class="task-action-btn-simple delete" onclick="deleteTask(<?= $task['id'] ?>)" title="Delete Task">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    
                    <!-- No Tasks Found Message -->
                    <div id="noTasksFound" class="empty-tasks-simple" style="display: none;">
                        <div class="empty-tasks-icon-simple">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="empty-tasks-title-simple">No tasks found</div>
                        <div class="empty-tasks-description-simple">Try adjusting your search criteria or filter options.</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-lg-4">
            <!-- Redesigned Team Workload Distribution -->
            <div class="card shadow-sm border-0 mb-4 d-none">
                <div class="card-header bg-gradient-info text-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users mr-2"></i>Team Workload
                        </h5>
                        <div class="workload-summary">
                            <span class="badge badge-light badge-pill">
                                <i class="fas fa-chart-pie mr-1"></i>
                                <?= count($employee_workload) ?> Team Members
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($employee_workload)): ?>
                        <!-- Workload Overview Stats -->
                        <div class="workload-overview">
                            <div class="row text-center m-0">
                                <div class="col-4 workload-stat-item">
                                    <div class="stat-circle completed-stat">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4 class="mb-0 text-success"><?= array_sum(array_column($employee_workload, 'completed_tasks')) ?></h4>
                                        <small class="text-muted">Completed</small>
                                    </div>
                                </div>
                                <div class="col-4 workload-stat-item">
                                    <div class="stat-circle progress-stat">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4 class="mb-0 text-warning"><?= array_sum(array_column($employee_workload, 'assigned_tasks')) ?></h4>
                                        <small class="text-muted">In Progress</small>
                                    </div>
                                </div>
                                <div class="col-4 workload-stat-item">
                                    <div class="stat-circle pending-stat">
                                        <i class="fas fa-exclamation-circle"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4 class="mb-0 text-danger"><?= array_sum(array_column($employee_workload, 'pending_tasks')) ?></h4>
                                        <small class="text-muted">Pending</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Team Members List -->
                        <div class="team-members-container">
                            <div class="team-members-header">
                                <h6 class="mb-0 text-muted">
                                    <i class="fas fa-user-friends mr-1"></i>
                                    Individual Workload
                                </h6>
                            </div>
                            <div class="team-members-list">
                                <?php foreach ($employee_workload as $employee): ?>
                                <div class="team-member-card">
                                    <div class="member-header">
                                        <div class="member-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="member-info">
                                            <h6 class="member-name mb-0"><?= htmlspecialchars($employee['name']) ?></h6>
                                            <small class="text-muted"><?= $employee['task_count'] ?> total tasks</small>
                                        </div>
                                        <div class="member-completion">
                                            <?php 
                                            $completion_rate = $employee['task_count'] > 0 ? round(($employee['completed_tasks'] / $employee['task_count']) * 100) : 0;
                                            $completion_class = $completion_rate >= 80 ? 'excellent' : ($completion_rate >= 60 ? 'good' : ($completion_rate >= 40 ? 'fair' : 'poor'));
                                            ?>
                                            <div class="completion-badge <?= $completion_class ?>">
                                                <?= $completion_rate ?>%
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="member-progress-section">
                                        <div class="progress-container">
                                            <div class="progress-bar-stacked">
                                                <div class="progress-segment completed" style="width: <?= $employee['task_count'] > 0 ? ($employee['completed_tasks'] / $employee['task_count']) * 100 : 0 ?>%"></div>
                                                <div class="progress-segment in-progress" style="width: <?= $employee['task_count'] > 0 ? ($employee['assigned_tasks'] / $employee['task_count']) * 100 : 0 ?>%"></div>
                                                <div class="progress-segment pending" style="width: <?= $employee['task_count'] > 0 ? ($employee['pending_tasks'] / $employee['task_count']) * 100 : 0 ?>%"></div>
                                            </div>
                                        </div>
                                        
                                        <div class="task-breakdown">
                                            <div class="breakdown-item">
                                                <span class="breakdown-dot completed"></span>
                                                <span class="breakdown-label"><?= $employee['completed_tasks'] ?> completed</span>
                                            </div>
                                            <div class="breakdown-item">
                                                <span class="breakdown-dot in-progress"></span>
                                                <span class="breakdown-label"><?= $employee['assigned_tasks'] ?> in progress</span>
                                            </div>
                                            <div class="breakdown-item">
                                                <span class="breakdown-dot pending"></span>
                                                <span class="breakdown-label"><?= $employee['pending_tasks'] ?> pending</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="empty-workload">
                            <div class="text-center py-5">
                                <div class="empty-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h6 class="text-muted mt-3">No team members assigned</h6>
                                <p class="text-muted small mb-0">Team workload will appear here once tasks are assigned to team members.</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Time Tracking Summary -->
            <div class="card shadow-sm border-0 d-none">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-clock mr-2"></i>Time Tracking Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="nav nav-pills nav-fill mb-3" id="time-period-tabs">
                        <a class="nav-link active" data-period="overall" href="#">Overall</a>
                        <a class="nav-link" data-period="last_7_days" href="#">7 Days</a>
                        <a class="nav-link" data-period="last_15_days" href="#">15 Days</a>
                        <a class="nav-link" data-period="last_30_days" href="#">30 Days</a>
                    </div>
                    
                    <div id="time-tracking-content">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
$(document).ready(function() {
    // Time tracking data from PHP
    const timeTrackingData = <?= json_encode($time_tracking) ?>;

    // Initialize time tracking display
    showTimeTrackingData('overall');

    // Time period tab switching
    $('#time-period-tabs .nav-link').on('click', function(e) {
        e.preventDefault();
        $('#time-period-tabs .nav-link').removeClass('active');
        $(this).addClass('active');
        const period = $(this).data('period');
        showTimeTrackingData(period);
    });

    function showTimeTrackingData(period) {
        const data = timeTrackingData[period];
        const totalHours = Math.floor(data.total_duration / 3600);
        const totalMinutes = Math.floor((data.total_duration % 3600) / 60);

        let html = `
            <div class="text-center mb-3">
                <h4 class="text-primary">${totalHours}h ${totalMinutes}m</h4>
                <p class="text-muted mb-0">Total Time Worked</p>
                <small class="text-muted">${data.total_entries} work entries by ${data.unique_users} employees</small>
            </div>
        `;

        if (data.user_breakdown && data.user_breakdown.length > 0) {
            html += '<div class="mt-3"><h6>Employee Breakdown:</h6>';
            data.user_breakdown.forEach(user => {
                const userHours = Math.floor(user.user_duration / 3600);
                const userMinutes = Math.floor((user.user_duration % 3600) / 60);
                const percentage = data.total_duration > 0 ? ((user.user_duration / data.total_duration) * 100).toFixed(1) : 0;

                html += `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm">${user.name}</span>
                        <div class="text-right">
                            <div class="text-sm font-weight-bold">${userHours}h ${userMinutes}m</div>
                            <small class="text-muted">${percentage}%</small>
                        </div>
                    </div>
                    <div class="progress mb-2" style="height: 6px;">
                        <div class="progress-bar bg-info" style="width: ${percentage}%"></div>
                    </div>
                `;
            });
            html += '</div>';
        } else {
            html += '<p class="text-muted text-center mt-3">No time tracking data available for this period.</p>';
        }

        $('#time-tracking-content').html(html);
    }
});

// Simplified Task Management JavaScript
$(document).ready(function() {
    let currentFilter = 'all';
    
    // Filter tab functionality
    $('.filter-tab-btn-simple').on('click', function(e) {
        e.preventDefault();
        
        // Update active tab
        $('.filter-tab-btn-simple').removeClass('active');
        $(this).addClass('active');
        
        // Get filter value
        currentFilter = $(this).data('filter');
        
        // Apply filter
        filterTasks();
    });
    
    // Search functionality
    $('#taskSearchInput').on('input', function() {
        filterTasks();
    });
    
    // Filter tasks based on current filter and search term
    function filterTasks() {
        const searchTerm = $('#taskSearchInput').val().toLowerCase();
        let visibleCount = 0;
        
        $('.task-card-simple').each(function() {
            const card = $(this);
            const cardStatus = card.data('status');
            const cardSearch = card.data('search');
            
            const statusMatch = currentFilter === 'all' || cardStatus === currentFilter;
            const searchMatch = searchTerm === '' || cardSearch.includes(searchTerm);
            
            if (statusMatch && searchMatch) {
                card.show();
                visibleCount++;
            } else {
                card.hide();
            }
        });
        
        // Show/hide no tasks found message
        if (visibleCount === 0) {
            $('#noTasksFound').show();
            $('#tasksList').hide();
        } else {
            $('#noTasksFound').hide();
            $('#tasksList').show();
        }
        
        // Update task counts
        updateTaskCounts();
    }
    
    // Update task count badges
    function updateTaskCounts() {
        const searchTerm = $('#taskSearchInput').val().toLowerCase();
        
        // All tasks count
        let allCount = 0;
        $('.task-card-simple').each(function() {
            const cardSearch = $(this).data('search');
            if (searchTerm === '' || cardSearch.includes(searchTerm)) {
                allCount++;
            }
        });
        $('#all-count').text(allCount);
        
        // Pending tasks count
        let pendingCount = 0;
        $('.task-card-simple[data-status="pending"]').each(function() {
            const cardSearch = $(this).data('search');
            if (searchTerm === '' || cardSearch.includes(searchTerm)) {
                pendingCount++;
            }
        });
        $('#pending-count').text(pendingCount);
        
        // Assigned/In Progress tasks count
        let assignedCount = 0;
        $('.task-card-simple[data-status="assigned"]').each(function() {
            const cardSearch = $(this).data('search');
            if (searchTerm === '' || cardSearch.includes(searchTerm)) {
                assignedCount++;
            }
        });
        $('#assigned-count').text(assignedCount);
        
        // Completed tasks count
        let completedCount = 0;
        $('.task-card-simple[data-status="completed"]').each(function() {
            const cardSearch = $(this).data('search');
            if (searchTerm === '' || cardSearch.includes(searchTerm)) {
                completedCount++;
            }
        });
        $('#completed-count').text(completedCount);
    }
    
    // Handle task card click
    window.handleTaskCardClick = function(event, taskId) {
        // Don't navigate if clicking on action buttons or their children
        if (event.target.closest('.task-actions-simple') || 
            event.target.tagName === 'BUTTON' ||
            event.target.tagName === 'A' ||
            event.target.closest('button') ||
            event.target.closest('a')) {
            return;
        }
        
        // Navigate to task view page
        window.location.href = `<?= base_url('app/tasks/view/') ?>${taskId}`;
    };
    
    // Delete task function
    window.deleteTask = function(taskId) {
        if (confirm('Are you sure you want to delete this task? This action cannot be undone.')) {
            // You can implement AJAX delete here or redirect to delete URL
            window.location.href = `<?= base_url('app/tasks/delete/') ?>${taskId}`;
        }
    };
    
    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // Add smooth scrolling to filter tabs
    $('.filter-tabs-simple').on('scroll', function() {
        // Add shadow effect when scrolled
        if (this.scrollLeft > 0) {
            $(this).addClass('scrolled');
        } else {
            $(this).removeClass('scrolled');
        }
    });
    
    // Add loading state to action buttons
    $('.task-action-btn-simple').on('click', function() {
        const btn = $(this);
        const originalHtml = btn.html();
        
        btn.html('<i class="fas fa-spinner fa-spin"></i>');
        btn.prop('disabled', true);
        
        // Re-enable after a short delay (adjust as needed)
        setTimeout(function() {
            btn.html(originalHtml);
            btn.prop('disabled', false);
        }, 2000);
    });
});

// Format duration helper function
function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
}
</script>

