<style>
    /* Professional Card Styles */
    .project-card {
        transition: all 0.3s ease-in-out;
        border: none !important;
        border-radius: 15px !important;
        overflow: hidden;
        background: #ffffff;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08) !important;
    }

    .project-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12) !important;
    }

    /* Clickable Wrapper Styles */
    .card-clickable-wrapper {
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .card-clickable-wrapper:hover {
        background-color: rgba(0, 123, 255, 0.02);
    }

    /* Header Section Styling - Light Gradient */
    .project-header {
        background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 100%);
        border-bottom: 1px solid rgba(0,0,0,0.1);
        position: relative;
        color: #495057;
    }

    .project-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent 0%, rgba(0,123,255,0.2) 50%, transparent 100%);
    }

    .project-title-link {
        transition: color 0.2s ease;
        font-size: 1.25rem;
        line-height: 1.3;
        color: #2c3e50 !important;
    }

    .card-clickable-wrapper:hover .project-title-link {
        color: #007bff !important;
    }

    /* Tester Info Styling */
    .tester-info {
        font-weight: 500;
    }

    .tester-info .text-danger {
        color: #dc3545 !important;
        font-weight: 600;
    }

    .tester-info .text-success {
        color: #28a745 !important;
        font-weight: 600;
    }

    /* Content Section Styling - Reduced spacing */
    .project-content {
        background: #ffffff;
        padding: 0.75rem !important;
        border-top: 1px solid rgba(0,0,0,0.05);
    }

    /* Action Buttons Styling - Compact with reduced spacing */
    .action-buttons {
        margin-top: 0;
        margin-bottom: 0;
    }

    .action-btn {
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.75rem;
        border-radius: 6px;
        transition: all 0.3s ease;
        border-width: 1.5px;
        padding: 4px 6px;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(0,0,0,0.12);
    }

    .btn-text {
        margin-left: 4px;
    }

    .add-task-btn {
        height: 32px;
        font-weight: 600;
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
        box-shadow: none;
    }

    .add-task-btn:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
    }

    /* Grid Gap Utility */
    .g-2 > * {
        padding: 3px;
    }

    /* Simplified Statistics Cards */
    .stats-card {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: none;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .stats-card-body {
        padding: 1.5rem;
        display: flex;
        align-items: center;
    }

    .stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-right: 1rem;
    }

    .stats-content {
        flex: 1;
    }

    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.25rem;
    }

    .stats-label {
        font-size: 0.9rem;
        font-weight: 600;
        color: #64748b;
    }

    /* Card Variants */
    .stats-card-primary .stats-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .stats-card-primary .stats-number {
        color: #667eea;
    }

    .stats-card-danger .stats-icon {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
    }

    .stats-card-danger .stats-number {
        color: #ff6b6b;
    }

    .stats-card-warning .stats-icon {
        background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        color: white;
    }

    .stats-card-warning .stats-number {
        color: #feca57;
    }

    .stats-card-success .stats-icon {
        background: linear-gradient(135deg, #48cab2 0%, #2dd4bf 100%);
        color: white;
    }

    .stats-card-success .stats-number {
        color: #48cab2;
    }

    /* Badge Outline Styles */
    .badge-outline-primary {
        color: #007bff;
        background-color: rgba(0, 123, 255, 0.1);
        border: 1px solid rgba(0, 123, 255, 0.3);
    }

    .badge-outline-info {
        color: #17a2b8;
        background-color: rgba(23, 162, 184, 0.1);
        border: 1px solid rgba(23, 162, 184, 0.3);
    }

    /* Progress Bar Enhancements */
    .progress {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 10px;
        overflow: hidden;
        height: 8px;
    }

    .progress-bar {
        border-radius: 10px;
        transition: width 0.6s ease;
    }

    .bg-success {
        background: linear-gradient(45deg, #28a745, #20c997) !important;
    }

    .bg-info {
        background: linear-gradient(45deg, #17a2b8, #6f42c1) !important;
    }

    /* Search Bar Enhancement */
    .input-group-text {
        border: none;
        background: transparent;
    }

    .form-control {
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        font-size: 0.875rem;
    }

    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    select.form-control {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    /* Utility Classes */
    .gap-2 > * + * {
        margin-left: 0.5rem;
    }

    .shadow-lg {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .project-title-link {
            font-size: 1.1rem;
        }

        .project-content {
            padding: 0.5rem !important;
        }

        .action-btn {
            height: 28px;
            font-size: 0.7rem;
            padding: 3px 4px;
        }

        .add-task-btn {
            height: 28px;
        }

        .badge-lg {
            font-size: 0.8rem;
            padding: 6px 12px;
        }

        .sub-card-number {
            font-size: 1.25rem;
        }

        .sub-card-label {
            font-size: 0.55rem;
        }

        .progress-label {
            font-size: 0.8rem;
        }

        .progress-count {
            font-size: 0.7rem;
        }

        .sub-card {
            padding: 6px 3px;
        }

        .value-cards-container {
            padding: 6px 0 0 0;
        }

        .tester-info {
            font-size: 0.8rem;
        }

        .col-md-3 {
            margin-bottom: 10px;
        }
        
        .form-control {
            font-size: 0.8rem;
        }
        
        .btn-primary {
            font-size: 0.8rem;
            padding: 0.5rem 1rem;
        }
    }

    /* Project Status Badges */
    .project-status {
        font-size: 0.7rem;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 6px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .project-status.delivered {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .project-status.active {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    /* Delivered Project Styling - Removed status-based shadows */
    .delivered-project {
        position: relative;
        background: #ffffff;
        border: none !important;
    }

    .delivered-project:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12) !important;
    }

    /* Active Project Styling - Removed status-based shadows */
    .active-project {
        position: relative;
        background: #ffffff;
        border: none !important;
    }

    .active-project:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.12) !important;
    }

    /* Progress Section Styling */
    .progress-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border: 1px solid rgba(0,0,0,0.06);
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    }

    .progress-item {
        margin-bottom: 12px;
    }

    .progress-label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        color: #495057;
    }

    .progress-title {
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .progress-count {
        font-weight: 700;
        color: #007bff;
        background: rgba(0, 123, 255, 0.1);
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
    }

    /* Value Cards Container within Progress Section */
    .value-cards-container {
        padding: 8px 0 0 0;
    }

    /* Sub-card Styling - Updated with reduced font size */
    .sub-card {
        background: #ffffff;
        border: 1px solid rgba(0,0,0,0.06);
        border-radius: 6px;
        padding: 8px 4px;
        text-align: center;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .sub-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    }

    .sub-card-number {
        font-size: 1.5rem;
        font-weight: 800;
        line-height: 1;
        margin-bottom: 4px;
        color: #2c3e50;
    }

    .sub-card-label {
        font-size: 0.6rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        color: #6c757d;
    }

    /* Sub-card Color Variants - Using lighter backgrounds instead of borders */
    .pending-sub-card {
        background: rgba(220, 53, 69, 0.08);
        border: 1px solid rgba(220, 53, 69, 0.15);
    }

    .pending-sub-card .sub-card-number {
        color: #dc3545;
    }

    .pending-sub-card .sub-card-label {
        color: #dc3545;
    }

    .completed-sub-card {
        background: rgba(40, 167, 69, 0.08);
        border: 1px solid rgba(40, 167, 69, 0.15);
    }

    .completed-sub-card .sub-card-number {
        color: #28a745;
    }

    .completed-sub-card .sub-card-label {
        color: #28a745;
    }

    .resolved-sub-card {
        background: rgba(23, 162, 184, 0.08);
        border: 1px solid rgba(23, 162, 184, 0.15);
    }

    .resolved-sub-card .sub-card-number {
        color: #17a2b8;
    }

    .resolved-sub-card .sub-card-label {
        color: #17a2b8;
    }

    /* Grid Gap for Sub-cards */
    .g-2 > * {
        padding: 3px;
    }

    .project-info {
        background: rgba(255,255,255,0.9);
        border-radius: 8px;
        padding: 10px;
        margin-top: 8px;
        border: 1px solid rgba(0,0,0,0.08);
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .project-info small {
        color: #6c757d !important;
    }

    /* Select2 Custom Styling */
    .select2-container--default .select2-selection--single {
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        height: 38px;
        line-height: 36px;
        background-color: #fff;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #495057;
        padding-left: 12px;
        padding-right: 30px;
        font-size: 0.875rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
        right: 8px;
    }

    .select2-container--default .select2-selection--single .select2-selection__clear {
        color: #6c757d;
        margin-right: 20px;
    }

    .select2-container--default .select2-selection--single:focus,
    .select2-container--default.select2-container--open .select2-selection--single {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .select2-dropdown {
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #007bff;
    }

    .select2-container--default .select2-results__option[aria-selected=true] {
        background-color: #e9ecef;
    }

    .select2-container--default .select2-results__option {
        padding: 8px 12px;
        font-size: 0.875rem;
    }

    /* Responsive Select2 */
    @media (max-width: 768px) {
        .select2-container--default .select2-selection--single {
            height: 36px;
            line-height: 34px;
        }
        
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            font-size: 0.8rem;
            padding-left: 10px;
        }
        
        .select2-container--default .select2-results__option {
            padding: 6px 10px;
            font-size: 0.8rem;
        }
    }
</style>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-1 text-primary">
                                <i class="fas fa-project-diagram mr-2"></i>Projects Overview
                            </h4>
                            <p class="text-muted mb-0">Manage and track all your projects</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left mr-1"></i> Dashboard
                            </a>
                            <?php if (has_permission('clients/add')): ?>
                            <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=clients/add', 'Add New Client')"
                                    class="btn btn-outline-primary">
                                <i class="fas fa-plus mr-1"></i> Add Client
                            </button>
                            <?php endif; ?>
                            <?php if (has_permission('projects/add')): ?>
                            <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=projects/add', 'Add New Project')"
                                    class="btn btn-primary">
                                <i class="fas fa-plus mr-1"></i> Add Project
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Simplified Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
            <div class="stats-card stats-card-primary">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number"><?=count($list_items)?></div>
                        <div class="stats-label">Total Projects</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
            <div class="stats-card stats-card-danger">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number"><?=$status_count['pending_count']?></div>
                        <div class="stats-label">Pending Projects</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
            <div class="stats-card stats-card-warning">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number"><?=$status_count['assigned_count']?></div>
                        <div class="stats-label">In Progress</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
            <div class="stats-card stats-card-success">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number"><?=$status_count['completed_count']?></div>
                        <div class="stats-label">Completed</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Bar -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body py-3">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text bg-light border-right-0">
                                        <i class="fas fa-search text-muted"></i>
                                    </span>
                                </div>
                                <input type="text" class="form-control border-left-0" id="ta_search" 
                                       placeholder="Search projects by name, client, or type...">
                            </div>
                        </div>
                        <div class="col-md-8">
                            <form method="GET" action="<?= base_url('app/projects/index') ?>" class="row">
                                <div class="col-md-3">
                                    <select name="is_delivered" class="form-control select2">
                                        <option value="">All Projects</option>
                                        <option value="delivered" <?= ($filters['is_delivered'] == 'delivered') ? 'selected' : '' ?>>Delivered</option>
                                        <option value="not_delivered" <?= ($filters['is_delivered'] == 'not_delivered') ? 'selected' : '' ?>>Not Delivered</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select name="tester_id" class="form-control select2">
                                        <option value="">All Testers</option>
                                        <?php foreach ($testers as $id => $name): ?>
                                            <option value="<?= $id ?>" <?= ($filters['tester_id'] == $id) ? 'selected' : '' ?>><?= $name ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select name="task_status" class="form-control select2">
                                        <option value="">All Task Status</option>
                                        <option value="pending" <?= ($filters['task_status'] == 'pending') ? 'selected' : '' ?>>Tasks Pending</option>
                                        <option value="completed" <?= ($filters['task_status'] == 'completed') ? 'selected' : '' ?>>All Tasks Completed</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-filter mr-1"></i> Filter
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Projects Grid -->
    <div class="row">
        <?php if (isset($list_items) && isset($client_list) && isset($project_type) && isset($testers)): ?>
            <?php foreach ($list_items as $key => $item): ?>
                <div class="col-12 col-lg-4 col-md-6 mb-4 ta_search_item">
                    <div class="card h-100 shadow-sm border-0 project-card <?= $item['is_delivered'] == 1 ? 'delivered-project' : 'active-project' ?>">
                        <div class="card-body p-0">
                            <!-- Clickable wrapper for entire card content except action buttons -->
                            <div class="card-clickable-wrapper" onclick="window.location.href='<?= base_url('app/projects/view/'.$item['id']) ?>'">
                                <!-- Header Section with Status and Priority -->
                                <?php
                                $this->load->model('projects_m');
                                ?>
                                <div class="project-header p-4 pb-3">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <!-- Project Title (Clickable & Bold) -->
                                        <h4 class="card-title mb-0 font-weight-bold flex-grow-1 pr-2">
                                            <span class="text-dark project-title-link">
                                                <?= htmlspecialchars($item['title']) ?>
                                            </span>
                                        </h4>
                                        <div class="d-flex flex-column align-items-end">
                                            <!-- Status Badge -->
                                            <?php if($item['is_delivered'] == 1): ?>
                                                <span class="badge badge-success mb-1 project-status delivered">
                                                    <i class="fas fa-check-circle mr-1"></i>DELIVERED
                                                </span>
                                            <?php else: ?>
                                                <span class="badge badge-warning mb-1 project-status active">
                                                    <i class="fas fa-clock mr-1"></i>NOT DELIVERED
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Progress Bars Section with Value Cards - Combined -->
                                    <div class="progress-section mb-3">
                                        <div class="row">
                                            <!-- Tasks Progress with Value Cards -->
                                            <div class="col-6">
                                                <div class="progress-item">
                                                    <div class="progress-label">
                                                        <i class="fas fa-tasks mr-1 text-primary"></i>
                                                        <span class="progress-title">Tasks</span>
                                                        <span class="progress-count"><?= $item['tasks']['completed'] ?>/<?= $item['tasks']['total'] ?></span>
                                                    </div>
                                                    <div class="progress">
                                                        <div class="progress-bar bg-success" role="progressbar" 
                                                             style="width: <?= $item['tasks']['progress'] ?>%" 
                                                             aria-valuenow="<?= $item['tasks']['progress'] ?>" 
                                                             aria-valuemin="0" aria-valuemax="100">
                                                        </div>
                                                    </div>
                                                    <!-- Value Cards for Tasks -->
                                                    <div class="value-cards-container mt-3">
                                                        <div class="row g-2">
                                                            <!-- Pending Tasks Sub-card -->
                                                            <div class="col-6">
                                                                <div class="sub-card pending-sub-card">
                                                                    <div class="sub-card-number"><?= $item['tasks']['total'] - $item['tasks']['completed'] ?></div>
                                                                    <div class="sub-card-label">Pending</div>
                                                                </div>
                                                            </div>
                                                            <!-- Completed Tasks Sub-card -->
                                                            <div class="col-6">
                                                                <div class="sub-card completed-sub-card">
                                                                    <div class="sub-card-number"><?= $item['tasks']['completed'] ?></div>
                                                                    <div class="sub-card-label">Completed</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Tickets Progress with Value Cards -->
                                            <div class="col-6">
                                                <div class="progress-item">
                                                    <div class="progress-label">
                                                        <i class="fas fa-ticket-alt mr-1 text-info"></i>
                                                        <span class="progress-title">Tickets</span>
                                                        <span class="progress-count"><?= $item['tickets']['total'] - $item['tickets']['pending'] ?>/<?= $item['tickets']['total'] ?></span>
                                                    </div>
                                                    <div class="progress">
                                                        <div class="progress-bar bg-info" role="progressbar" 
                                                             style="width: <?= $item['tickets']['total'] > 0 ? round((($item['tickets']['total'] - $item['tickets']['pending']) / $item['tickets']['total']) * 100) : 0 ?>%" 
                                                             aria-valuenow="<?= $item['tickets']['total'] > 0 ? round((($item['tickets']['total'] - $item['tickets']['pending']) / $item['tickets']['total']) * 100) : 0 ?>" 
                                                             aria-valuemin="0" aria-valuemax="100">
                                                        </div>
                                                    </div>
                                                    <!-- Value Cards for Tickets -->
                                                    <div class="value-cards-container mt-3">
                                                        <div class="row g-2">
                                                            <!-- Pending Tickets Sub-card -->
                                                            <div class="col-6">
                                                                <div class="sub-card pending-sub-card">
                                                                    <div class="sub-card-number"><?= $item['tickets']['pending'] ?></div>
                                                                    <div class="sub-card-label">Pending</div>
                                                                </div>
                                                            </div>
                                                            <!-- Resolved Tickets Sub-card -->
                                                            <div class="col-6">
                                                                <div class="sub-card resolved-sub-card">
                                                                    <div class="sub-card-number"><?= $item['tickets']['total'] - $item['tickets']['pending'] ?></div>
                                                                    <div class="sub-card-label">Resolved</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Project Info -->
                                    <div class="project-info">
                                        <div class="row">
                                            <div class="col-12 mb-2">
                                                <small class="text-muted">
                                                    <i class="fas fa-building mr-2 text-primary"></i>
                                                    <strong><?= $client_list[$item['client_id']] ?></strong> • <?= $project_type[$item['project_type']] ?>
                                                </small>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar-alt mr-1 text-info"></i>
                                                    <?= DateTime::createFromFormat('Y-m-d H:i:s', $item['created_on'])->format('M d, Y') ?>
                                                </small>
                                            </div>
                                            <div class="col-6">
                                                <small class="tester-info">
                                                    <i class="fas fa-user-check mr-1 <?= ($testers[$item['tester_id']] && $all_users[$item['tester_id']] == 1) ? 'text-success' : 'text-danger' ?>"></i>
                                                    <span class="<?= ($testers[$item['tester_id']] && $all_users[$item['tester_id']] == 1) ? 'text-success' : 'text-danger' ?>">
                                                        <?= ($testers[$item['tester_id']] && $all_users[$item['tester_id']] == 1) ? $testers[$item['tester_id']] : 'Not Assigned' ?>
                                                    </span>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Content Section - Action Buttons (Non-clickable) -->
                            <div class="project-content p-3 pt-2 pb-2">
                                <!-- Quick Action Buttons - All in One Row -->
                                <div class="action-buttons">
                                    <div class="row g-2">
                                        <!-- View Button -->
                                        <div class="col-4">
                                            <a href="<?= base_url('app/projects/view/'.$item['id']) ?>"
                                               class="btn btn-info btn-sm w-100 action-btn">
                                                <i class="fas fa-eye"></i><span class="btn-text"> View</span>
                                            </a>
                                        </div>

                                        <!-- Edit Button - Only if user has permission -->
                                        <?php if (has_permission('projects/edit')): ?>
                                        <div class="col-4">
                                            <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=projects/edit'); ?>', 'Edit Project')"
                                                    class="btn btn-outline-info btn-sm w-100 action-btn">
                                                <i class="fas fa-pencil-alt"></i><span class="btn-text"> Edit</span>
                                            </button>
                                        </div>
                                        <?php endif; ?>

                                        <!-- Add Task Button - Only if user has permission -->
                                        <?php if (has_permission('tasks/add')): ?>
                                        <div class="col-4">
                                            <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add&project_id=<?= $item['id'] ?>', 'Add Task')"
                                                    class="btn btn-outline-secondary btn-sm w-100 action-btn add-task-btn">
                                                <i class="fas fa-plus"></i><span class="btn-text"> Task</span>
                                            </button>
                                        </div>
                                        <?php endif; ?>

                                        <!-- Delete Button - Only if user has permission (moved to second row if needed) -->
                                        <?php if (has_permission('projects/delete')): ?>
                                        <div class="col-12 mt-2">
                                            <button onclick="confirm_modal('<?=base_url("app/projects/delete/{$item['id']}/");?>')"
                                                    class="btn btn-outline-danger btn-sm w-100 action-btn">
                                                <i class="fas fa-trash"></i><span class="btn-text"> Delete Project</span>
                                            </button>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-project-diagram fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">No Projects Found</h4>
                    <p class="text-muted">Start by creating your first project</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- No Results Message -->
    <div id="ta_no_result" class="text-center p-4" style="display: none">
        <div class="card shadow-sm">
            <div class="card-body py-5">
                <i class="fas fa-search fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No Results Found</h4>
                <p class="text-muted">Try adjusting your search criteria</p>
            </div>
        </div>
    </div>
</div>



<script>
$(document).ready(function() {
    // Search functionality
    $('#ta_search').on('keyup', function() {
        var searchText = $(this).val().toLowerCase();
        var hasResults = false;
        
        $('.ta_search_item').each(function() {
            var projectText = $(this).text().toLowerCase();
            if (projectText.indexOf(searchText) > -1) {
                $(this).show();
                hasResults = true;
            } else {
                $(this).hide();
            }
        });
        
        // Show/hide no results message
        if (hasResults || searchText === '') {
            $('#ta_no_result').hide();
        } else {
            $('#ta_no_result').show();
        }
    });

    // Initialize Select2 for all select elements
    $('.select2').select2({
        placeholder: "Select an option",
        allowClear: true,
        width: '100%'
    });
});
</script>
