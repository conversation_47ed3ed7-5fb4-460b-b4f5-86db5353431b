<style>
    /* Professional Card Styles - Completely Redesigned for Better List View */
    .project-card {
        transition: box-shadow 0.3s ease;
        border: none !important;
        border-radius: 16px !important;
        overflow: hidden;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        box-shadow: 0 2px 8px rgba(0,0,0,0.06), 0 1px 3px rgba(0,0,0,0.08) !important;
        margin-bottom: 0.75rem;
        position: relative;
        cursor: pointer;
    }

    .project-card:hover {
        box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15), 0 2px 8px rgba(0,0,0,0.1) !important;
        transform: translateY(-2px);
    }

    /* Main Project Row Layout */
    .project-row {
        display: flex;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid rgba(0,0,0,0.06);
    }

    .project-row:last-child {
        border-bottom: none;
    }

    /* Project Title Section */
    .project-title-section {
        flex: 2;
        min-width: 0;
        padding-right: 1.5rem;
    }

    .project-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        line-height: 1.3;
        text-decoration: none;
        transition: color 0.2s ease;
    }

    .project-title:hover {
        color: #007bff;
        text-decoration: none;
    }

    .project-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 0.85rem;
        color: #6c757d;
        flex-wrap: wrap;
    }

    .project-meta-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .project-meta-item i {
        font-size: 0.8rem;
    }

    /* Status Section */
    .project-status-section {
        flex: 0 0 140px;
        text-align: center;
        padding: 0 1rem;
    }

    .status-badge {
        display: inline-block;
        padding: 0.4rem 0.8rem;
        border-radius: 6px;
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-delivered {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .status-active {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;
    }

    /* Progress Section */
    .project-progress-section {
        flex: 1;
        padding: 0 1rem;
    }

    .progress-row {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0.5rem;
    }

    .progress-row:last-child {
        margin-bottom: 0;
    }

    .progress-label {
        flex: 0 0 60px;
        font-size: 0.8rem;
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .progress-bar-container {
        flex: 1;
        min-width: 0;
    }

    .progress {
        height: 6px;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 0.25rem;
    }

    .progress-bar {
        height: 100%;
        border-radius: 3px;
        transition: width 0.6s ease;
    }

    .progress-count {
        font-size: 0.75rem;
        font-weight: 600;
        color: #007bff;
        text-align: right;
    }

    /* Actions Section */
    .project-actions-section {
        flex: 0 0 200px;
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
        padding-left: 1rem;
    }

    .action-btn {
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        text-decoration: none;
    }

    .btn-view {
        background: #007bff;
        color: white;
    }

    .btn-view:hover {
        background: #0056b3;
        color: white;
        box-shadow: 0 3px 8px rgba(0, 123, 255, 0.3);
    }

    .btn-edit {
        background: transparent;
        color: #17a2b8;
        border-color: #17a2b8;
    }

    .btn-edit:hover {
        background: #17a2b8;
        color: white;
        box-shadow: 0 3px 8px rgba(23, 162, 184, 0.3);
    }

    .btn-task {
        background: transparent;
        color: #6c757d;
        border-color: #6c757d;
    }

    .btn-task:hover {
        background: #6c757d;
        color: white;
        box-shadow: 0 3px 8px rgba(108, 117, 125, 0.3);
    }

    .btn-delete {
        background: transparent;
        color: #dc3545;
        border-color: #dc3545;
    }

    .btn-delete:hover {
        background: #dc3545;
        color: white;
        box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3);
    }

    /* Enhanced Responsive Design */
    @media (max-width: 1200px) {
        .project-card-container {
            flex-direction: column;
            align-items: stretch;
        }
        
        .priority-indicator {
            width: 100%;
            height: 4px;
        }
        
        .progress-section {
            flex-direction: row;
            padding: 1rem;
            min-width: auto;
            border-right: none;
            border-bottom: 2px solid rgba(0,0,0,0.05);
        }
        
        .content-section {
            padding: 1.25rem;
        }
        
        .actions-section {
            min-width: auto;
            max-width: none;
            border-left: none;
            border-top: 2px solid rgba(0,0,0,0.05);
        }
        
        .analytics-grid {
            grid-template-columns: 1fr 1fr;
        }
    }

    @media (max-width: 768px) {
        .project-card {
            margin-bottom: 1rem;
        }
        
        .progress-section {
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .project-meta-grid {
            grid-template-columns: 1fr;
            gap: 0.5rem;
        }
        
        .analytics-grid {
            grid-template-columns: 1fr;
            gap: 0.5rem;
        }
        
        .action-buttons {
            grid-template-columns: 1fr 1fr;
            display: grid;
        }
        
        .content-section {
            padding: 1rem;
        }
        
        .actions-section {
            padding: 1rem;
        }
    }
    
    @media (max-width: 480px) {
        .action-buttons {
            grid-template-columns: 1fr;
        }
        
        .project-header h3 {
            font-size: 1.1rem;
        }
        
        .metric-card {
            padding: 0.75rem;
        }
    }

    /* Simplified Statistics Cards */
    .stats-card {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: none;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .stats-card-body {
        padding: 1.5rem;
        display: flex;
        align-items: center;
    }

    .stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-right: 1rem;
    }

    .stats-content {
        flex: 1;
    }

    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.25rem;
    }

    .stats-label {
        font-size: 0.9rem;
        font-weight: 600;
        color: #64748b;
    }

    /* Card Variants */
    .stats-card-primary .stats-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .stats-card-primary .stats-number {
        color: #667eea;
    }

    .stats-card-danger .stats-icon {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
    }

    .stats-card-danger .stats-number {
        color: #ff6b6b;
    }

    .stats-card-warning .stats-icon {
        background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        color: white;
    }

    .stats-card-warning .stats-number {
        color: #feca57;
    }

    .stats-card-success .stats-icon {
        background: linear-gradient(135deg, #48cab2 0%, #2dd4bf 100%);
        color: white;
    }

    .stats-card-success .stats-number {
        color: #48cab2;
    }

    /* Search Bar Enhancement */
    .input-group-text {
        border: none;
        background: transparent;
    }

    .form-control {
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        font-size: 0.875rem;
    }

    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    select.form-control {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    /* Enhanced Utility Classes */
    .gap-2 > * + * {
        margin-left: 0.5rem;
    }

    .shadow-lg {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    }
    
    /* Project Card Enhanced Animations & Interactivity */
    .project-card-enhanced {
        position: relative;
        overflow: hidden;
    }
    
    
    
    
    /* Typography Improvements */
    .metric-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.12);
    }
    
    /* Loading States */
    .project-card.loading {
        opacity: 0.7;
        pointer-events: none;
    }
    
    /* Focus States for Accessibility */
    
    .action-buttons button:focus,
    .action-buttons a:focus {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
    }
    

    /* Select2 Custom Styling */
    .select2-container--default .select2-selection--single {
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        height: 38px;
        line-height: 36px;
        background-color: #fff;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #495057;
        padding-left: 12px;
        padding-right: 30px;
        font-size: 0.875rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
        right: 8px;
    }

    .select2-container--default .select2-selection--single .select2-selection__clear {
        color: #6c757d;
        margin-right: 20px;
    }

    .select2-container--default .select2-selection--single:focus,
    .select2-container--default.select2-container--open .select2-selection--single {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .select2-dropdown {
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #007bff;
    }

    .select2-container--default .select2-results__option[aria-selected=true] {
        background-color: #e9ecef;
    }

    .select2-container--default .select2-results__option {
        padding: 8px 12px;
        font-size: 0.875rem;
    }

    /* Responsive Select2 */
    @media (max-width: 768px) {
        .select2-container--default .select2-selection--single {
            height: 36px;
            line-height: 34px;
        }
        
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            font-size: 0.8rem;
            padding-left: 10px;
        }
        
        .select2-container--default .select2-results__option {
            padding: 6px 10px;
            font-size: 0.8rem;
        }
    }
</style>
<div class="container-fluid" style="padding:16px!important">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-1 text-primary">
                                <i class="fas fa-project-diagram mr-2"></i>Projects Overview
                            </h4>
                            <p class="text-muted mb-0">Manage and track all your projects</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?= base_url("app/dashboard/index"); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left mr-1"></i> Dashboard
                            </a>
                            <?php if (has_permission('clients/add')): ?>
                            <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=clients/add', 'Add New Client')"
                                    class="btn btn-outline-primary">
                                <i class="fas fa-plus mr-1"></i> Add Client
                            </button>
                            <?php endif; ?>
                            <?php if (has_permission('projects/add')): ?>
                            <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=projects/add', 'Add New Project')"
                                    class="btn btn-primary">
                                <i class="fas fa-plus mr-1"></i> Add Project
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Simplified Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
            <div class="stats-card stats-card-primary">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number"><?=count($list_items)?></div>
                        <div class="stats-label">Total Projects</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
            <div class="stats-card stats-card-danger">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number"><?=$status_count['pending_count']?></div>
                        <div class="stats-label">Pending Projects</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
            <div class="stats-card stats-card-warning">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number"><?=$status_count['assigned_count']?></div>
                        <div class="stats-label">In Progress</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
            <div class="stats-card stats-card-success">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number"><?=$status_count['completed_count']?></div>
                        <div class="stats-label">Completed</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Bar -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body py-3">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text bg-light border-right-0">
                                        <i class="fas fa-search text-muted"></i>
                                    </span>
                                </div>
                                <input type="text" class="form-control border-left-0" id="ta_search" 
                                       placeholder="Search projects by name, client, or type...">
                            </div>
                        </div>
                        <div class="col-md-8">
                            <form method="GET" action="<?= base_url('app/projects/index') ?>" class="row">
                                <div class="col-md-3">
                                    <select name="is_delivered" class="form-control select2">
                                        <option value="">All Projects</option>
                                        <option value="delivered" <?= ($filters['is_delivered'] == 'delivered') ? 'selected' : '' ?>>Delivered</option>
                                        <option value="not_delivered" <?= ($filters['is_delivered'] == 'not_delivered') ? 'selected' : '' ?>>Not Delivered</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select name="tester_id" class="form-control select2">
                                        <option value="">All Testers</option>
                                        <?php foreach ($testers as $id => $name): ?>
                                            <option value="<?= $id ?>" <?= ($filters['tester_id'] == $id) ? 'selected' : '' ?>><?= $name ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select name="task_status" class="form-control select2">
                                        <option value="">All Task Status</option>
                                        <option value="pending" <?= ($filters['task_status'] == 'pending') ? 'selected' : '' ?>>Tasks Pending</option>
                                        <option value="completed" <?= ($filters['task_status'] == 'completed') ? 'selected' : '' ?>>All Tasks Completed</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-filter mr-1"></i> Filter
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Projects List -->
    <div class="row">
        <?php if (isset($list_items) && isset($client_list) && isset($project_type) && isset($testers)): ?>
            <?php foreach ($list_items as $key => $item): ?>
                <div class="col-12 mb-2 ta_search_item">
                    <div class="card project-card p-0 project-card-enhanced" 
                         style="position: relative; overflow: visible;">
                        <div class="project-card-container" style="display: flex; align-items: stretch; background: linear-gradient(135deg, #ffffff 0%, #f8f9fb 100%); border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.08); border-left: 4px solid <?= ($item['tasks']['total'] - $item['tasks']['completed']) > 0 ? '#e74c3c' : '#27ae60' ?>;">
                            <!-- Priority Indicator -->
                            <div class="priority-indicator" style="width: 6px; background: linear-gradient(180deg, <?= ($item['tasks']['total'] - $item['tasks']['completed']) > 0 ? '#e74c3c' : '#27ae60' ?> 0%, <?= ($item['tasks']['total'] - $item['tasks']['completed']) > 0 ? '#c0392b' : '#229954' ?> 100%); flex-shrink: 0;"></div>
                            
                            <!-- Compact Progress Section -->
                            <div class="progress-section" style="display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 1rem; background: linear-gradient(135deg, <?= ($item['tasks']['total'] - $item['tasks']['completed']) > 0 ? '#fef2f2' : '#f0fff4' ?> 0%, <?= ($item['tasks']['total'] - $item['tasks']['completed']) > 0 ? '#fee2e2' : '#e6ffed' ?> 100%); border-right: 2px solid rgba(0,0,0,0.05); min-width: 100px; position: relative;">
                                <!-- Smaller Progress Circle -->
                                <div style="position: relative; width: 50px; height: 50px; margin-bottom: 0.3rem;">
                                    <svg width="50" height="50" style="transform: rotate(-90deg);">
                                        <circle cx="25" cy="25" r="22" stroke="rgba(0,0,0,0.1)" stroke-width="3" fill="none"/>
                                        <circle cx="25" cy="25" r="22" stroke="<?= ($item['tasks']['total'] - $item['tasks']['completed']) > 0 ? '#e74c3c' : '#27ae60' ?>" stroke-width="3" fill="none" 
                                                stroke-dasharray="<?= 2 * 3.14159 * 22 ?>" 
                                                stroke-dashoffset="<?= 2 * 3.14159 * 22 * (1 - ($item['tasks']['total'] > 0 ? $item['tasks']['progress'] / 100 : 0)) ?>"
                                                style="transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);"/>
                                    </svg>
                                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                                        <div style="font-size: 0.9rem; font-weight: 800; color: <?= ($item['tasks']['total'] - $item['tasks']['completed']) > 0 ? '#e74c3c' : '#27ae60' ?>; line-height: 1;">
                                            <?= $item['tasks']['total'] > 0 ? round($item['tasks']['progress']) : 0 ?>%
                                        </div>
                                    </div>
                                </div>
                                <!-- Compact Progress Label -->
                                <div style="text-align: center;">
                                    <div style="font-size: 0.65rem; color: #64748b; font-weight: 600; margin-bottom: 0.1rem;">Progress</div>
                                    <div style="font-size: 0.6rem; color: #94a3b8; font-weight: 500;"><?= $item['tasks']['completed'] ?>/<?= $item['tasks']['total'] ?></div>
                                </div>
                            </div>

                            <!-- Compact Main Content Section -->
                            <div class="content-section" style="flex: 1; padding: 1rem; min-width: 0;">
                                <!-- Project Title -->
                                <div class="project-header" style="margin-bottom: 0.75rem;">
                                    <h3 style="font-size: 1.2rem; font-weight: 700; color: #1e293b; margin: 0 0 0.4rem 0; line-height: 1.3;">
                                        <a href="<?= base_url('app/projects/view/'.$item['id']) ?>" style="color: #1e293b; text-decoration: none; transition: color 0.2s ease;" onmouseover="this.style.color='#3b82f6'" onmouseout="this.style.color='#1e293b'">
                                            <?= htmlspecialchars($item['title']) ?>
                                        </a>
                                    </h3>
                                    <!-- Status Badge -->
                                    <div style="display: inline-block;">
                                        <?php if($item['is_delivered'] == 1): ?>
                                            <span style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; font-weight: 600; font-size: 0.7rem; padding: 0.25rem 0.7rem; border-radius: 16px; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);">✓ Delivered</span>
                                        <?php else: ?>
                                            <span style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; font-weight: 600; font-size: 0.7rem; padding: 0.25rem 0.7rem; border-radius: 16px; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);">In Progress</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- Compact Project Meta Information -->
                                <div class="project-meta-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin-bottom: 0.75rem;">
                                    <div style="display: flex; align-items: center; gap: 0.4rem; font-size: 0.8rem; color: #64748b;">
                                        <i class="fas fa-user" style="color: #6366f1; width: 12px; font-size: 0.7rem;"></i>
                                        <span style="font-weight: 500;">Client:</span>
                                        <span style="color: #334155; font-weight: 600; font-size: 0.8rem;"><?= $client_list[$item['client_id']] ?></span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 0.4rem; font-size: 0.8rem; color: #64748b;">
                                        <i class="fas fa-tag" style="color: #8b5cf6; width: 12px; font-size: 0.7rem;"></i>
                                        <span style="font-weight: 500;">Type:</span>
                                        <span style="color: #334155; font-weight: 600; font-size: 0.8rem;"><?= $project_type[$item['project_type']] ?></span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 0.4rem; font-size: 0.8rem; color: #64748b;">
                                        <i class="fas fa-calendar" style="color: #06b6d4; width: 12px; font-size: 0.7rem;"></i>
                                        <span style="font-weight: 500;">Created:</span>
                                        <span style="color: #334155; font-weight: 600; font-size: 0.8rem;"><?= DateTime::createFromFormat('Y-m-d H:i:s', $item['created_on'])->format('M d, Y') ?></span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 0.4rem; font-size: 0.8rem; color: #64748b;">
                                        <i class="fas fa-user-check" style="color: #10b981; width: 12px; font-size: 0.7rem;"></i>
                                        <span style="font-weight: 500;">Tester:</span>
                                        <?php if ($testers[$item['tester_id']]): ?>
                                            <span style="color: <?= $all_users[$item['tester_id']] == 1 ? '#059669' : '#dc2626' ?>; font-weight: 700; font-size: 0.8rem;">
                                                <?= $testers[$item['tester_id']] ?> 
                                                <i class="fas fa-circle" style="font-size: 0.35rem; margin-left: 0.2rem; color: <?= $all_users[$item['tester_id']] == 1 ? '#10b981' : '#ef4444' ?>;"></i>
                                            </span>
                                        <?php else: ?>
                                            <span style="color: #dc2626; font-weight: 700; font-size: 0.8rem;">Not Assigned</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Compact Analytics & Actions Section -->
                            <div class="actions-section" style="display: flex; flex-direction: column; justify-content: space-between; padding: 1rem; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-left: 2px solid rgba(0,0,0,0.05); min-width: 220px; max-width: 280px;">
                                <!-- Compact Analytics Cards -->
                                <div class="analytics-grid" style="display: flex; gap: 0.5rem; margin-bottom: 1rem;">
                                    <!-- Compact Tasks Card -->
                                    <div class="metric-card" style="background: <?= ($item['tasks']['total'] - $item['tasks']['completed']) > 0 ? '#fef2f2' : '#ecfdf5' ?>; border: 1px solid <?= ($item['tasks']['total'] - $item['tasks']['completed']) > 0 ? '#fecaca' : '#a7f3d0' ?>; border-radius: 8px; padding: 0.5rem; flex: 1; position: relative;">
                                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 0.2rem;">
                                            <div style="font-size: 0.65rem; font-weight: 600; color: #64748b; text-transform: uppercase;">Tasks</div>
                                            <div style="width: 6px; height: 6px; background: <?= ($item['tasks']['total'] - $item['tasks']['completed']) > 0 ? '#ef4444' : '#22c55e' ?>; border-radius: 50%;"></div>
                                        </div>
                                        <div style="font-size: 1.1rem; font-weight: 700; color: <?= ($item['tasks']['total'] - $item['tasks']['completed']) > 0 ? '#dc2626' : '#059669' ?>; line-height: 1;">
                                            <?= $item['tasks']['completed'] ?><span style="font-size: 0.8rem; color: #64748b;">/<?= $item['tasks']['total'] ?></span>
                                        </div>
                                    </div>
                                    
                                    <!-- Compact Tickets Card -->
                                    <div class="metric-card" style="background: <?= $item['tickets']['pending'] > 0 ? '#fef2f2' : '#ecfdf5' ?>; border: 1px solid <?= $item['tickets']['pending'] > 0 ? '#fecaca' : '#a7f3d0' ?>; border-radius: 8px; padding: 0.5rem; flex: 1; position: relative;">
                                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 0.2rem;">
                                            <div style="font-size: 0.65rem; font-weight: 600; color: #64748b; text-transform: uppercase;">Tickets</div>
                                            <div style="width: 6px; height: 6px; background: <?= $item['tickets']['pending'] > 0 ? '#ef4444' : '#22c55e' ?>; border-radius: 50%;"></div>
                                        </div>
                                        <div style="font-size: 1.1rem; font-weight: 700; color: <?= $item['tickets']['pending'] > 0 ? '#dc2626' : '#059669' ?>; line-height: 1;">
                                            <?= $item['tickets']['total'] - $item['tickets']['pending'] ?><span style="font-size: 0.8rem; color: #64748b;">/<?= $item['tickets']['total'] ?></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Compact Action Buttons -->
                                <div class="action-buttons" style="display: flex; flex-wrap: wrap; gap: 0.4rem; position: relative; z-index: 10;">
                                    <a href="<?= base_url('app/projects/view/'.$item['id']) ?>" 
                                       style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white; text-decoration: none; padding: 0.5rem 0.8rem; border-radius: 6px; font-size: 0.75rem; font-weight: 600; display: flex; align-items: center; gap: 0.3rem; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3); flex: 1; justify-content: center; min-width: 70px;"
                                       onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(59, 130, 246, 0.4)'"
                                       onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(59, 130, 246, 0.3)'">
                                        <i class="fas fa-eye" style="font-size: 0.7rem;"></i> View
                                    </a>
                                    
                                    <?php if (has_permission('projects/edit')): ?>
                                        <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=projects/edit'); ?>', 'Edit Project')" 
                                                style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; border: none; padding: 0.5rem 0.8rem; border-radius: 6px; font-size: 0.75rem; font-weight: 600; display: flex; align-items: center; gap: 0.3rem; transition: all 0.3s ease; cursor: pointer; box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3); flex: 1; justify-content: center; min-width: 70px;"
                                                onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(16, 185, 129, 0.4)'"
                                                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(16, 185, 129, 0.3)'">
                                            <i class="fas fa-edit" style="font-size: 0.7rem;"></i> Edit
                                        </button>
                                    <?php endif; ?>
                                    
                                    <?php if (has_permission('tasks/add')): ?>
                                        <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=tasks/add&project_id=<?= $item['id'] ?>', 'Add Task')" 
                                                style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white; border: none; padding: 0.5rem 0.8rem; border-radius: 6px; font-size: 0.75rem; font-weight: 600; display: flex; align-items: center; gap: 0.3rem; transition: all 0.3s ease; cursor: pointer; box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3); flex: 1; justify-content: center; min-width: 80px;"
                                                onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(139, 92, 246, 0.4)'"
                                                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(139, 92, 246, 0.3)'">
                                            <i class="fas fa-plus" style="font-size: 0.7rem;"></i> Task
                                        </button>
                                    <?php endif; ?>
                                    
                                    <?php if (has_permission('projects/delete')): ?>
                                        <button onclick="confirm_modal('<?=base_url("app/projects/delete/{$item['id']}/");?>')" 
                                                style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; border: none; padding: 0.5rem 0.8rem; border-radius: 6px; font-size: 0.75rem; font-weight: 600; display: flex; align-items: center; gap: 0.3rem; transition: all 0.3s ease; cursor: pointer; box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3); flex: 1; justify-content: center; min-width: 75px;"
                                                onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(239, 68, 68, 0.4)'"
                                                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(239, 68, 68, 0.3)'">
                                            <i class="fas fa-trash" style="font-size: 0.7rem;"></i> Delete
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-project-diagram fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">No Projects Found</h4>
                    <p class="text-muted">Start by creating your first project</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- No Results Message -->
    <div id="ta_no_result" class="text-center p-4" style="display: none">
        <div class="card shadow-sm">
            <div class="card-body py-5">
                <i class="fas fa-search fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">No Results Found</h4>
                <p class="text-muted">Try adjusting your search criteria</p>
            </div>
        </div>
    </div>
</div>



<script>
$(document).ready(function() {
    // Enhanced search functionality with animations
    $('#ta_search').on('keyup', function() {
        var searchText = $(this).val().toLowerCase();
        var hasResults = false;
        
        $('.ta_search_item').each(function() {
            var $item = $(this);
            var projectText = $item.text().toLowerCase();
            
            if (projectText.indexOf(searchText) > -1) {
                $item.fadeIn(300);
                hasResults = true;
            } else {
                $item.fadeOut(300);
            }
        });
        
        // Show/hide no results message with animation
        setTimeout(function() {
            if (hasResults || searchText === '') {
                $('#ta_no_result').fadeOut(200);
            } else {
                $('#ta_no_result').fadeIn(200);
            }
        }, 300);
    });

    // Initialize Select2 for all select elements
    $('.select2').select2({
        placeholder: "Select an option",
        allowClear: true,
        width: '100%'
    });
    
    // Enhanced hover effects for project cards
    $('.project-card').hover(
        function() {
            $(this).addClass('card-hover-active');
        },
        function() {
            $(this).removeClass('card-hover-active');
        }
    );
    
    // Smooth scroll to card when clicking on progress circle
    $('.progress-section').click(function() {
        var $card = $(this).closest('.project-card');
        $('html, body').animate({
            scrollTop: $card.offset().top - 100
        }, 500);
    });
    
    // Add loading state to action buttons
    $('.action-buttons button, .action-buttons a').click(function() {
        var $btn = $(this);
        var originalText = $btn.html();
        
        $btn.html('<i class="fas fa-spinner fa-spin"></i> Loading...');
        $btn.prop('disabled', true);
        
        // Reset after 2 seconds (adjust based on your modal/page loading time)
        setTimeout(function() {
            $btn.html(originalText);
            $btn.prop('disabled', false);
        }, 2000);
    });
    
    // Intersection Observer for card animations on scroll
    if ('IntersectionObserver' in window) {
        const cardObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });
        
        $('.project-card').each(function() {
            this.style.opacity = '0';
            this.style.transform = 'translateY(20px)';
            this.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            cardObserver.observe(this);
        });
    }
    
    // Keyboard navigation for accessibility
    $('.project-card').attr('tabindex', '0').keydown(function(e) {
        if (e.keyCode === 13 || e.keyCode === 32) { // Enter or Space
            e.preventDefault();
            $(this).find('.action-buttons a').first().click();
        }
    });
    
    // Make entire project card clickable
    $('.project-card').click(function(e) {
        // Don't trigger if clicking on action buttons
        if ($(e.target).closest('.action-buttons').length > 0) {
            return;
        }
        
        // Find the View link and navigate to it
        var viewLink = $(this).find('.action-buttons a[href*="view"]').first();
        if (viewLink.length > 0) {
            window.location.href = viewLink.attr('href');
        }
    });
});
</script>
