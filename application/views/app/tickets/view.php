<div class="container-fluid">
    <div style="padding-bottom: 10px;margin:14px;" class="clearfix shadow-pro p-2">
        <a href="<?= base_url("app/tickets/index"); ?>" class="btn btn-outline-secondary btn-mini pull-left">
            <i class="fas fa-arrow-circle-left"></i> Back to Tickets
        </a>
        
        <?php if (has_permission('tickets/edit')): ?>
            <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$ticket['id'].'/?page_name=tickets/edit'); ?>', 'Edit Ticket')"
                    class="btn btn-info btn-mini float-right">
                <small><i class="fas fa-pencil-alt"></i></small> Edit Ticket
            </button>
        <?php endif; ?>
    </div>
    
    <div class="row" style="margin:14px;">
        <!-- Ticket Details Card -->
        <div class="col-md-8">
            <div class="card card-primary shadow-pro">
                <div class="card-header">
                    <h3 class="card-title">Ticket Details</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <h4 class="text-primary"><?= $ticket['title'] ?></h4>
                            <hr>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Project:</strong><br>
                            <span class="text-muted"><?= $ticket['project_title'] ?? 'N/A' ?></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Type:</strong><br>
                            <?= get_task_type($ticket['type']) ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Priority:</strong><br>
                            <?= get_task_priority($ticket['priority']) ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Status:</strong><br>
                            <?php
                            switch($ticket['status']) {
                                case 'new':
                                    echo '<span class="badge badge-info badge-lg">NEW</span>';
                                    break;
                                case 'assigned':
                                    echo '<span class="badge badge-warning badge-lg">ASSIGNED</span>';
                                    break;
                                case 'closed':
                                    echo '<span class="badge badge-success badge-lg">CLOSED</span>';
                                    break;
                                case 'on_hold':
                                    echo '<span class="badge badge-secondary badge-lg">ON HOLD</span>';
                                    break;
                                case 're_open':
                                    echo '<span class="badge badge-danger badge-lg">RE-OPEN</span>';
                                    break;
                                default:
                                    echo '<span class="badge badge-light badge-lg">UNKNOWN</span>';
                            }
                            ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Assigned To:</strong><br>
                            <span class="text-muted"><?= $ticket['assigned_user_name'] ?? 'Unassigned' ?></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Reported By:</strong><br>
                            <span class="text-muted"><?= $ticket['reported_by'] ?? 'N/A' ?></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Reported To:</strong><br>
                            <span class="text-muted"><?= isset($users[$ticket['reported_to']]) ? $users[$ticket['reported_to']] : 'N/A' ?></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Ticket Via:</strong><br>
                            <span class="text-muted"><?= ucfirst($ticket['ticket_via'] ?? 'N/A') ?></span>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <strong>Ticket Date:</strong><br>
                            <span class="text-muted"><?= (!empty($ticket['ticket_date']) && $ticket['ticket_date'] !== '0000-00-00 00:00:00') ? date('d-m-Y H:i', strtotime($ticket['ticket_date'])) : 'N/A' ?></span>
                        </div>
                        
                        <?php if (!empty($ticket['close_date']) && $ticket['close_date'] !== '0000-00-00 00:00:00'): ?>
                        <div class="col-md-6 mb-3">
                            <strong>Close Date:</strong><br>
                            <span class="text-muted"><?= date('d-m-Y H:i', strtotime($ticket['close_date'])) ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($ticket['closed_by']): ?>
                        <div class="col-md-6 mb-3">
                            <strong>Closed By:</strong><br>
                            <span class="text-muted"><?= isset($users[$ticket['closed_by']]) ? $users[$ticket['closed_by']] : 'N/A' ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($ticket['description'])): ?>
                        <div class="col-12">
                            <strong>Description:</strong><br>
                            <div class="bg-light p-3 rounded mt-2">
                                <?= nl2br(htmlspecialchars($ticket['description'])) ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <!-- Comments/History Section -->
            <div class="col-12 mt-4">
                <div class="card card-success shadow-pro">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-comments"></i> Comments & History
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- Add Comment Form -->
                        <?php if (has_permission('tickets/edit')): ?>
                        <div class="mb-4">
                            <form id="add_comment_form" class="border rounded p-3 bg-light">
                                <div class="form-group">
                                    <label for="comment_text"><strong>Add Comment:</strong></label>
                                    <textarea name="remarks" id="comment_text" class="form-control" rows="3"
                                            placeholder="Enter your comment here..." required></textarea>
                                </div>

                                <!-- Status Update Option -->
                                <div class="form-group status-update-section">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="update_status_checkbox">
                                                <label class="custom-control-label" for="update_status_checkbox">
                                                    <i class="fas fa-sync-alt"></i> <strong>Also update ticket status</strong>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <select name="new_status" id="new_status" class="form-control" disabled>
                                                <option value="">Select New Status</option>
                                                <option value="new" <?= $ticket['status'] == 'new' ? 'selected' : '' ?>>New</option>
                                                <option value="assigned" <?= $ticket['status'] == 'assigned' ? 'selected' : '' ?>>Assigned</option>
                                                <option value="closed" <?= $ticket['status'] == 'closed' ? 'selected' : '' ?>>Closed</option>
                                                <option value="on_hold" <?= $ticket['status'] == 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                                                <option value="re_open" <?= $ticket['status'] == 're_open' ? 'selected' : '' ?>>Re-open</option>
                                            </select>
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle"></i> Current:
                                                <span class="badge badge-secondary">
                                                    <?php
                                                    $status_labels = [
                                                        'new' => 'New',
                                                        'assigned' => 'Assigned',
                                                        'closed' => 'Closed',
                                                        'on_hold' => 'On Hold',
                                                        're_open' => 'Re-open'
                                                    ];
                                                    echo $status_labels[$ticket['status']] ?? $ticket['status'];
                                                    ?>
                                                </span>
                                            </small>
                                        </div>
                                    </div>
                                    
                                    <!-- Assigned To dropdown for comments form - shown only when status is "assigned" -->
                                    <div class="form-group mt-3" id="comment_assigned_to_group" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <label for="comment_assigned_to"><strong>Assign To:</strong></label>
                                                <select name="comment_user_id" id="comment_assigned_to" class="form-control select2" style="width: 100%;">
                                                    <option value="">Select User</option>
                                                    <?php if (isset($users)): ?>
                                                        <?php foreach ($users as $user_id => $user_name): ?>
                                                            <option value="<?= $user_id ?>" <?= $ticket['user_id'] == $user_id ? 'selected' : '' ?>>
                                                                <?= htmlspecialchars($user_name) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </select>
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle"></i> 
                                                    When changing status to "Assigned", you must select a user to assign the ticket to.
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-0">
                                    <button type="submit" id="submit_comment_btn" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> <span id="submit_btn_text">Add Comment</span>
                                    </button>
                                    <button type="reset" id="reset_form_btn" class="btn btn-outline-secondary ml-2">
                                        <i class="fas fa-times"></i> Clear
                                    </button>
                                </div>
                            </form>
                        </div>
                        <?php endif; ?>

                        <!-- Comments Timeline -->
                        <div id="comments_timeline">
                            <?php if (!empty($ticket_history)): ?>
                                <div class="timeline-header mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-history"></i>
                                        <strong><?= count($ticket_history) ?></strong> entries found
                                        <span class="ml-2">
                                            <i class="fas fa-sort-amount-down"></i> Latest first
                                        </span>
                                    </small>
                                </div>
                                <?php foreach ($ticket_history as $index => $history): ?>
                                    <div class="comment-item border-bottom pb-3 mb-3 position-relative">
                                        <?php if ($index < count($ticket_history) - 1): ?>
                                            <div class="timeline-connector"></div>
                                        <?php endif; ?>
                                        <div class="d-flex">
                                            <div class="comment-avatar mr-3">
                                                <?php if (!empty($history['user_photo']) && file_exists(FCPATH . 'uploads/users/' . $history['user_photo'])): ?>
                                                    <img src="<?= base_url('uploads/users/'.$history['user_photo']) ?>"
                                                        alt="<?= htmlspecialchars($history['user_name']) ?>"
                                                        class="user-avatar rounded-circle shadow-sm"
                                                        style="width: 45px; height: 45px; object-fit: cover; border: 2px solid #e9ecef;"
                                                        onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                    <div class="user-avatar-fallback bg-primary text-white rounded-circle d-flex align-items-center justify-content-center shadow-sm"
                                                        style="width: 45px; height: 45px; font-size: 16px; font-weight: bold; border: 2px solid #e9ecef; display: none;">
                                                        <?= strtoupper(substr($history['user_name'], 0, 1)) ?>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="user-avatar-fallback bg-primary text-white rounded-circle d-flex align-items-center justify-content-center shadow-sm"
                                                        style="width: 45px; height: 45px; font-size: 16px; font-weight: bold; border: 2px solid #e9ecef;">
                                                        <?= strtoupper(substr($history['user_name'], 0, 1)) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="comment-content flex-grow-1">
                                                <div class="comment-header mb-2">
                                                    <strong class="text-primary"><?= htmlspecialchars($history['user_name']) ?></strong>
                                                    <small class="text-muted ml-2">
                                                        <i class="fas fa-clock"></i>
                                                        <?= date('d-m-Y g:i A', strtotime($history['created_at'])) ?>
                                                        <span class="ml-1 text-info">
                                                            <?php
                                                            $time_diff = time() - strtotime($history['created_at']);
                                                            if ($time_diff < 60) {
                                                                echo '(Just now)';
                                                            } elseif ($time_diff < 3600) {
                                                                echo '(' . floor($time_diff / 60) . ' min ago)';
                                                            } elseif ($time_diff < 86400) {
                                                                echo '(' . floor($time_diff / 3600) . ' hrs ago)';
                                                            } elseif ($time_diff < 604800) {
                                                                echo '(' . floor($time_diff / 86400) . ' days ago)';
                                                            }
                                                            ?>
                                                        </span>
                                                    </small>
                                                    <?php if (!empty($history['duration'])): ?>
                                                        <span class="ml-2">
                                                            <span class="badge badge-info badge-sm">
                                                                <i class="fas fa-clock"></i> <?= gmdate('H:i:s', $history['duration']) ?>
                                                            </span>
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="comment-text">
                                                    <?= nl2br(htmlspecialchars($history['remarks'])) ?>
                                                </div>
                                                <?php if (!empty($history['start_time']) && !empty($history['end_time'])): ?>
                                                    <div class="comment-time-info mt-2">
                                                        <small class="text-muted">
                                                            <i class="fas fa-clock"></i> Time Period:
                                                            <span class="badge badge-light ml-1">
                                                                <?= date('H:i', strtotime($history['start_time'])) ?> - <?= date('H:i', strtotime($history['end_time'])) ?>
                                                            </span>
                                                        </small>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center text-muted py-5">
                                    <div class="mb-3">
                                        <i class="fas fa-comments fa-4x" style="opacity: 0.3;"></i>
                                    </div>
                                    <h5 class="text-muted">No History Yet</h5>
                                    <p class="mb-0">This ticket doesn't have any comments or history entries yet.<br>
                                    Be the first to add a comment!</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions Card -->
        <div class="col-md-4">
            <div class="card card-secondary shadow-pro">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div class="card-body">
                    <?php if ($ticket['status'] !== 'closed'): ?>
                        <?php if (has_permission('tickets/edit')): ?>
                            <form action="<?= base_url('app/tickets/change_status/'.$ticket['id']) ?>" method="post" class="mb-3">
                                <div class="form-group">
                                    <label for="status">Change Status:</label>
                                    <select name="status" id="status" class="form-control" required>
                                        <option value="">Select Status</option>
                                        <option value="new" <?= $ticket['status'] == 'new' ? 'selected' : '' ?>>New</option>
                                        <option value="assigned" <?= $ticket['status'] == 'assigned' ? 'selected' : '' ?>>Assigned</option>
                                        <option value="closed" <?= $ticket['status'] == 'closed' ? 'selected' : '' ?>>Closed</option>
                                        <option value="on_hold" <?= $ticket['status'] == 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                                        <option value="re_open" <?= $ticket['status'] == 're_open' ? 'selected' : '' ?>>Re-open</option>
                                    </select>
                                </div>
                                
                                <!-- Assigned To dropdown - shown only when status is "assigned" -->
                                <div class="form-group" id="assigned_to_group" style="display: <?= $ticket['status'] == 'assigned' ? 'block' : 'none' ?>;">
                                    <label for="assigned_to">Assign To:</label>
                                    <select name="user_id" id="assigned_to" class="form-control select2" style="width: 100%;">
                                        <option value="">Select User</option>
                                        <?php if (isset($users)): ?>
                                            <?php foreach ($users as $user_id => $user_name): ?>
                                                <option value="<?= $user_id ?>" <?= $ticket['user_id'] == $user_id ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($user_name) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> 
                                        When changing status to "Assigned", you must select a user to assign the ticket to.
                                    </small>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-sm btn-block">
                                    <i class="fas fa-sync"></i> Update Status
                                </button>
                            </form>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <div class="task-timestamps-modern">
                        <div class="timestamp-card">
                            <div class="timestamp-header">
                                <i class="bi bi-plus-circle text-success"></i>
                                <span class="timestamp-label">Created</span>
                            </div>
                            <div class="timestamp-content">
                                <div class="timestamp-date"><?= date('M d, Y', strtotime($ticket['created_on'])) ?></div>
                                <div class="timestamp-time"><?= date('g:i A', strtotime($ticket['created_on'])) ?></div>
                                <div class="timestamp-user">
                                    <i class="bi bi-person-circle"></i>
                                    <?= isset($users[$ticket['created_by']]) ? $users[$ticket['created_by']] : 'System' ?>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($ticket['updated_on'] && $ticket['updated_on'] != $ticket['created_on']): ?>
                            <div class="timestamp-card">
                                <div class="timestamp-header">
                                    <i class="bi bi-arrow-clockwise text-info"></i>
                                    <span class="timestamp-label">Updated</span>
                                </div>
                                <div class="timestamp-content">
                                    <div class="timestamp-date"><?= date('M d, Y', strtotime($ticket['updated_on'])) ?></div>
                                    <div class="timestamp-time"><?= date('g:i A', strtotime($ticket['updated_on'])) ?></div>
                                    <div class="timestamp-user">
                                        <i class="bi bi-person-circle"></i>
                                        <?= isset($users[$ticket['updated_by']]) ? $users[$ticket['updated_by']] : 'System' ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Ticket Info Card -->
            <div class="card card-info shadow-pro">
                <div class="card-header">
                    <h3 class="card-title">Ticket Information</h3>
                </div>
                <div class="card-body">
                    <div class="info-box">
                        <span class="info-box-icon bg-info"><i class="fas fa-ticket-alt"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Ticket ID</span>
                            <span class="info-box-number">#<?= str_pad($ticket['id'], 6, '0', STR_PAD_LEFT) ?></span>
                        </div>
                    </div>
                    
                    <?php if ($ticket['status'] == 'closed' && !empty($ticket['close_date']) && $ticket['close_date'] !== '0000-00-00 00:00:00' && !empty($ticket['created_on'])): ?>
                        <?php $resolution_display = get_ticket_resolution_time($ticket['created_on'], $ticket['close_date']); ?>
                        <div class="info-box">
                            <span class="info-box-icon bg-success"><i class="fas fa-clock"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">Resolution Time</span>
                                <span class="info-box-number"><?= $resolution_display ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        
    </div>
</div>

<style>
.badge-lg {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.comment-item:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

.comment-avatar {
    flex-shrink: 0;
}

.user-avatar {
    transition: transform 0.2s ease;
}

.user-avatar:hover {
    transform: scale(1.05);
}

.user-avatar-fallback {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    transition: transform 0.2s ease;
}

.user-avatar-fallback:hover {
    transform: scale(1.05);
}

.comment-content {
    min-height: 40px;
}

.comment-text {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-radius: 10px;
    border-left: 3px solid #007bff;
}

#add_comment_form {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

#new_status:disabled {
    background-color: #e9ecef;
    opacity: 0.6;
}

.custom-control-label {
    cursor: pointer;
}

.status-update-section {
    background-color: rgba(0, 123, 255, 0.05);
    border: 1px dashed #007bff;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
}

#update_status_checkbox:checked + .custom-control-label {
    color: #007bff;
    font-weight: bold;
}

.badge-sm {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
}

.comment-header .badge {
    vertical-align: middle;
}

.comment-header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}

.timeline-connector {
    position: absolute;
    left: 22px;
    top: 55px;
    bottom: -15px;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #e9ecef);
    z-index: 1;
}

.timeline-header {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 20px !important;
}

.comment-item:last-child .timeline-connector {
    display: none;
}

/* Assigned To Dropdown Styling */
#assigned_to_group, #comment_assigned_to_group {
    background-color: rgba(255, 193, 7, 0.05);
    border: 1px dashed #ffc107;
    border-radius: 5px;
    padding: 15px;
    margin-top: 10px;
    transition: all 0.3s ease;
}

#assigned_to_group.show, #comment_assigned_to_group.show {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: #ffc107;
}

#assigned_to_group label, #comment_assigned_to_group label {
    color: #856404;
    font-weight: 600;
}

#assigned_to_group small, #comment_assigned_to_group small {
    color: #856404;
}

#assigned_to_group select, #comment_assigned_to_group select {
    border-color: #ffc107;
}

#assigned_to_group select:focus, #comment_assigned_to_group select:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}
</style>

<script>
$(document).ready(function() {
    // Initialize assigned to dropdown visibility based on current status
    var currentStatus = $('#status').val();
    if (currentStatus === 'assigned') {
        $('#assigned_to_group').show().addClass('show');
        $('#assigned_to').prop('required', true);
    }

    // Handle status change in Quick Actions form
    $('#status').on('change', function() {
        var selectedStatus = $(this).val();
        if (selectedStatus === 'assigned') {
            $('#assigned_to_group').show().addClass('show');
            $('#assigned_to').prop('required', true);
        } else {
            $('#assigned_to_group').hide().removeClass('show');
            $('#assigned_to').prop('required', false);
        }
    });

    // Handle status update checkbox
    $('#update_status_checkbox').on('change', function() {
        var isChecked = $(this).is(':checked');
        $('#new_status').prop('disabled', !isChecked);

        // Update button text based on checkbox state
        if (isChecked) {
            $('#submit_btn_text').text('Add Comment & Update Status');
            $('#submit_comment_btn').removeClass('btn-primary').addClass('btn-success');
            $('#new_status').focus();
            
            // Check if current status is already "assigned" and show dropdown
            var currentCommentStatus = $('#new_status').val();
            if (currentCommentStatus === 'assigned') {
                $('#comment_assigned_to_group').show().addClass('show');
                $('#comment_assigned_to').prop('required', true);
            }
        } else {
            $('#submit_btn_text').text('Add Comment');
            $('#submit_comment_btn').removeClass('btn-success').addClass('btn-primary');
            $('#new_status').val('<?= $ticket['status'] ?>');
            $('#comment_assigned_to_group').hide().removeClass('show');
            $('#comment_assigned_to').prop('required', false);
        }
    });

    // Handle status change in Comments form
    $('#new_status').on('change', function() {
        var selectedStatus = $(this).val();
        if (selectedStatus === 'assigned') {
            $('#comment_assigned_to_group').show().addClass('show');
            $('#comment_assigned_to').prop('required', true);
        } else {
            $('#comment_assigned_to_group').hide().removeClass('show');
            $('#comment_assigned_to').prop('required', false);
        }
    });

    // Handle comment form submission
    $('#add_comment_form').on('submit', function(e) {
        e.preventDefault();

        var formData = {
            remarks: $('#comment_text').val().trim()
        };

        // Check if status update is requested
        var updateStatus = $('#update_status_checkbox').is(':checked');
        if (updateStatus) {
            var newStatus = $('#new_status').val();
            if (!newStatus) {
                alert('Please select a new status or uncheck the status update option.');
                return;
            }

            // Check if status is actually changing
            if (newStatus === '<?= $ticket['status'] ?>') {
                // If status is not changing but it's "assigned", check for user reassignment
                if (newStatus === 'assigned') {
                    var assignedUserId = $('#comment_assigned_to').val();
                    if (!assignedUserId) {
                        alert('Please select a user to assign the ticket to.');
                        return;
                    }
                    // Check if user is actually changing
                    if (assignedUserId === '<?= $ticket['user_id'] ?>') {
                        alert('The selected user is the same as the current assigned user. Please select a different user or uncheck the status update option.');
                        return;
                    }
                    formData.user_id = assignedUserId;
                } else {
                    alert('The selected status is the same as the current status. Please select a different status or uncheck the status update option.');
                    return;
                }
            } else {
                // If status is "assigned", check if user is selected
                if (newStatus === 'assigned') {
                    var assignedUserId = $('#comment_assigned_to').val();
                    if (!assignedUserId) {
                        alert('Please select a user to assign the ticket to when changing status to "Assigned".');
                        return;
                    }
                    formData.user_id = assignedUserId;
                }

                formData.update_status = true;
                formData.new_status = newStatus;
            }
        }

        if (!formData.remarks) {
            alert('Please enter a comment before submitting.');
            return;
        }

        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();

        // Update button text based on action
        var loadingText = updateStatus ?
            '<i class="fas fa-spinner fa-spin"></i> Adding Comment & Updating Status...' :
            '<i class="fas fa-spinner fa-spin"></i> Adding Comment...';

        // Disable submit button and show loading
        submitBtn.prop('disabled', true).html(loadingText);

        $.ajax({
            url: '<?= base_url("app/tickets/add_comment/".$ticket['id']) ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // Clear the form
                    $('#comment_text').val('');
                    $('#update_status_checkbox').prop('checked', false);
                    $('#new_status').prop('disabled', true).val('<?= $ticket['status'] ?>');
                    $('#comment_assigned_to_group').hide().removeClass('show');
                    $('#comment_assigned_to').prop('required', false);

                    // Reload the page to show the new comment and updated status
                    location.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('An error occurred while processing your request. Please try again.');
                console.error('AJAX Error:', error);
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Handle form reset
    $('#reset_form_btn').on('click', function() {
        $('#comment_text').val('');
        $('#update_status_checkbox').prop('checked', false);
        $('#new_status').prop('disabled', true).val('<?= $ticket['status'] ?>');
        $('#comment_assigned_to_group').hide().removeClass('show');
        $('#comment_assigned_to').prop('required', false);
        $('#submit_btn_text').text('Add Comment');
        $('#submit_comment_btn').removeClass('btn-success').addClass('btn-primary');

        // Reset textarea height
        $('#comment_text').css('height', 'auto');
    });

    // Auto-resize textarea
    $('#comment_text').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>

<style>
/* Modern Timestamp Cards */
.task-timestamps-modern {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.timestamp-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.timestamp-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

.timestamp-card:last-child::before {
    background: linear-gradient(90deg, #17a2b8 0%, #6f42c1 100%);
}

.timestamp-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.timestamp-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.timestamp-header i {
    font-size: 16px;
}

.timestamp-label {
    font-weight: 600;
    color: #495057;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.timestamp-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.timestamp-date {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.timestamp-time {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.timestamp-user {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: #495057;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    margin-top: 4px;
    border: 1px solid #e9ecef;
}

.timestamp-user i {
    color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .timestamp-card {
        padding: 10px;
    }
    
    .timestamp-date {
        font-size: 13px;
    }
    
    .timestamp-time {
        font-size: 11px;
    }
    
    .timestamp-user {
        font-size: 10px;
    }
}
</style>