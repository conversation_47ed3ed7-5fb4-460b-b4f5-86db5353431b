<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */
$item_id = $param1;
$edit_data = $this->users_m->get(['id' => $item_id])->row_array();
?>

<form id="quick-status-form" class="form-horizontal" method="post">
    <input type="hidden" name="user_id" value="<?= $item_id ?>">
    
    <div class="row" style="margin: 0!important;">
        
        <!-- User Info Header -->
        <div class="col-12 text-center mb-3">
            <div class="user-info-header">
                <?php
                $photo_url = is_file($edit_data['photo']) ? base_url($edit_data['photo']) : base_url('assets/images/default-user.png');
                ?>
                <img src="<?= $photo_url ?>" class="user-avatar" alt="User Photo">
                <h5 class="user-name"><?= strtoupper($edit_data['name']) ?></h5>
                <p class="user-designation text-muted"><?= $edit_data['designation'] ?></p>
            </div>
            <hr>
        </div>

        <!-- Employee Status -->
        <div class="form-group col-6">
            <label for="employee_status" class="col-form-label text-muted">Employee Status</label>
            <select class="form-control" id="employee_status" name="employee_status">
                <option value="1" <?= $edit_data['employee_status'] == 1 ? 'selected' : '' ?>>Active</option>
                <option value="0" <?= $edit_data['employee_status'] == 0 ? 'selected' : '' ?>>Inactive</option>
            </select>
        </div>

        <!-- Status -->
        <div class="form-group col-6">
            <label for="status" class="col-form-label text-muted">Status</label>
            <select class="form-control" id="status" name="status">
                <option value="1" <?= $edit_data['status'] == 1 ? 'selected' : '' ?>>Active</option>
                <option value="0" <?= $edit_data['status'] == 0 ? 'selected' : '' ?>>Inactive</option>
            </select>
        </div>

        <!-- Is Developer -->
        <div class="form-group col-6">
            <div class="form-check mt-4">
                <input type="hidden" name="is_developer" value="0">
                <input class="form-check-input" type="checkbox" id="is_developer" name="is_developer" value="1" <?= $edit_data['is_developer'] == 1 ? 'checked' : '' ?>>
                <label class="form-check-label text-muted" for="is_developer">
                    Is Developer
                </label>
            </div>
        </div>

        <!-- Is Employee -->
        <div class="form-group col-6">
            <div class="form-check mt-4">
                <input type="hidden" name="is_employee" value="0">
                <input class="form-check-input" type="checkbox" id="is_employee" name="is_employee" value="1" <?= $edit_data['is_employee'] == 1 ? 'checked' : '' ?>>
                <label class="form-check-label text-muted" for="is_employee">
                    Is Employee
                </label>
            </div>
        </div>

        <!-- Work Assign -->
        <div class="form-group col-6">
            <div class="form-check mt-2">
                <input type="hidden" name="work_assign" value="0">
                <input class="form-check-input" type="checkbox" id="work_assign" name="work_assign" value="1" <?= $edit_data['work_assign'] == 1 ? 'checked' : '' ?>>
                <label class="form-check-label text-muted" for="work_assign">
                    Work Assign
                </label>
            </div>
        </div>

        <!-- Work Report -->
        <div class="form-group col-6">
            <div class="form-check mt-2">
                <input type="hidden" name="work_report" value="0">
                <input class="form-check-input" type="checkbox" id="work_report" name="work_report" value="1" <?= $edit_data['work_report'] == 1 ? 'checked' : '' ?>>
                <label class="form-check-label text-muted" for="work_report">
                    Work Report
                </label>
            </div>
        </div>

        <!-- Is Performance -->
        <div class="form-group col-6">
            <div class="form-check mt-2">
                <input type="hidden" name="is_performance" value="0">
                <input class="form-check-input" type="checkbox" id="is_performance" name="is_performance" value="1" <?= $edit_data['is_performance'] == 1 ? 'checked' : '' ?>>
                <label class="form-check-label text-muted" for="is_performance">
                    Is Performance
                </label>
            </div>
        </div>

        <!-- Hourly Rate -->
        <div class="form-group col-6">
            <label for="hourly_rate" class="col-form-label text-muted">Hourly Rate</label>
            <input type="number" step="0.01" class="form-control" id="hourly_rate" name="hourly_rate" value="<?= $edit_data['hourly_rate'] ?>" placeholder="0.00">
        </div>

        <!-- Submit Button -->
        <div class="col-12 mt-3">
            <button type="submit" class="btn btn-primary btn-mini float-right" style="width: 120px;">
                <small><i class="fa fa-check"></i></small> Update Status
            </button>
        </div>
    </div>
</form>

<style>
/* User Info Header Styles */
.user-info-header {
    padding: 10px 0;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 10px;
}

.user-name {
    margin: 5px 0;
    font-weight: bold;
    color: #333;
}

.user-designation {
    margin: 0;
    font-size: 14px;
}

/* Form Styles */
.form-check {
    padding-left: 1.5rem;
}

.form-check-input {
    margin-top: 0.3rem;
}

.form-check-label {
    font-weight: 500;
}

/* Loading overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
$(document).ready(function() {
    // Handle form submission
    $('#quick-status-form').on('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Updating...');
        
        // Prepare form data
        const formData = $(this).serialize();
        
        // Submit via AJAX
        $.ajax({
            url: '<?= base_url("app/users/quick_status_update") ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert('Status updated successfully!');
                    
                    // Close modal
                    $('#scrollable-modal').modal('hide');
                    
                    // Reload page to reflect changes
                    window.location.reload();
                } else {
                    // Show error message
                    alert('Error: ' + (response.message || 'Failed to update status'));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                alert('Error: Failed to update status. Please try again.');
            },
            complete: function() {
                // Restore button state
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>
