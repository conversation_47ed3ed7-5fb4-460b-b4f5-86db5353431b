<!-- Users List -->
<div class="card card-primary shadow-pro" style="margin:14px;">
    <div class="card-header">
        <h3 class="card-title"><?= strtoupper($page_title ?? '') ?></h3>
        <button onclick="show_large_modal('<?php echo site_url('app/modal/popup/get/'); ?>/?page_name=users/add', '<?= get_phrase('add_users'); ?>')" style="width: 160px;"
                class="btn btn-primary btn-mini float-right">
            <small><i class="fas fa-plus"></i></small> Add <?= $page_title ?? '' ?>
        </button>
    </div>

    <!-- Filter Section -->
    <div class="card-body border-bottom">
        <form action="" method="get" class="mb-0">
            <div class="row align-items-end">
                <div class="col-md-4 mb-2">
                    <label for="status" class="form-label text-muted">Employee Status</label>
                    <select class="form-control select2" id="status" name="status">
                        <option value="all" <?= isset($current_status) && $current_status == 'all' ? 'selected' : '' ?>>All</option>
                        <option value="1" <?= isset($current_status) && $current_status == '1' ? 'selected' : '' ?>>Active</option>
                        <option value="0" <?= isset($current_status) && $current_status == '0' ? 'selected' : '' ?>>Inactive</option>
                    </select>
                </div>
                <div class="col-md-4 mb-2">
                    <button type="submit" class="btn btn-primary"><i class="fas fa-filter"></i> Filter</button>
                </div>
            </div>
        </form>
    </div>

    <div class="card-body">
        <table id="example1" class="table table-bordered table-hover">
            <thead>
            <tr>
                <th width="60%">Employee Information</th>
                <th width="25%">Contact Details</th>
                <?php if (has_permission('users/edit') && has_permission('users/delete')): ?>
                    <th width="15%">Actions</th>
                <?php endif; ?>
            </tr>
            </thead>
            <tbody>
            <?php if (isset($list_items) && isset($roles)): ?>
                <?php foreach ($list_items as $key => $item): ?>
                    <tr>
                        <!-- Employee Info with Photo -->
                        <td>
                            <div class="d-flex">
                                <!-- Left side - Photo -->
                                <div class="employee-photo">
                                    <?php
                                    $photo_path = !empty($item['photo']) ? $item['photo'] : 'assets/images/default-user.png';
                                    if (!is_file($photo_path)) {
                                        $photo_path = 'uploads/default.jpg';
                                    }
                                    ?>
                                    <img src="<?= base_url($photo_path) ?>"
                                         class="profile-image"
                                         alt="User Photo">
                                    <span class="status-indicator <?= $item['employee_status'] == 1 ? 'active' : 'inactive' ?>"></span>
                                </div>

                                <!-- Right side - Info -->
                                <div class="employee-details">
                                    <div class="name-section">
                                        <h5 class="employee-name"><?= $item['name'] ?></h5>
                                        <div class="employee-info">
                                            <span class="employee-role"><?= $roles[$item['role_id']] ?></span>
                                            <span class="employee-code"><?= $item['employee_code'] ?></span>
                                        </div>
                                    </div>

                                    <div class="dates-info">
                                        <div class="date-box">
                                            <div class="date-icon">
                                                <i class="far fa-calendar-alt"></i>
                                            </div>
                                            <div class="date-content">
                                                <div class="date-label">DOB</div>
                                                <div class="date-value"><?= DateTime::createFromFormat('Y-m-d', $item['dob'])->format('d M Y') ?></div>
                                            </div>
                                        </div>

                                        <?php if($item['is_employee'] == 1): ?>
                                        <div class="date-box">
                                            <div class="date-icon">
                                                <i class="fas fa-sign-in-alt"></i>
                                            </div>
                                            <div class="date-content">
                                                <div class="date-label">JOD</div>
                                                <div class="date-value"><?= DateTime::createFromFormat('Y-m-d', $item['join_date'])->format('d M Y') ?></div>
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                        <?php if(!empty($item['leave_date'])): ?>
                                        <div class="date-box leave-date">
                                            <div class="date-icon">
                                                <i class="fas fa-sign-out-alt"></i>
                                            </div>
                                            <div class="date-content">
                                                <div class="date-label">DOL</div>
                                                <div class="date-value"><?= DateTime::createFromFormat('Y-m-d', $item['leave_date'])->format('d M Y') ?></div>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </td>

                        <!-- Contact Details with HID -->
                        <td class="contact-column">
                            <div class="contact-info">
                                <div class="contact-item hid-item">
                                    <i class="fas fa-id-card"></i>
                                    <div class="hid-details">
                                        <span class="hid-number"><?= $item['hid_number'] ?></span>
                                        <span class="id-card"><?= $item['id_card_no'] ?></span>
                                    </div>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <span><?= $item['phone'] ?></span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <span><?= $item['email'] ?></span>
                                </div>
                            </div>
                        </td>

                        <!-- Actions -->
                        <?php if (has_permission('users/edit') && has_permission('users/delete')): ?>
                            <td class="action-column">
                                <div class="action-buttons">
                                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=users/overview'); ?>', '<?= get_phrase('view_user'); ?>')"
                                            class="btn btn-primary btn-sm" title="View">
                                        <i class="bi bi-person-lines-fill"></i>
                                    </button>
                                    <button onclick="show_large_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=users/edit'); ?>', '<?= get_phrase('update_users'); ?>')"
                                            class="btn btn-info btn-sm" title="Edit">
                                        <i class="fas fa-pencil-alt"></i>
                                    </button>
                                    <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=users/quick_status_edit'); ?>', '<?= get_phrase('quick_status_edit'); ?>')"
                                            class="btn btn-success btn-sm" title="Quick Status Edit">
                                        <i class="bi bi-sliders"></i>
                                    </button>
                                    <button onclick="show_ajax_modal('<?= site_url('app/modal/popup/get/'.$item['id'].'/?page_name=users/reset_password'); ?>', '<?= get_phrase('reset_password'); ?>')"
                                            class="btn btn-warning btn-sm" title="Reset Password">
                                        <i class="bi bi-key"></i>
                                    </button>
                                </div>
                            </td>
                        <?php endif; ?>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<style>
/* General Table Styles */
.table {
    background: #fff;
}

.table td {
    padding: 1rem;
}

/* Employee Information Column */
.employee-photo {
    position: relative;
    margin-right: 1.5rem;
}

.profile-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-indicator {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.status-indicator.active {
    background-color: #28a745;
}

.status-indicator.inactive {
    background-color: #ffc107;
}

.employee-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    padding-top: 0.3rem;
}

.name-section {
    /* margin-bottom: 1rem; */
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.employee-name {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    display: block;
}

.employee-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

.employee-role {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 500;
}

.employee-code {
    color: #444;
    font-size: 0.9rem;
    background: #f0f0f0;
    padding: 3px 8px;
    border-radius: 4px;
    display: inline-block;
}

/* Designation removed */

.dates-info {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;
    /* margin-top: 0.5rem; */
    padding-top: 0.3rem;
}

.date-box {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    min-width: 120px;
}

.date-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: #e9ecef;
    border-radius: 4px;
    margin-right: 8px;
}

.date-icon i {
    color: #495057;
    font-size: 0.9rem;
}

.date-content {
    display: flex;
    flex-direction: column;
}

.date-label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.75rem;
    text-transform: uppercase;
    margin-bottom: 2px;
}

.date-value {
    color: #212529;
    font-size: 0.85rem;
}

.leave-date .date-icon {
    background: #f8d7da;
}

.leave-date .date-icon i {
    color: #dc3545;
}

.leave-date .date-label {
    color: #dc3545;
}

/* Contact Details Column */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.6rem;
}

.contact-item {
    display: flex;
    align-items: center;
    color: #555;
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    font-size: 0.9rem;
}

.contact-item i {
    width: 20px;
    margin-right: 12px;
    color: #2c3e50;
    font-size: 0.9rem;
}

/* HID Item Styling */
.contact-item.hid-item {
    padding: 0.8rem;
    margin-bottom: 0.5rem;
}

.hid-item i {
    font-size: 1.1rem;
    margin-right: 12px;
}

.hid-details {
    display: flex;
    flex-direction: column;
}

.hid-number {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.3;
}

.id-card {
    font-size: 0.85rem;
    color: #555;
    margin-top: 3px;
}

/* Action Column */
.action-column {
    text-align: center;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-buttons .btn {
    padding: 0.4rem 0.8rem;
}

/* DataTable Customization */
.dataTables_wrapper .dataTables_length select {
    min-width: 60px;
}

.dataTables_wrapper .dataTables_filter input {
    min-width: 250px;
}

/* Filter Section Styles */
.card-body.border-bottom {
    background-color: #f8f9fa;
    padding: 1rem 1.5rem;
}

.form-label.text-muted {
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.btn-outline-secondary {
    margin-left: 0.5rem;
}
</style>

<script>
$(document).ready(function() {
    // Auto-submit form when status or role changes
    $('#status, #role').on('change', function() {
        $(this).closest('form').submit();
    });
});
</script>