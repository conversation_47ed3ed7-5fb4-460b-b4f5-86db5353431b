<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migrate_work_history extends Frontend_Controller
{
    public function index()
    {
        

        $this->load->database();

        echo "Starting migration to work_history...\n";

        $this->migrate_tasks();
        $this->migrate_tickets();

        echo "Migration complete.\n";
    }

    private function migrate_tasks()
    {
        echo "Migrating task_assign data...\n";

        $tasks = $this->db->get('task_assign')->result();

        foreach ($tasks as $task) {
            $start_time = (!empty($task->start_time) && !empty($task->job_date))
                ? date('Y-m-d H:i:s', strtotime("{$task->job_date} {$task->start_time}"))
                : null;

            $end_time = (!empty($task->end_time) && !empty($task->job_date))
                ? date('Y-m-d H:i:s', strtotime("{$task->job_date} {$task->end_time}"))
                : null;

            $duration = 0;
            if (!empty($task->time_taken)) {
                $time_parts = explode(':', $task->time_taken);
                $duration = ((int)$time_parts[0]) * 60 + (int)$time_parts[1];
            }

            $data = [
                'user_id'     => $task->user_id,
                'item_type'   => 'task',
                'item_id'     => $task->task_id,
                'start_time'  => $start_time,
                'end_time'    => $end_time,
                'duration'    => $duration,
                'remarks'     => $task->remarks,
                'created_at'  => $task->created_on ?: date('Y-m-d H:i:s'),
                'updated_at'  => $task->updated_on ?: date('Y-m-d H:i:s'),
            ];

            $this->db->insert('work_history', $data);
        }

        echo "Task migration complete. Total: " . count($tasks) . " entries.\n";
    }

    private function migrate_tickets()
    {
        echo "Migrating ticket_history data...\n";

        $tickets = $this->db->get('ticket_history')->result();

        foreach ($tickets as $ticket) {
            if (empty($ticket->ticket_id) || empty($ticket->user_id)) {
                continue; // skip invalid entries
            }

            $data = [
                'user_id'     => $ticket->user_id,
                'item_type'   => 'ticket',
                'item_id'     => $ticket->ticket_id,
                'start_time'  => null,
                'end_time'    => null,
                'duration'    => 0,
                'remarks'     => $ticket->remarks,
                'created_at'  => $ticket->created_at ?: date('Y-m-d H:i:s'),
                'updated_at'  => $ticket->updated_at ?: date('Y-m-d H:i:s'),
            ];

            $this->db->insert('work_history', $data);
        }

        echo "Ticket migration complete. Total: " . count($tickets) . " entries.\n";
    }
}
