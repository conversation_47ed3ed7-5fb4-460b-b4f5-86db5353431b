<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Task_assign extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('tasks_m');
        $this->load->model('work_history_m');
        $this->load->model('projects_m');
        $this->load->model('project_type_m');
        $this->load->model('clients_m');
    }

    public function index($task_id) {
        $this->data['clients']       = array_column($this->clients_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['projects']      = array_column($this->projects_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['list_items']    = $this->tasks_m->get()->result_array();
        $this->data['page_title']    = 'Tasks';
        $this->data['page_name']     = 'tasks/index';
        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $client_id = $this->projects_m->get(['id' => $this->input->post('project_id')])->row()->client_id;
            $remark_files = array_column($this->upload_file_multiple('tasks', 'remark_files'), 'file');
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'project_id' => $this->input->post('project_id'),
                'client_id' => $client_id,
                'task_status' => 'pending',
                'task_priority' => $this->input->post('task_priority'),
                'task_type' => $this->input->post('task_type'),
                'due_date' => $this->input->post('due_date'),
                'required_time' => $this->input->post('required_time'),
                'requested_by' => $this->input->post('requested_by'),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            if (count($remark_files)){
                $data['remark_files'] = json_encode($remark_files);
            }
            $this->tasks_m->insert($data);
            set_alert('message_success', 'Task Added Successfully!');
        }
        redirect('app/tasks/index');
    }

    public function edit($item_id){
        if ($this->input->post()){
            $client_id = $this->projects_m->get(['id' => $this->input->post('project_id')])->row()->client_id;
            $remark_files = array_column($this->upload_file_multiple('tasks', 'remark_files'), 'file');
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'project_id' => $this->input->post('project_id'),
                'client_id' => $client_id,
                'task_status' => 'pending',
                'task_priority' => $this->input->post('task_priority'),
                'task_type' => $this->input->post('task_type'),
                'due_date' => $this->input->post('due_date'),
                'required_time' => $this->input->post('required_time'),
                'requested_by' => $this->input->post('requested_by'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            if (count($remark_files)){
                // delete existing files
                $task_files = json_decode($this->tasks_m->get(['id' => $item_id])->row()->remark_files, true);
                if (is_array($task_files)){
                    foreach ($task_files as $file){
                        if (is_file($file)){
                            unlink($file);
                        }
                    }
                }

                $data['remark_files'] = json_encode($remark_files);
            }
            $this->tasks_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Task Updated Successfully!');
        }
        redirect('app/tasks/index');
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->work_history_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Task History Deleted Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }
}
