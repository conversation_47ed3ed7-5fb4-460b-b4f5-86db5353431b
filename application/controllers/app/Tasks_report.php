<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Tasks_report extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('tasks_m');
        $this->load->model('work_history_m');
        $this->load->model('projects_m');
        $this->load->model('project_type_m');
        $this->load->model('users_m');
    }

    public function index() {
        if (!has_permission('tasks_report/index')){
            redirect('app/dashboard/index');
        }

        $start_date = $_GET['start_date'] ?? date('Y-m-d');
        $end_date = $_GET['end_date'] ?? date('Y-m-d');
        $user_id = $_GET['user_id'] ?? 0;
        $project_id = $_GET['project_id'] ?? 0;
        $this->data['task_report'] = $this->get_report($start_date, $end_date, $user_id, $project_id);

        $this->data['users'] = array_column($this->users_m->get(['work_assign' => 1])->result_array(), 'name', 'id');
        $this->data['employees'] = array_column($this->users_m->get()->result_array(), 'name', 'id');
        $this->data['projects'] = array_column($this->projects_m->get()->result_array(), 'title', 'id');

        $this->data['page_title']    = 'Task Report';
        $this->data['page_name']     = 'tasks_report/index';
        $this->load->view('app/index', $this->data);
    }

    private function get_report($start_date, $end_date, $user_id, $project_id) {
        $where['DATE(work_history.start_time) >='] = $start_date;
        $where['DATE(work_history.start_time) <='] = $end_date;
        $where['work_history.item_type'] = 'task';
        if ($user_id > 0){
            $where['work_history.user_id'] = $user_id;
        }
        if ($project_id > 0){
            $where['tasks.project_id'] = $project_id;
        }

        $this->db->select('work_history.*, tasks.project_id, tasks.title as task_title');
        $this->db->from('work_history');
        $this->db->join('tasks', 'tasks.id = work_history.item_id');
        $this->db->where($where);
        $result = $this->db->get()->result_array();

        $task_report = $result;
        return $task_report;
    }

    public function task_overview() {
        if (!has_permission('tasks_report/task_overview')){
            redirect('app/dashboard/index');
        }

        $this->data['users'] = $this->get_task_overview();
        $this->data['projects'] = array_column($this->projects_m->get()->result_array(), 'title', 'id');

        $this->data['page_title']    = 'Task Overview';
        $this->data['page_name']     = 'tasks_report/task_overview';
        $this->load->view('app/index', $this->data);
    }

    private function get_task_overview(){
        // get users
        $users = $this->users_m->get(
            ['employee_status' => 1, 'work_assign' => 1],
            ['id', 'name', 'phone'],
            ['key' => 'name', 'direction' => 'asc']
        )->result_array();

        $users_data = [];
        foreach ($users as $user){
            $users_data[$user['id']] = $user;
            $users_data[$user['id']]['projects'] = [];
            $users_data[$user['id']]['tasks'] = [];
        }

        // get pending tasks
        $this->db->where_in('user_id', array_column($users, 'id'));
        $pending_tasks = $this->tasks_m->get(
            ['task_status' => 'assigned'],
            ['id', 'title', 'due_date', 'task_priority', 'task_type', 'project_id', 'user_id']
        )->result_array();

        foreach ($pending_tasks as $task){
            $users_data[$task['user_id']]['tasks'][] = $task;

            if(!isset($users_data[$task['user_id']]['projects'][$task['project_id']])){
                $users_data[$task['user_id']]['projects'][$task['project_id']] = 1;
            }else{
                $users_data[$task['user_id']]['projects'][$task['project_id']] ++;
            }
        }

        return $users_data;
    }


}



