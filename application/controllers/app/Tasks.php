<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Tasks extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('tasks_m');
        $this->load->model('work_history_m');
        $this->load->model('projects_m');
        $this->load->model('project_type_m');
        $this->load->model('clients_m');
        $this->load->model('users_m');
    }

    public function index() {
        if (!has_permission('tasks/index')){
            redirect('app/dashboard/index');
        }

        // Remove default date filtering logic
        $this->data['start_date'] = $this->input->get('start_date');
        $this->data['end_date'] = $this->input->get('end_date');
        $this->data['project_id'] = $this->input->get('project_id') > 0 ? $this->input->get('project_id') : 0;

        $where = [];

        if($this->input->get('project_id') > 0){
            $where['tasks.project_id'] = $this->input->get('project_id');
        }

        if($this->input->get('user_id') > 0 && $this->input->get('task_status')!='pending' && $this->input->get('task_status')!='on_hold' && $this->input->get('task_status')!='all'){
            $where['tasks.user_id'] = $this->input->get('user_id');
        }

        if (
            isset($where['task_status']) &&
            $where['task_status'] != 'pending' &&
            $where['task_status'] != 'assigned' &&
            $where['task_status'] != 'on_hold'
        ){
            if (!empty($this->data['start_date'])) {
                $where['date(tasks.due_date) >='] = $this->data['start_date'];
            }
            if (!empty($this->data['end_date'])) {
                $where['date(tasks.due_date) <='] = $this->data['end_date'];
            }
        }

        $this->data['status_count'] = [
            'all' => $this->tasks_m->get_count($this->data['start_date'], $this->data['end_date'],[], $this->input->get('project_id')),
            'pending' => $this->tasks_m->get_count($this->data['start_date'], $this->data['end_date'], ['task_status' => 'pending'], $this->input->get('project_id')),
            'assigned' => $this->tasks_m->get_count($this->data['start_date'], $this->data['end_date'], ['task_status' => 'assigned'], $this->input->get('project_id'), $this->input->get('user_id')),
            'testing' => $this->tasks_m->get_count($this->data['start_date'], $this->data['end_date'], ['task_status' => 'testing'], $this->input->get('project_id'), $this->input->get('user_id')),
            'on_hold' => $this->tasks_m->get_count($this->data['start_date'], $this->data['end_date'], ['task_status' => 'on_hold'], $this->input->get('project_id')),
            'completed' => $this->tasks_m->get_count($this->data['start_date'], $this->data['end_date'], ['task_status' => 'completed'], $this->input->get('project_id'), $this->input->get('user_id')),
        ];

        if (!empty($this->input->get('task_status')) && $this->input->get('task_status') != 'all'){
            $where['tasks.task_status'] = $this->input->get('task_status');
        }
        

        $this->data['list_items']    = $this->tasks_m->get($where, null, ['key' => 'id', 'direction' => 'desc'])->result_array();
        

        $this->data['clients'] = array_column($this->clients_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['projects_list'] = $this->projects_m->get(null, ['id', 'title', 'project_type'])->result_array();
        $this->data['projects'] = array_column($this->data['projects_list'], 'title', 'id');
        $this->data['project_type'] = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');

        $this->db->where_not_in('role_id', [0, 1]);
        $this->db->where('employee_status', 1);
        $this->data['users'] = array_column($this->users_m->get(null, ['id', 'name'])->result_array(), 'name', 'id');

        if (is_technical_support()){
            $this->data['tester_projects'] = array_column($this->projects_m->get(['tester_id' => get_user_id()], ['id'])->result_array(), 'id');
        }

        $this->data['page_title']    = 'Tasks (Optimized)';
        $this->data['page_name'] = 'tasks/index_optimized';
        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $client_id = $this->projects_m->get(['id' => $this->input->post('project_id')])->row()->client_id;
            $remark_files = array_column($this->upload_file_multiple('tasks', 'remark_files'), 'file');
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'project_id' => $this->input->post('project_id'),
                'client_id' => $client_id,
                'task_status' => 'pending',
                'task_priority' => $this->input->post('task_priority'),
                'task_type' => $this->input->post('task_type'),
                'due_date' => $this->input->post('due_date'),
                'required_time' => $this->input->post('required_time'),
                'requested_by' => $this->input->post('requested_by'),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            if (count($remark_files)){
                $data['remark_files'] = json_encode($remark_files);
            }
            $this->tasks_m->insert($data);
            $task_id = $this->db->insert_id();
            set_alert('message_success', 'Task Added Successfully!');

            // if the user does not have permission for tasks listing then assign to themselves
            if ($this->input->post('assign_user_id') > 0){
                $data = [
                    'user_id' => $this->input->post('assign_user_id'),
                    'task_status' => 'assigned',
                    'task_priority' => $this->input->post('task_priority'),
                    'task_type' => $this->input->post('task_type'),
                    'due_date' => $this->input->post('due_date'),
                    'required_time' => $this->input->post('required_time'),
                    'updated_by' => get_user_id(),
                    'updated_on' => date('Y-m-d H:i:s'),
                ];
                $this->tasks_m->update($data, ['id' => $task_id]);

                // check if already assigned
                $is_assigned = $this->work_history_m->is_task_assigned($task_id);

                // assign user
                $data_assign = [
                    'task_id' => $task_id,
                    'user_id' => $this->input->post('assign_user_id'),
                    'remarks' => 'Task assigned',
                    'created_at' => date('Y-m-d H:i:s')
                ];
                $this->work_history_m->insert_task_history($data_assign);
            }

        }
        if (is_mobile()){
            redirect('app/dashboard/index');
        }else{
            redirect($_SERVER['HTTP_REFERER']);
        }
    }

    public function add_mobile(){
        $this->data['page_title']    = 'Add Task';
        $this->data['page_name']     = 'tasks/add_mobile';
        $this->load->view('app/index', $this->data);
    }

    public function edit($item_id){
        if ($this->input->post()){
            $client_id = $this->projects_m->get(['id' => $this->input->post('project_id')])->row()->client_id;
            $remark_files = array_column($this->upload_file_multiple('tasks', 'remark_files'), 'file');
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'project_id' => $this->input->post('project_id'),
                'client_id' => $client_id,
                'task_priority' => $this->input->post('task_priority'),
                'task_type' => $this->input->post('task_type'),
                'due_date' => $this->input->post('due_date'),
                'required_time' => $this->input->post('required_time'),
                'requested_by' => $this->input->post('requested_by'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            if (count($remark_files)){
                // delete existing files
                $task_files = json_decode($this->tasks_m->get(['id' => $item_id])->row()->remark_files, true);
                if (is_array($task_files)){
                    foreach ($task_files as $file){
                        if (is_file($file)){
                            unlink($file);
                        }
                    }
                }

                $data['remark_files'] = json_encode($remark_files);
            }
            $this->tasks_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Task Updated Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function ajax_save_content(){
        if ($this->input->post()){
            $data = [
                'description' => $this->input->post('content'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->tasks_m->update($data, ['id' => $this->input->post('task_id')]);
            echo true;
        }
        echo false;
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->tasks_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Task Deleted Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    // tasks assign
    public function assign($item_id){
        if ($this->input->post()){
            $data = [
                'user_id' => $this->input->post('user_id'),
                'task_status' => 'assigned',
                'task_priority' => $this->input->post('task_priority'),
                'task_type' => $this->input->post('task_type'),
                'due_date' => $this->input->post('due_date'),
                'required_time' => $this->input->post('required_time'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->tasks_m->update($data, ['id' => $item_id]);

            // check if already assigned
            $is_assigned = $this->work_history_m->is_task_assigned($item_id);

            // assign user
            $data_assign = [
                'task_id' => $item_id,
                'user_id' => $this->input->post('user_id'),
                'remarks' => 'Task assigned',
                'created_at' => date('Y-m-d H:i:s')
            ];
            $this->work_history_m->insert_task_history($data_assign);
            log_message('error', $this->db->last_query());
            set_alert('message_success', 'Task Updated Successfully!');
            $user_id = $this->input->post('user_id');
            redirect($this->get_redirect_url($user_id));
        }
    }
    
    private function get_redirect_url($user_id){
        $url = $_SERVER['HTTP_REFERER'];
        
        $parsed_url = parse_url($url);
        $query_params = [];
        parse_str($parsed_url['query'] ?? '', $query_params);
        
        $query_params['user_id'] = $user_id;
        
        // Build the new query string
        $new_query_string = http_build_query($query_params);
        
        // Reconstruct the URL
        $new_url = "{$parsed_url['scheme']}://{$parsed_url['host']}{$parsed_url['path']}?$new_query_string";
        if (isset($parsed_url['fragment'])) {
            $new_url .= "#{$parsed_url['fragment']}";
        }
        $new_url .= "#user_{$user_id}";
        return $new_url;
    }

    public function un_assign($item_id){
        if ($this->input->post()){
            $data = [
                'task_status' => $this->input->post('action_status'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            if ($this->input->post('action_status') == 'pending'){
                $data['user_id'] = null;
            }
            $this->tasks_m->update($data, ['id' => $item_id]);
            
            set_alert('message_success', 'Task Updated Successfully!');
            redirect($_SERVER['HTTP_REFERER']);
        }
    }

    // update task status employee
    public function employee_update_status($item_id){

        if ($this->work_history_m->is_task_period_overlap(get_user_id(), $this->input->post('job_date'), $this->input->post('start_time'), $this->input->post('end_time'))){
            set_alert('message_error', 'The task time overlaps with an existing task.!');
        }else{
            log_message('error', $this->db->last_query());
            
            $job_date = $this->input->post('job_date');

            //update task status
            $data = [
                'task_status' => $this->input->post('task_status'),
                'time_taken' => $this->input->post('time_taken'),
            ];
            $this->tasks_m->update($data, ['id' => $item_id]);

            // update task status assign
            $this->db->order_by('work_history.id', 'DESC');
            $this->db->limit(1);
            $task_assign = $this->work_history_m->get([
                'item_type' => 'task',
                'item_id' => $item_id,
                'user_id' => get_user_id(),
            ]);

            if ($task_assign->num_rows() > 0){
                $task_assign = $task_assign->row();
                $created_at = date('Y-m-d H:i:s');

                if ($this->input->post('task_status') == 'completed'){
                    $task = [
                        'start_time' => $job_date . ' ' . $this->input->post('start_time'),
                        'end_time' => $job_date . ' ' . $this->input->post('end_time'),
                        'time_taken' => $this->input->post('time_taken'),
                        'remarks' => $this->input->post('remarks'),
                    ];

                    if (empty($task_assign->duration)){
                        $this->work_history_m->update(
                            [
                                'start_time' => $task['start_time'],
                                'end_time' => $task['end_time'],
                                'duration' => $this->calculate_duration_minutes($task['start_time'], $task['end_time']),
                                'remarks' => $task['remarks'],
                                'updated_at' => $created_at,
                            ],
                            ['id' => $task_assign->id]
                        );
                    }else{
                        $task_assign_data = [
                            'task_id' => $task_assign->item_id,
                            'user_id' => $task_assign->user_id,
                            'start_time' => $task['start_time'],
                            'end_time' => $task['end_time'],
                            'duration' => $this->calculate_duration_minutes($task['start_time'], $task['end_time']),
                            'remarks' => $task['remarks'],
                            'created_at' => $created_at,
                        ];
                        $this->work_history_m->insert_task_history($task_assign_data);
                    }
                }else{
                    $task = [
                        'start_time' => $job_date . ' ' . $this->input->post('start_time'),
                        'end_time' => $job_date . ' ' . $this->input->post('end_time'),
                        'time_taken' => $this->input->post('time_taken'),
                        'remarks' => $this->input->post('remarks'),
                    ];

                    if (empty($task_assign->duration)){
                        $this->work_history_m->update(
                            [
                                'start_time' => $task['start_time'],
                                'end_time' => $task['end_time'],
                                'duration' => $this->calculate_duration_minutes($task['start_time'], $task['end_time']),
                                'remarks' => $task['remarks'],
                                'updated_at' => $created_at,
                            ],
                            ['id' => $task_assign->id]
                        );
                    }else{
                        $task_assign_data = [
                            'task_id' => $task_assign->item_id,
                            'user_id' => $task_assign->user_id,
                            'start_time' => $task['start_time'],
                            'end_time' => $task['end_time'],
                            'duration' => $this->calculate_duration_minutes($task['start_time'], $task['end_time']),
                            'remarks' => $task['remarks'],
                            'created_at' => $created_at,
                        ];
                        $this->work_history_m->insert_task_history($task_assign_data);
                    }
                }
            }
            set_alert('message_success', 'Task Status Updated Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    // Helper method to calculate duration in minutes
    private function calculate_duration_minutes($start_time, $end_time) {
        $start = new DateTime($start_time);
        $end = new DateTime($end_time);
        return ($end->getTimestamp() - $start->getTimestamp()) / 60;
    }

    public function task_details_employee_mobile($item_id){
        $this->data['item_id']    = $item_id;
        $this->data['page_title']    = 'Add Task';
        $this->data['page_name']     = 'tasks/task_details_employee_mobile';
        $this->load->view('app/index', $this->data);
    }
   
    public function task_update_bulk(){
        if(!is_project_manager() && get_user_id() != 10){
            redirect('app/dashboard/index');
        }

        if($this->input->post()){
            $job_date = $this->input->post('job_date');
            
            if($job_date!= date('Y-m-d')){
                $created_at = $job_date.' 23:59:10';
            }else{
                $created_at = date('Y-m-d H:i:s');
            }
            $remarks = $this->input->post('remarks');

            if(empty($remarks)){
                set_alert('message_error', 'Please enter remarks');
                redirect($_SERVER['HTTP_REFERER']);
            }
            
            $tasks = getTaskTimeData($remarks);
            log_message('error', json_encode($tasks));
            
            if(empty($tasks)){
                set_alert('message_error', 'No tasks found');
                redirect($_SERVER['HTTP_REFERER']);
            }

            foreach($tasks as $task){
                // update task status assign
                $this->db->order_by('work_history.id', 'DESC');
                $this->db->limit(1);
                $task_assign = $this->work_history_m->get([
                    'item_type' => 'task',
                    'item_id' => $task['task_id'],
                    'user_id' => get_user_id(),
                ]);

                if ($task_assign->num_rows() > 0){
                    $task_assign = $task_assign->row();

                    if (empty($task_assign->duration)){
                        $this->work_history_m->update(
                            [
                                'start_time' => $task['start_time'],
                                'end_time' => $task['end_time'],
                                'duration' => $this->calculate_duration_minutes($task['start_time'], $task['end_time']),
                                'remarks' => $task['remarks'],
                                'updated_at' => $created_at,
                            ],
                            ['id' => $task_assign->id]
                        );
                    }else{
                        $task_assign_data = [
                            'task_id' => $task_assign->item_id,
                            'user_id' => $task_assign->user_id,
                            'start_time' => $task['start_time'],
                            'end_time' => $task['end_time'],
                            'duration' => $this->calculate_duration_minutes($task['start_time'], $task['end_time']),
                            'remarks' => $task['remarks'],
                            'created_at' => $created_at,
                        ];
                        $this->work_history_m->insert_task_history($task_assign_data);
                    }
                }else{
                    $task = $this->tasks_m->get(['id' => $item_id])->row();
                    $task_assign_data = [
                        'task_id' => $task->id,
                        'user_id' => $task->user_id,
                        'start_time' => $task['start_time'],
                        'end_time' => $task['end_time'],
                        'duration' => $this->calculate_duration_minutes($task['start_time'], $task['end_time']),
                        'remarks' => $task['remarks'],
                        'created_at' => $created_at,
                    ];
                    $this->work_history_m->insert_task_history($task_assign_data);
                }
            }
            set_alert('message_success', 'Task Status Updated Successfully!');
            redirect($_SERVER['HTTP_REFERER']);
        }

        $this->data['page_title']    = 'Bulk Update Task';
        $this->data['page_name']     = 'tasks/task_update_bulk';
        $this->load->view('app/index', $this->data);
    }
    
    public function test_task($task_id){
        if ($task_id > 0){
            // update task
            $this->tasks_m->update(
                [
                    'tested_by' => get_user_id(),
                    'tester_remarks' => $this->input->post('remarks'),
                    'task_status' => $this->input->post('task_status'),
                    'updated_by' => get_user_id(),
                    'updated_on' => date('Y-m-d H:i:s'),
                ],
                ['id' => $task_id]
            );

            // get task details
            $task = $this->tasks_m->get(['id' => $task_id])->row();
            $task_assign_data = [
                'task_id' => $task->id,
                'user_id' => get_user_id(),
                'start_time' => $this->input->post('job_date') . ' ' . $this->input->post('start_time'),
                'end_time' => $this->input->post('job_date') . ' ' . $this->input->post('end_time'),
                'duration' => $this->calculate_duration_minutes(
                    $this->input->post('job_date') . ' ' . $this->input->post('start_time'),
                    $this->input->post('job_date') . ' ' . $this->input->post('end_time')
                ),
                'remarks' => $this->input->post('remarks'),
                'created_at' => date('Y-m-d H:i:s'),
            ];
            $this->work_history_m->insert_task_history($task_assign_data);

            set_alert('message_success', 'Task Status Updated Successfully!');
            redirect($_SERVER['HTTP_REFERER']);

        }
    }

    public function task_status($task_id){
        if ($task_id > 0){
            $new_status = $this->input->post('task_status');
            $user_id = $this->input->post('user_id');
            
            // Get current task details
            $current_task = $this->tasks_m->get(['id' => $task_id])->row_array();
            
            // Prepare update data
            $update_data = [
                'task_status' => $new_status,
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s')
            ];

            // Update assigned user if status is 'assigned' and user is provided
            if ($new_status === 'assigned' && !empty($user_id)) {
                $update_data['user_id'] = $user_id;
            }

            // Update task
            $this->tasks_m->update($update_data, ['id' => $task_id]);

            // Create status change message
            $status_labels = [
                'pending' => 'Pending',
                'assigned' => 'Assigned',
                'testing' => 'Testing',
                'completed' => 'Completed',
                'on_hold' => 'On Hold'
            ];
            
            $status_message = 'Task status changed from "' . $status_labels[$current_task['task_status']] . '" to "' . $status_labels[$new_status] . '"';
            
            // Add user assignment message if applicable
            if ($new_status === 'assigned' && !empty($user_id)) {
                $assigned_user = $this->users_m->get(['id' => $user_id])->row();
                if ($assigned_user) {
                    $status_message .= ' and assigned to ' . $assigned_user->name;
                }
            }

            // Save to work history
            $history_data = [
                'task_id' => $task_id,
                'user_id' => get_user_id(),
                'remarks' => $status_message,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $this->work_history_m->insert_task_history($history_data);

            set_alert('message_success', 'Task Status Updated Successfully!');
            redirect($_SERVER['HTTP_REFERER']);
        }
    }

    public function view($task_id) {
        if (!has_permission('tasks/index')){
            redirect('app/dashboard/index');
        }

        // Get task details with project and user information
        $this->db->select('
            t.*,
            p.title as project_title,
            u.name as assigned_user_name,
            c.title as client_name
        ');
        $this->db->from('tasks t');
        $this->db->join('projects p', 't.project_id = p.id', 'left');
        $this->db->join('users u', 't.user_id = u.id', 'left');
        $this->db->join('clients c', 't.client_id = c.id', 'left');
        $this->db->where('t.id', $task_id);
        $task = $this->db->get()->row_array();

        if (!$task) {
            set_alert('message_error', 'Task not found!');
            redirect('app/tasks/index');
        }

        // Get task history
        $task_history = $this->work_history_m->get_task_history($task_id);

        // Get users for assignment dropdown
        $this->db->where_not_in('role_id', [0, 1]);
        $this->db->where('employee_status', 1);
        $users = array_column($this->users_m->get(null, ['id', 'name'])->result_array(), 'name', 'id');

        $this->data['task'] = $task;
        $this->data['task_history'] = $task_history;
        $this->data['users'] = $users;
        $this->data['page_title'] = 'Task Details - ' . $task['title'];
        $this->data['page_name'] = 'tasks/view';
        $this->load->view('app/index', $this->data);
    }

    public function add_comment($task_id) {
        if (!has_permission('tasks/edit')){
            $this->output->set_status_header(403);
            echo json_encode(['status' => 'error', 'message' => 'Permission denied']);
            return;
        }

        $remarks = $this->input->post('remarks');
        $start_time = $this->input->post('start_time');
        $end_time = $this->input->post('end_time');
        $update_status = $this->input->post('update_status');
        $new_status = $this->input->post('new_status');
        $user_id = $this->input->post('user_id');

        if (empty($remarks)) {
            echo json_encode(['status' => 'error', 'message' => 'Please enter a comment']);
            return;
        }

        // Prepare comment data
        $comment_data = [
            'task_id' => $task_id,
            'user_id' => get_user_id(),
            'remarks' => $remarks,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // Add time tracking if provided
        if (!empty($start_time) && !empty($end_time)) {
            $comment_data['start_time'] = date('Y-m-d') . ' ' . $start_time;
            $comment_data['end_time'] = date('Y-m-d') . ' ' . $end_time;
        }

        // Insert comment
        $this->work_history_m->insert_task_history($comment_data);

        // Update task status if requested
        if ($update_status && !empty($new_status)) {
            $update_data = [
                'task_status' => $new_status,
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s')
            ];

            // Update assigned user if status is 'assigned' and user is provided
            if ($new_status === 'assigned' && !empty($user_id)) {
                $update_data['user_id'] = $user_id;
            }

            $this->tasks_m->update($update_data, ['id' => $task_id]);
        }

        echo json_encode(['status' => 'success', 'message' => 'Comment added successfully']);
    }

    /**
     * AJAX endpoint for DataTables (optimized for large datasets)
     */
    public function ajax_list() {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        $request_data = $this->input->post();
        $result = $this->tasks_m->get_tasks_datatable($request_data);

        // Format data for DataTables
        $data = [];
        foreach ($result['data'] as $key => $item) {
            $row = [];

            // Task ID in TRG-{id} format with job_id_dashboard class
            $task_id_formatted = '<div class="mb-1 job_id_dashboard">TRG-<b>' . $item['id'] . '</b></div>';
            $row[] = $task_id_formatted;

            // Project title above task title (combined column)
            $project_title = $item['project_title'] ?? 'N/A';
            $project_formatted = '';
            if ($project_title !== 'N/A') {
                $project_formatted = '<span style="background-color:#efefef;font-size: 12px;border-left: 2px solid #999;color: blueviolet;padding-left: 5px">' .
                                   strtoupper(htmlspecialchars($project_title)) . '</span><br>';
            }

            // Title with task styling
            $title = $project_formatted .
                    '<div class="p-2 text-muted" style="font-size: 14px!important; font-weight: bold; background-color: rgba(250,247,250,0.81); border-radius: 2px;">' .
                    '<a href="' . base_url('app/tasks/view/' . $item['id']) . '" class="text-decoration-none text-dark" style="font-weight: bold;">' .
                    htmlspecialchars($item['title']) .
                    '</a>' .
                    '</div>';
            $row[] = $title;

            // Type and Priority (combined column)
            $details = '<div class="details-container">';
            
            // Type
            $details .= '<div class="mb-2">';
            $details .= '<small class="text-muted">Type:</small><br>';
            $type_badge = '';
            switch($item['task_type']) {
                case 'bug':
                    $type_badge = '<span class="badge badge-danger">Bug</span>';
                    break;
                case 'feature':
                    $type_badge = '<span class="badge badge-success">Feature</span>';
                    break;
                case 'improvement':
                    $type_badge = '<span class="badge badge-info">Improvement</span>';
                    break;
                default:
                    $type_badge = '<span class="badge badge-secondary">' . ucfirst($item['task_type']) . '</span>';
            }
            $details .= $type_badge;
            $details .= '</div>';

            // Priority
            $details .= '<div>';
            $details .= '<small class="text-muted">Priority:</small><br>';
            $priority_badge = '';
            switch($item['task_priority']) {
                case 'critical':
                    $priority_badge = '<span class="badge badge-danger">Critical</span>';
                    break;
                case 'high':
                    $priority_badge = '<span class="badge badge-warning">High</span>';
                    break;
                case 'medium':
                    $priority_badge = '<span class="badge badge-info">Medium</span>';
                    break;
                case 'low':
                    $priority_badge = '<span class="badge badge-secondary">Low</span>';
                    break;
                default:
                    $priority_badge = '<span class="badge badge-light">' . ucfirst($item['task_priority']) . '</span>';
            }
            $details .= $priority_badge;
            $details .= '</div>';

            $details .= '</div>';
            $row[] = $details;

            // Status badge
            $status_badge = '';
            switch($item['task_status']) {
                case 'pending':
                    $status_badge = '<span class="badge badge-warning" style="font-size: 13px; padding: 6px 12px;">PENDING</span>';
                    break;
                case 'assigned':
                    $status_badge = '<span class="badge badge-info" style="font-size: 13px; padding: 6px 12px;">ASSIGNED</span>';
                    break;
                case 'testing':
                    $status_badge = '<span class="badge badge-primary" style="font-size: 13px; padding: 6px 12px;">TESTING</span>';
                    break;
                case 'completed':
                    $status_badge = '<span class="badge badge-success" style="font-size: 13px; padding: 6px 12px;">COMPLETED</span>';
                    break;
                case 'on_hold':
                    $status_badge = '<span class="badge badge-secondary" style="font-size: 13px; padding: 6px 12px;">ON HOLD</span>';
                    break;
                default:
                    $status_badge = '<span class="badge badge-light" style="font-size: 13px; padding: 6px 12px;">UNKNOWN</span>';
            }
            $row[] = $status_badge;

            // Assigned To and Due Date (combined column)
            $assigned_due = '<div class="assigned-due-container">';
            
            // Assigned To
            $assigned_user = $item['assigned_user_name'] ?? null;
            if (!empty($assigned_user)) {
                $assigned_due .= '<div class="mb-2">';
                $assigned_due .= '<small class="text-muted">Assigned:</small><br>';
                $assigned_due .= '<span class="badge bg-light text-dark border">';
                $assigned_due .= '<i class="fas fa-user me-2"></i> ' . htmlspecialchars($assigned_user);
                $assigned_due .= '</span>';
                $assigned_due .= '</div>';
            } else {
                $assigned_due .= '<div class="mb-2">';
                $assigned_due .= '<small class="text-muted">Assigned:</small><br>';
                $assigned_due .= '<span class="text-muted fst-italic">Unassigned</span>';
                $assigned_due .= '</div>';
            }

            // Due Date
            $due_date = get_due_date($item['due_date']);
            $assigned_due .= '<div>';
            $assigned_due .= '<small class="text-muted">Due:</small><br>';
            $assigned_due .= '<span class="due_date">' . $due_date . '</span>';
            $assigned_due .= '</div>';

            $assigned_due .= '</div>';
            $row[] = $assigned_due;

            // Created By
            $created_by = $item['created_by_name'] ?? 'N/A';
            $row[] = htmlspecialchars($created_by);

            // Action buttons
            $actions = '<div class="btn-group-vertical" role="group">';

            $actions .= '<a href="' . base_url('app/tasks/view/' . $item['id']) . '" class="btn btn-outline-success btn-sm mb-1" title="View Details" style="width: 35px; height: 28px;">
                           <i class="fas fa-eye"></i>
                       </a>';

            // Show "Assign Task" button if task is unassigned
            if (empty($item['user_id']) || $item['user_id'] == 0) {
                $actions .= '<button onclick="show_ajax_modal(\'' . site_url('app/modal/popup/get/' . $item['id'] . '/?page_name=tasks/assign') . '\', \'Assign Task\')"
                                    class="btn btn-outline-info btn-sm mb-1" title="Assign Task" style="width: 35px; height: 28px;">
                                <i class="fas fa-user-plus"></i>
                            </button>';
            }

            if (has_permission('tasks/edit')) {
                $actions .= '<button onclick="show_large_modal(\'' . site_url('app/modal/popup/get/' . $item['id'] . '/?page_name=tasks/edit') . '\', \'Edit Task\')"
                                    class="btn btn-outline-primary btn-sm mb-1" title="Edit" style="width: 35px; height: 28px;">
                                <i class="fas fa-pencil-alt"></i>
                            </button>';
            }

            if (has_permission('tasks/delete')) {
                $actions .= '<button onclick="confirm_modal(\'' . base_url("app/tasks/delete/{$item['id']}/") . '\')"
                                    class="btn btn-outline-danger btn-sm mb-1" title="Delete" style="width: 35px; height: 28px;">
                                <i class="fas fa-trash"></i>
                            </button>';
            }

            $actions .= '</div>';
            $row[] = $actions;

            $data[] = $row;
        }

        $response = [
            "draw" => intval($request_data['draw']),
            "recordsTotal" => intval($result['recordsTotal']),
            "recordsFiltered" => intval($result['recordsFiltered']),
            "data" => $data
        ];

        echo json_encode($response);
    }

}



