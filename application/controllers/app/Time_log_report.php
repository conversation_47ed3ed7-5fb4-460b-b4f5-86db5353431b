<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Time_log_report extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('users_m');
        $this->load->model('time_log_m');
        $this->load->model('time_log_type_m');
        $this->load->model('attendance_m');
        $this->load->model('tasks_m');
        $this->load->model('work_history_m');
    }

    public function daily_report() {
        if (has_permission('time_log/daily_log') || get_user_id() == 57){
            if (empty($this->input->get('log_date'))){
                redirect('app/time_log_report/daily_report/?log_date='.date('Y-m-d'));
            }
            $this->data['log_date']     = $this->input->get('log_date');
            $this->data['users']        = $this->users_m->get([
                'employee_code!=' => '', 'employee_status' => 1, 'id!=' => 4
            ])->result_array();
    
            $this->data['time_log']         = $this->time_log_m->get_time_log_data($this->input->get('log_date'));
            $this->data['attendance_data']  = $this->attendance_m->get_attendance_data($this->input->get('log_date'));
            $this->data['time_log_type']    = $this->time_log_type_m->get(['status' => 1])->result_array();
            
            $this->data['page_title']   = 'Daily Time Report';
            $this->data['page_name']    = 'time_log_report/daily_report';
            $this->load->view('app/index', $this->data);
        }else{
            redirect('app/dashboard/index/');
        }
        
    }

    public function overview_report(){
        $start_date = $this->input->get('start_date');
        $end_date = $this->input->get('end_date');

        if (empty($start_date) || empty($end_date)){
            $start_date = date('Y-m-01');
            $end_date = date('Y-m-t');
            redirect("app/time_log_report/overview_report/?start_date={$start_date}&end_date={$end_date}&working_days=26");
        }



        $report = $this->time_log_m->generate_overview_report($start_date, $end_date);

        if($this->input->get('save_report') == 'true'){
            $this->save_report($report);
        }

        $this->data['pdf_url']   = $this->get_overview_report_pdf($start_date, $end_date);
        $this->data['users']   = $report;
        $this->data['page_title']   = 'Overview Report';
        $this->data['page_name']    = 'time_log_report/overview_report';
        $this->load->view('app/index', $this->data);

    }
    
    private function save_report($report){
        $month = '07-2024';
        $insert_data = [
                'month' => $month,
                'data' => json_encode($report),
                'created_at' => date('Y-m-d H:i:s')
            ];
        $this->db->insert('employee_performance', $insert_data);
    }

    public function get_overview_report_pdf($start_date, $end_date){
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_left' => 3,
            'margin_right' => 3,
            'margin_top' => 3,
            'margin_bottom' => 3,
        ]);

        $data['start_date'] = $start_date;
        $data['end_date'] = $end_date;

        $report = $this->time_log_m->generate_overview_report($start_date, $end_date);

//        echo json_encode($report);
        $data['users']   = $report;

        $mpdf->AddPage();
        $html = $this->load->view('app/time_log_report/overview_report_pdf', $data, true);
        $mpdf->WriteHTML($html);
        $start_date = DateTime::createFromFormat('Y-m-d', $start_date)->format('M-Y');
        $pdf_url = "uploads/time_log_report/Time_log_Report_{$start_date}.pdf";

        $mpdf->Output($pdf_url,'F');
        if ($_GET['view']){
            $mpdf->Output();
        }
        return base_url($pdf_url);
    }


    // generate overview report
    public function generate_overview_report($users, $time_log_data, $attendance_data){
        foreach($users as $key => $user){
            $users[$key]['attendance_overview'] = [
                'P' => 0,
                'A' => 0,
                'WH' => 0,
                'OF' => 0,
                'OD' => 0,
                'HD' => 0,
                'late_coming' => 0,
                'early_going' => 0,
            ];

            $users[$key]['time_log_overview'] = [
                'work_duration_seconds' => 0,
                'break_duration_seconds' => 0,
                'office_duration_seconds' => 0,
                'work_duration' => '00:00:00',
                'break_duration' => '00:00:00',
                'office_duration' => '00:00:00',
            ];


            // attendance status
            $user_attendance_data = $attendance_data[$user['id']];
            if (is_array($user_attendance_data)){

                foreach ($user_attendance_data as $date => $attendance){
                    if ($attendance['attendance'] == 'P'){
                        $users[$key]['attendance_overview']['P']++;
                    }elseif ($attendance['attendance'] == 'A'){
                        $users[$key]['attendance_overview']['A']++;
                    }elseif ($attendance['attendance'] == 'WH'){
                        $users[$key]['attendance_overview']['WH']++;
                    }elseif ($attendance['attendance'] == 'OF'){
                        $users[$key]['attendance_overview']['OF']++;
                    }elseif ($attendance['attendance'] == 'OD'){
                        $users[$key]['attendance_overview']['OD']++;
                    }elseif ($attendance['attendance'] == 'HD'){
                        $users[$key]['attendance_overview']['HD']++;
                    }
                }
            }

            // time log status
            $user_time_log_data = $time_log_data[$user['id']];
            if(is_array($user_time_log_data)){
                foreach ($user_time_log_data as $date => $time_log){
                    $users[$key]['time_log_overview']['work_duration_seconds'] += $time_log['work_duration_seconds'];
                    $users[$key]['time_log_overview']['break_duration_seconds'] += $time_log['break_duration_seconds'];
                    $users[$key]['time_log_overview']['office_duration_seconds'] += $time_log['office_duration_seconds'];
                    if (is_late_coming($time_log)){
                        $users[$key]['attendance_overview']['late_coming']++;
                    }

                    if (is_early_going($time_log)){
                        $users[$key]['attendance_overview']['early_going']++;
                    }
                }
            }
            $users[$key]['time_log_overview']['work_duration'] = get_time_from_seconds($users[$key]['time_log_overview']['work_duration_seconds']);
            $users[$key]['time_log_overview']['break_duration'] = get_time_from_seconds($users[$key]['time_log_overview']['break_duration_seconds']);
            $users[$key]['time_log_overview']['office_duration'] = get_time_from_seconds($users[$key]['time_log_overview']['office_duration_seconds']);



            // calculate employee points
            $employee_points = 0;
            $employee_points = $users[$key]['time_log_overview']['work_duration_seconds']/25200;

            $employee_points = $employee_points - ($users[$key]['attendance_overview']['late_coming']*0.142857);
            $employee_points = $employee_points - ($users[$key]['attendance_overview']['early_going']*0.142857);
            $employee_points = $employee_points - ($users[$key]['attendance_overview']['WH']*0.5);
            $users[$key]['employee_points'] = number_format($employee_points, 4);

        }
        usort($users, function($a, $b) {
            return $b['time_log_overview']['work_duration_seconds'] <=> $a['time_log_overview']['work_duration_seconds'];
        });
        return $users;
    }
    
}