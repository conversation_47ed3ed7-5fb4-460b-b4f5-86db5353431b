<?php
/*
 * Copyright (c) 2023.
 * PRODUCT: ECOPEN
 * AUTHOR: TROGON MEDIA PVT LTD
 * WEBSITE: http://trogonmedia.com
 */

class Projects extends App_Controller{
    public function __construct () {
        parent::__construct();
        $this->load->model('projects_m');
        $this->load->model('project_type_m');
        $this->load->model('clients_m');
        $this->load->model('users_m');
        $this->load->model('teams_m');
    }

    public function index() {
        // Load only testers (role_id = 5) with active status (employee_status = 1)
        $this->db->where('role_id', 5);
        $this->db->where('employee_status', 1);
        $testers = $this->users_m->get()->result_array();
        $this->data['testers'] = array_column($testers, 'name', 'id');
        
        // Load all users for tester status check
        $all_users = $this->users_m->get()->result_array();
        $this->data['all_users'] = array_column($all_users, 'employee_status', 'id');
        
        $this->data['client_list']      = array_column($this->clients_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['project_type']     = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        
        // Get filter parameters
        $filters = [
            'is_delivered' => $this->input->get('is_delivered'),
            'tester_id' => $this->input->get('tester_id'),
            'task_status' => $this->input->get('task_status')
        ];
        
        // Get all projects for status count (unfiltered)
        $all_projects = $this->projects_m->get_project_list([]);
        $this->data['status_count'] = $this->projects_m->get_status_count($all_projects);
        
        // Get filtered projects for display
        $this->data['list_items'] = $this->projects_m->get_project_list($filters);
        $this->data['teams'] = array_column($this->teams_m->get()->result_array(), 'title', 'id');
        
        // Get filter parameters for view
        $this->data['filters'] = $filters;
        
        $this->data['page_title']   = 'Projects';
        $this->data['page_name']    = 'projects/index';
        $this->load->view('app/index', $this->data);
    }
    public function new_projects() {
        $users = $this->users_m->get()->result_array();
        $this->data['users'] = array_column($users, 'name', 'id');

        $this->data['client_list']      = array_column($this->clients_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['project_type']     = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');

        $this->db->where('is_delivered', 0);
        $this->db->or_where('is_delivered', null);
        $this->data['list_items']       = $this->projects_m->get_project_list();
        $this->data['status_count']     = $this->projects_m->get_status_count($this->data['list_items']);
        $this->data['teams']        = array_column($this->teams_m->get()->result_array(), 'title', 'id');

        $this->data['page_title']   = 'Projects';
        $this->data['page_name']    = 'projects/new_projects';
        $this->load->view('app/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $project_teams = $this->input->post('project_teams');
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'tester_id' => $this->input->post('tester_id'),
                'project_lead_id' => $this->input->post('project_lead_id'),
                'project_teams' => json_encode($project_teams),
                'client_id' => $this->input->post('client_id'),
                'project_type' => $this->input->post('project_type'),
                'is_delivered' => $this->input->post('is_delivered') == 1 ? 1 : 0,
                'phone' => $this->input->post('phone'),
                'email' => $this->input->post('email'),
                'url_web' => $this->input->post('url_web'),
                'url_android' => $this->input->post('url_android'),
                'url_ios' => $this->input->post('url_ios'),
                'start_date' => set_input_value($this->input->post('start_date')),
                'project_date' => set_input_value($this->input->post('project_date')),
                'project_logo' => $this->input->post('project_logo'),
                'created_by' => get_user_id(),
                'updated_by' => get_user_id(),
                'created_on' => date('Y-m-d H:i:s'),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->projects_m->insert($data);
            set_alert('message_success', 'Project Added Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function edit($item_id){
        if ($this->input->post()){
            $project_teams = $this->input->post('project_teams');
            $data = [
                'title' => $this->input->post('title'),
                'description' => $this->input->post('description'),
                'tester_id' => $this->input->post('tester_id'),
                'project_lead_id' => $this->input->post('project_lead_id'),
                'project_teams' => json_encode($project_teams),
                'client_id' => $this->input->post('client_id'),
                'project_type' => $this->input->post('project_type'),
                'is_delivered' => $this->input->post('is_delivered') == 1 ? 1 : 0,
                'phone' => $this->input->post('phone'),
                'email' => $this->input->post('email'),
                'url_web' => $this->input->post('url_web'),
                'url_android' => $this->input->post('url_android'),
                'url_ios' => $this->input->post('url_ios'),
                'start_date' => set_input_value($this->input->post('start_date')),
                'project_date' => set_input_value($this->input->post('project_date')),
                'project_logo' => $this->input->post('project_logo'),
                'updated_by' => get_user_id(),
                'updated_on' => date('Y-m-d H:i:s'),
            ];
            $this->projects_m->update($data, ['id' => $item_id]);
            set_alert('message_success', 'Project Updated Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    public function delete($item_id){
        if ($item_id > 0){
            $this->projects_m->delete(['id' => $item_id]);
            set_alert('message_success', 'Project Deleted Successfully!');
        }
        redirect($_SERVER['HTTP_REFERER']);
    }

    /**
     * View project details
     */
    public function view($item_id) {
        // Get project details with task statistics
        $projects = $this->projects_m->get_project_list();
        $project = null;

        foreach ($projects as $p) {
            if ($p['id'] == $item_id) {
                $project = $p;
                break;
            }
        }

        if (!$project) {
            show_404();
        }

        // Load additional models for project details
        $this->load->model('tasks_m');
        $this->load->model('work_history_m');

        // Get project tasks with user details
        $this->db->select('tasks.*, users.name as user_name, creator.name as creator_name');
        $this->db->from('tasks');
        $this->db->join('users', 'users.id = tasks.user_id', 'left');
        $this->db->join('users creator', 'creator.id = tasks.created_by', 'left');
        $this->db->where('tasks.project_id', $item_id);
        $this->db->order_by('tasks.created_on', 'DESC');
        $this->data['project_tasks'] = $this->db->get()->result_array();

        // Get project tickets with user details
        $this->db->select('tickets.*, users.name as user_name');
        $this->db->from('tickets');
        $this->db->join('users', 'users.id = tickets.user_id', 'left');
        $this->db->where('tickets.project_id', $item_id);
        $this->db->order_by('tickets.created_on', 'DESC');
        $this->data['project_tickets'] = $this->db->get()->result_array();

        // Get employee workload distribution
        $this->data['employee_workload'] = $this->_get_employee_workload($item_id);

        // Get time tracking data for different periods
        $this->data['time_tracking'] = $this->_get_time_tracking_data($item_id);

        // Get ticket summary data
        $this->data['ticket_summary'] = $this->_get_ticket_summary($item_id);

        // Get users, clients, project types for display
        $users = $this->users_m->get()->result_array();
        $this->data['users'] = array_column($users, 'name', 'id');
        $this->data['client_list'] = array_column($this->clients_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['project_type'] = array_column($this->project_type_m->get(null, ['id', 'title'])->result_array(), 'title', 'id');
        $this->data['teams'] = array_column($this->teams_m->get()->result_array(), 'title', 'id');

        $this->data['project'] = $project;
        $this->data['page_title'] = 'Project Details - ' . $project['title'];
        $this->data['page_name'] = 'projects/view';
        $this->load->view('app/index', $this->data);
    }

    /**
     * Get employee workload distribution
     */
    private function _get_employee_workload($project_id) {
        $this->db->select('users.name, users.id, COUNT(tasks.id) as task_count,
                          SUM(CASE WHEN tasks.task_status = "completed" THEN 1 ELSE 0 END) as completed_tasks,
                          SUM(CASE WHEN tasks.task_status = "assigned" THEN 1 ELSE 0 END) as assigned_tasks,
                          SUM(CASE WHEN tasks.task_status = "pending" THEN 1 ELSE 0 END) as pending_tasks');
        $this->db->from('tasks');
        $this->db->join('users', 'users.id = tasks.user_id', 'left');
        $this->db->where('tasks.project_id', $project_id);
        $this->db->where('tasks.task_status !=', 'on_hold');
        $this->db->group_by('users.id, users.name');
        $this->db->order_by('task_count', 'DESC');

        return $this->db->get()->result_array();
    }

    /**
     * Get time tracking data for different periods
     */
    private function _get_time_tracking_data($project_id) {
        $periods = [
            'overall' => '',
            'last_7_days' => 'AND DATE(wh.start_time) >= DATE_SUB(NOW(), INTERVAL 7 DAY)',
            'last_15_days' => 'AND DATE(wh.start_time) >= DATE_SUB(NOW(), INTERVAL 15 DAY)',
            'last_30_days' => 'AND DATE(wh.start_time) >= DATE_SUB(NOW(), INTERVAL 30 DAY)'
        ];

        $time_data = [];

        foreach ($periods as $period => $date_condition) {
            // Get total time worked on project tasks from work_history table
            $sql = "SELECT
                        SUM(wh.duration * 60) as total_duration,
                        COUNT(DISTINCT wh.user_id) as unique_users,
                        COUNT(wh.id) as total_entries
                    FROM work_history wh
                    JOIN tasks t ON t.id = wh.item_id
                    WHERE wh.item_type = 'task'
                    AND t.project_id = ? 
                    AND wh.duration IS NOT NULL 
                    AND wh.duration > 0
                    AND wh.duration < 360
                    $date_condition";

            $result = $this->db->query($sql, [$project_id])->row_array();

            // Get user-wise time breakdown
            $user_sql = "SELECT
                            u.name,
                            u.id as user_id,
                            SUM(wh.duration * 60) as user_duration,
                            COUNT(wh.id) as user_entries
                        FROM work_history wh
                        JOIN tasks t ON t.id = wh.item_id
                        JOIN users u ON u.id = wh.user_id
                        WHERE wh.item_type = 'task'
                        AND t.project_id = ? 
                        AND wh.duration IS NOT NULL 
                        AND wh.duration > 0
                        AND wh.duration < 360
                        $date_condition
                        GROUP BY u.id, u.name
                        ORDER BY user_duration DESC";

            $user_breakdown = $this->db->query($user_sql, [$project_id])->result_array();

            $time_data[$period] = [
                'total_duration' => $result['total_duration'] ?? 0,
                'unique_users' => $result['unique_users'] ?? 0,
                'total_entries' => $result['total_entries'] ?? 0,
                'user_breakdown' => $user_breakdown
            ];
        }

        return $time_data;
    }

    /**
     * Get ticket summary data for different periods
     */
    private function _get_ticket_summary($project_id) {
        $summary = [
            'last_7_days' => $this->_get_ticket_period_summary($project_id, 7),
            'last_30_days' => $this->_get_ticket_period_summary($project_id, 30),
            'overall' => $this->_get_ticket_period_summary($project_id, null)
        ];

        return $summary;
    }

    /**
     * Get ticket summary for a specific period
     */
    private function _get_ticket_period_summary($project_id, $days = null) {
        $this->db->select('
            COUNT(*) as total,
            SUM(CASE WHEN status = "closed" THEN 1 ELSE 0 END) as resolved,
            AVG(CASE WHEN status = "closed" AND close_date IS NOT NULL AND ticket_date IS NOT NULL 
                THEN TIMESTAMPDIFF(HOUR, ticket_date, close_date) 
                ELSE NULL END) as avg_resolution_hours
        ');
        $this->db->from('tickets');
        $this->db->where('project_id', $project_id);
        
        if ($days) {
            $this->db->where('ticket_date >=', date('Y-m-d H:i:s', strtotime("-$days days")));
        }

        $result = $this->db->get()->row_array();

        $avg_resolution_time = '';
        if ($result['avg_resolution_hours'] && $result['avg_resolution_hours'] > 0) {
            $hours = floor($result['avg_resolution_hours']);
            $minutes = round(($result['avg_resolution_hours'] - $hours) * 60);
            
            if ($hours > 0) {
                $avg_resolution_time = $hours . 'h';
                if ($minutes > 0) {
                    $avg_resolution_time .= ' ' . $minutes . 'm';
                }
            } else {
                $avg_resolution_time = $minutes . 'm';
            }
        }

        return [
            'total' => (int)$result['total'],
            'resolved' => (int)$result['resolved'],
            'avg_resolution_time' => $avg_resolution_time
        ];
    }
}
