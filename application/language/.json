{"add_class": "Add class", "add_client": "Add client", "update_classes": "Update classes", "add_project": "Add project", "update_project": "Update project", "add_users": "Add users", "update_users": "Update users", "view_user": "View user", "add_task": "Add task", "update_task": "Update task", "add_todo_category": "Add todo category", "update_todo_category": "Update todo category", "add_todo": "Add todo", "update_todo": "Update todo", "add_tasks": "Add tasks", "assign_task": "Assign task", "update_task_status": "Update task status", "update_todo_status": "Update todo status", "reschedule_task": "Reschedule task", "add_products": "Add products", "update_products": "Update products", "task_details": "Task details", "todo_details": "Todo details", "morning_in": "Morning in", "morning_tea_out": "Morning tea out", "morning_tea_in": "Morning tea in", "lunch_out": "Lunch out", "lunch_in": "Lunch in", "evening_tea_out": "Evening tea out", "evening_tea_in": "Evening tea in", "evening_out": "Evening out", "un_assign": "Un assign", "re_assign_task": "Re assign task", "task_list": "Task list", "assigned_task_list": "Assigned task list", "time_log_report": "Time log report", "update_task_content": "Update task content", "task_history": "Task history", "morning": "Morning", "evening": "Evening", "reset_password": "Reset password", "quick_status_edit": "Quick status edit", "test_task": "Test task", "change_status": "Change status", "": "", "add_document": "Add document", "view_document": "View document", "update_document": "Update document", "add_work_schedule": "Add work schedule", "update_work_schedule": "Update work schedule", "add_project_schedule": "Add project schedule", "update_project_schedule": "Update project schedule", "task_details_-_": "Task details - ", "add_team": "Add team", "add_project_assignment": "Add project assignment", "bulk_assign_projects": "Bulk assign projects", "candidate_answers:_ameer_suhail": "Candidate answers: <PERSON><PERSON><PERSON>", "candidate_answers:_test": "Candidate answers: test", "candidate_answers:_anjana.p.s_": "Candidate answers: anjana.p.s ", "candidate_answers:_adeeb_c": "Candidate answers: adeeb c", "candidate_answers:_anjana": "Candidate answers: an<PERSON>", "candidate_answers:_sreekanth_s": "Candidate answers: s<PERSON><PERSON><PERSON> s", "candidate_answers:_shuhaib_": "Candidate answers: <PERSON><PERSON><PERSON><PERSON> ", "candidate_answers:_ansaf_ct": "Candidate answers: ansaf ct", "candidate_answers:_nima_siraj": "Candidate answers: nima siraj", "candidate_answers:_nikhil_suresh_": "Candidate answers: ni<PERSON><PERSON> <PERSON><PERSON> ", "candidate_answers:_ezhuvanthanam_anju_antony_": "Candidate answers: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> anju antony ", "candidate_answers:_akshay_k": "Candidate answers: aks<PERSON> k", "candidate_answers:_muhammed_udaif_p": "Candidate answers: muh<PERSON><PERSON> u<PERSON> p", "candidate_answers:_fizza_anwar_sadath": "Candidate answers: <PERSON><PERSON> <PERSON><PERSON> sadath", "candidate_answers:_fathimath_sahala": "Candidate answers: <PERSON><PERSON><PERSON>", "candidate_answers:_akhila_sivaraman": "Candidate answers: <PERSON><PERSON><PERSON>", "candidate_answers:_aswin_kumar_r": "Candidate answers: <PERSON><PERSON> kumar r", "candidate_answers:_renjithraj_r_s": "Candidate answers: re<PERSON><PERSON><PERSON> r s", "candidate_answers:_abhay_vinod": "Candidate answers: a<PERSON><PERSON> vinod", "candidate_answers:_tony_sunny": "Candidate answers: tony sunny", "candidate_answers:_rahan_rahman_a": "Candidate answers: rahan rahman a", "candidate_answers:_muhammed_adhil_pt": "Candidate answers: muh<PERSON><PERSON> pt", "candidate_answers:_ayisha_selin_e": "Candidate answers: <PERSON><PERSON><PERSON> e"}